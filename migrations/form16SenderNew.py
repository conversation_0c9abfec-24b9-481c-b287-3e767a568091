import boto3
import os
import traceback
import A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from gql import gql
from email.message import EmailMessage
import mimetypes

# AWS SES Configuration
ses_client = boto3.client('ses', region_name='us-east-2')
sender_email = "Fianance Communications <<EMAIL>>"

def create_graphql_client():
    return AppSyncHelper.create_graphql_client()

def list_employees():
    """Fetch all active employees from the database"""
    query = gql(
        """
        query ListEmployees($nextToken: String) {
            listEmployees(nextToken: $nextToken, filter: {hidden_profile: {ne: true}, active: {ne: false}}) {
                items {
                    email
                    employee_id
                    first_name
                    last_name
                    pan_card
                }
                nextToken
            }
        }
        """
    )
    
    items = []
    nextToken = None
    gql_client = create_graphql_client()

    while True:
        params = {}
        if nextToken:
            params["nextToken"] = nextToken
        response = gql_client.execute(query, variable_values=params)
        items += response["listEmployees"]["items"]
        nextToken = response["listEmployees"]["nextToken"]
        if not nextToken:
            break
    return items

def find_form16_file(directory, pan_number):
    """Find Form16 PDF file matching the PAN number"""
    for root, _, files in os.walk(directory):
        for file in files:
            if pan_number in file:
                return os.path.join(root, file)
    return None

def send_email_with_attachment(to_address, subject, body_html, filename):
    """Send email with Form16 PDF attachment"""
    try:
        msg = EmailMessage()
        msg['Subject'] = subject
        msg['From'] = sender_email
        msg['To'] = to_address
        msg.set_content("This is a multipart message in MIME format.")
        msg.add_alternative(body_html, subtype='html')

        # Attach the file
        with open(filename, 'rb') as file:
            file_content = file.read()
            file_type, _ = mimetypes.guess_type(filename)
            file_type = file_type or 'application/octet-stream'
            maintype, subtype = file_type.split('/', 1)
            msg.add_attachment(file_content, maintype=maintype, subtype=subtype, filename=os.path.basename(filename))

        response = ses_client.send_raw_email(
            Source=sender_email,
            Destinations=[to_address],
            RawMessage={'Data': msg.as_bytes()}
        )
        print(f"Email sent to {to_address}! Message ID: {response['MessageId']}")
        return True
    except Exception as e:
        print(f"Failed to send email to {to_address}. Error: {e}")
        traceback.print_exc()
        return False

def main():
    # Directory containing Form16 PDFs
    form16_directory = '/Users/<USER>/Downloads/tmp/form 16 24-25/AHMA22684E_202526_16_02062025_132216/'
    
    # Get all employees
    employees = list_employees()
    print(f"Found {len(employees)} employees")

    # Process each employee
    for employee in employees:
        if not employee.get('pan_card'):
            print(f"No PAN card found for employee {employee.get('email')}")
            continue

        # Find matching Form16 file
        form16_file = find_form16_file(form16_directory, employee['pan_card'])
        
        if form16_file:
            print(f"Found Form16 for {employee['email']}")
            
            # Prepare email content
            body_html = f"""
            <html>
                <body>
                    <p>Dear {employee['first_name']},</p>

                    <p>We are pleased to inform you that your Form 16 is attached to this email.</p>

                    <p>If you have any questions or need further assistance, please do not hesitate to contact Mansi Mehta.</p>

                    <p>Best regards,</p>
                    <p>The York IE Team</p>
                </body>
            </html>
            """

            # Send email with attachment
            if send_email_with_attachment('<EMAIL>', "Form 16", body_html, form16_file):
                # Delete the file after successful sending
                try:
                    os.remove(form16_file)
                    print(f"File {form16_file} deleted successfully")
                except Exception as e:
                    print(f"Error deleting file {form16_file}: {e}")
        else:
            print(f"No Form16 found for employee {employee['email']} with PAN {employee['pan_card']}")
        break

if __name__ == "__main__":
    main()
