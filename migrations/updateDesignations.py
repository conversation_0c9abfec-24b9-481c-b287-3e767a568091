import datetime
import json
import AppSyncHelper
from gql import gql
import csv


gql_client = AppSyncHelper.create_graphql_client()

def fetch_all_designations():
    query = gql("""
        query ListAllDesignations($nextToken: String) {
            listDesignations(limit: 999, nextToken: $nextToken) {
                items {
                    id
                    name
                    level
                    overall_level
                }
                nextToken
            }
        }
    """)
    all_designations = []
    next_token = None
    while True:
        variables = {"nextToken": next_token}
        resp = gql_client.execute(query, variable_values=variables)
        items = resp.get("listDesignations", {}).get("items", [])
        all_designations.extend(items)
        next_token = resp.get("listDesignations", {}).get("nextToken")
        if not next_token:
            break
    print(f"Fetched {len(all_designations)} designations from DB")
    return all_designations

def find_designation_id(designations, title, level, overall_level):
    for d in designations:
        if (
            d.get("name", "").strip() == title
            and str(d.get("level", "")).strip() == str(level).strip()
            and str(d.get("overall_level", "")).strip() == str(overall_level).strip()
        ):
            return d["id"]
    return None

def update_employee_designations(csv_filename):
    designations = fetch_all_designations()
    not_found = []
    with open(csv_filename, newline='') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            email = row.get('Email', '').strip()
            new_title = row.get('New Title', '').strip()
            new_level = row.get('New level', '').strip()
            new_overall_level = row.get('New Overall Level', '').strip()

            if not (email and new_title and new_level and new_overall_level):
                print(f"Skipping row due to missing data: {row}")
                continue

            designation_id = find_designation_id(designations, new_title, new_level, new_overall_level)
            if designation_id:
                mutation = gql("""
                    mutation UpdateEmployee($email: String!, $employeeTitleId: ID!) {
                        updateEmployee(input: {email: $email, employeeTitleId: $employeeTitleId}) {
                            email
                            employeeTitleId
                        }
                    }
                """)
                variables = {
                    "email": email,
                    "employeeTitleId": designation_id
                }
                resp = gql_client.execute(mutation, variable_values=variables)
                print(f"Updated {email} with designation {designation_id}, resp {resp}")
            else:
                print(f"Designation not found for {email}: {new_title}, {new_level}, {new_overall_level}")
                not_found.append({
                    "email": email,
                    "title": new_title,
                    "level": new_level,
                    "overall_level": new_overall_level
                })
    if not_found:
        print("\n--- Designations not found for the following employees ---")
        for nf in not_found:
            print(nf)

if __name__ == "__main__":
    update_employee_designations('current_teams.csv')