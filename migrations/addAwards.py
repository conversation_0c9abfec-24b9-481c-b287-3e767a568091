import csv
import json
import AppSyncHelper
from gql import gql
from datetime import datetime

gql_client = AppSyncHelper.create_graphql_client()

# Award type to S3Key and AwardType mapping
AWARD_MAP = {
    'Kudos': {'S3Key': 'Kudos.svg', 'type': 'BADGE'},
    'Rising Star': {'S3Key': 'RisingStar.svg', 'type': 'BADGE'},
    'York Star': {'S3Key': 'YorkStar.svg', 'type': 'BADGE'},
}

# GraphQL mutation for EmployeeCertificates
mutation = gql('''
    mutation CreateEmployeeCertificates($input: CreateEmployeeCertificatesInput!) {
        createEmployeeCertificates(input: $input) {
            id
            employeeCertificationId
            title
            S3Key
            type
            provided_date
        }
    }
''')

def process_awards_csv(csv_path):
    with open(csv_path, newline='') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            # Skip if type is not "badge"
            if row['type'].strip().lower() != 'badge':
                continue
                
            award_type = row['Awards Title'].strip()
            email = row['Employee ID'].strip()
            name = row['Employee Name'].strip()
            quarter = row['Quarter'].strip()
            year = row['Year '].strip()
            provided_date = row['date'].strip()
            
            if award_type not in AWARD_MAP:
                continue  # Only process specified awards
            s3key = AWARD_MAP[award_type]['S3Key']
            award_enum = AWARD_MAP[award_type]['type']
            title = f"{award_type} - {quarter} - {year}"
            input_data = {
                'employeeCertificationId': email,
                'S3Key': s3key,
                'title': title,
                'provider': 'Empty',
                'provided_date': provided_date,
                'type': award_enum,
                'url': '',
            }
            print(f"Inserting certificate for {name} ({email}): {title}, input: {json.dumps(input_data, indent=2)}")
            try:
                resp = gql_client.execute(mutation, variable_values={'input': input_data})
                print(f"Success: {resp}")
            except Exception as e:
                print(f"Failed for {email}: {e}")

if __name__ == "__main__":
    process_awards_csv('awards.csv')
