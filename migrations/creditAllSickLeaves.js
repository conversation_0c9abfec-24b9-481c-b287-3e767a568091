const AWS = require("aws-sdk");
const { v4: uuidv4 } = require("uuid");

// Initialize DynamoDB DocumentClient
AWS.config.update({ region: "us-east-2" }); // Update to your region
const dynamoDb = new AWS.DynamoDB.DocumentClient();
const employeeTable = "Employee-n2bv37wxwne4fb6obc2e36aqgy-prod";
const leaveTable = "Leave-n2bv37wxwne4fb6obc2e36aqgy-prod";

// Function to generate dummy Notice data
function createLeave(count, email) {
  return {
    id: uuidv4(),
    type: "SICK",
    start_time: "2025-06-01T00:00:00+05:30",
    end_time: "2025-06-01T23:59:59+05:30",
    adjustment_type: "CREDIT",
    count: count,
    description: "Sick Leave Credit for FY2025-26",
    leave_length: "FULL_DAY",
    employeeLeavesId: email,
    leaveApproved_byId: "<EMAIL>",
    __typename: "Leave",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
}

// 1. list all employeess with sick_leave_balance > 0 (paginate it as its have lots of data)
async function processEmployees() {
  let lastEvaluatedKey = null;

  do {
    // Scan the Employee table with filter for sick_leave_balance > 0
    const params = {
      TableName: employeeTable,
      FilterExpression: "active <> :false AND hidden_profile <> :true",
      ExpressionAttributeValues: {
      ":false": false,
      ":true": true,
      },
      ExclusiveStartKey: lastEvaluatedKey,
    };

    const result = await dynamoDb.scan(params).promise();
    const employees = result.Items;

    for (const employee of employees) {
      const leaveEntry = createLeave(
        6,
        employee.email
      );

      // Insert leave entry into Leave table
      const leaveParams = {
        TableName: leaveTable,
        Item: leaveEntry,
      };

      await dynamoDb.put(leaveParams).promise();
      console.log(`Created leave entry for employee: ${employee.email}`);
    }

    lastEvaluatedKey = result.LastEvaluatedKey;
  } while (lastEvaluatedKey);
}

processEmployees().catch((error) => {
  console.error("Error processing employees:", error);
});