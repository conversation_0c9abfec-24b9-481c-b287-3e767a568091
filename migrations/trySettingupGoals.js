const AWS = require("aws-sdk");
const AWSAppSyncClient = require("aws-appsync").default;
const { AUTH_TYPE } = require("aws-appsync");
const gql = require("graphql-tag");
const dotenv = require("dotenv");

dotenv.config();

const region = process.env.REGION || "us-east-2";
AWS.config.update({ region: region });
const appsyncUrl =
  process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT ||
  "https://7farl5sfnvajvd47qtrnj3e6fa.appsync-api.us-east-2.amazonaws.com/graphql";

const appsyncClient = new AWSAppSyncClient({
  url: appsyncUrl,
  region: region,
  auth: {
    type: AUTH_TYPE.AWS_IAM,
    credentials: AWS.config.credentials,
  },
  disableOffline: true,
});

const getEmployeeByEmail = /* GraphQL */ `
  query GetEmployee($email: String!) {
    getEmployee(email: $email) {
      email
      introduction
      skills {
        items {
          skill {
            name
          }
        }
      }
      SME {
        items {
          sME {
            name
          }
        }
      }
      career_start_date
    }
  }
`;

const listMBOGoalV2s = /* GraphQL */ `
  query ListMBOGoalV2s($filter: ModelMBOGoalV2FilterInput, $limit: Int, $nextToken: String) {
    listMBOGoalV2s(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        role
        experienceRange
        category
        goal
        purpose
        note
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

async function listAllMBOGoalV2s() {
  let allGoals = [];
  let nextToken = null;
  do {
    const response = await appsyncClient.query({
      query: gql(listMBOGoalV2s),
      variables: { limit: 100, nextToken },
      fetchPolicy: "no-cache"
    });
    const { items, nextToken: newNextToken } = response.data.listMBOGoalV2s;
    allGoals = allGoals.concat(items);
    nextToken = newNextToken;
  } while (nextToken);
  return allGoals;
}

async function trySettingupGoals(email) {
  try {
    const response = await appsyncClient.query({
      query: gql(getEmployeeByEmail),
      variables: { email },
      fetchPolicy: "no-cache"
    });

    const employee = response.data.getEmployee;
    
    console.log('Employee Information:');
    console.log('Email:', employee.email);
    console.log('Introduction:', employee.introduction);
    console.log('Skills:', employee.skills.items.map(item => item.skill.name));
    console.log('SME:', employee.SME.items.map(item => item.sME.name));
    console.log('Career Start Date:', employee.career_start_date);

    // List all MBOGoalV2s
    const mboGoals = await listAllMBOGoalV2s();
    console.log(`\nAll MBOGoalV2s (${mboGoals.length}):`);
    // mboGoals.forEach(goal => {
    //   console.log(goal);
    // });
    
    // Generate personalized MBO goals
    console.log('\n=== Generating Personalized MBO Goals ===');
    const { generateMBOGoals } = require('./generateMBOGoals');
    const personalizedGoals = await generateMBOGoals(email);
    
    return { employee, mboGoals, personalizedGoals };
  } catch (error) {
    console.error('Error fetching employee data:', error);
    throw error;
  }
}

trySettingupGoals("<EMAIL>");
module.exports = { trySettingupGoals };
