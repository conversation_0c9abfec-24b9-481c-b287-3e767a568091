const AWSAppSyncClient = require('aws-appsync').default;
const gql = require('graphql-tag');
require('isomorphic-fetch');
const AWS = require('aws-sdk');
const axios = require('axios');

const appSyncClient = new AWSAppSyncClient({
  url: process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT,
  region: process.env.REGION,
  auth: {
    type: 'AWS_IAM',
    credentials: AWS.config.credentials,
  },
  disableOffline: true,
});

const listJiraEventsQuery = gql`
  query ListJiraEvents($limit: Int, $nextToken: String) {
    listJiraEvents(limit: $limit, nextToken: $nextToken) {
      items {
        issueKey
        status
        priority
        severity
      }
      nextToken
    }
  }
`;

const updateJiraEventMutation = gql`
  mutation UpdateJiraEvent($input: UpdateJiraEventInput!) {
    updateJiraEvent(input: $input) {
      issueKey
      status
      priority
      severity
    }
  }
`;

const JIRA_BASE_URL = 'https://yorkdocs.atlassian.net';

async function fetchAllJiraEvents() {
  let nextToken = null;
  const allItems = [];
  do {
    const result = await appSyncClient.query({
      query: listJiraEventsQuery,
      variables: { limit: 1000, nextToken },
      fetchPolicy: 'network-only',
    });
    const { items, nextToken: newNextToken } = result.data.listJiraEvents;
    allItems.push(...items);
    nextToken = newNextToken;
  } while (nextToken);
  return allItems;
}

async function fetchJiraIssue(issueKey) {
  const url = `${JIRA_BASE_URL}/rest/api/2/issue/${issueKey}`;
  const auth = Buffer.from(
    `${process.env.JIRA_EMAIL}:${process.env.JIRA_API_TOKEN}`
  ).toString('base64');
  try {
    const response = await axios.get(url, {
      headers: {
        Authorization: `Basic ${auth}`,
        Accept: 'application/json',
      },
    });
    const fields = response.data.fields;
    return {
      status: fields.status?.name || null,
      priority: fields.priority?.name || null,
      severity: fields.severity?.name || null,
    };
  } catch (err) {
    console.error(`Failed to fetch Jira issue ${issueKey}:`, err.response?.data || err.message);
    return null;
  }
}

async function updateJiraEvent(issueKey, status, priority, severity) {
  try {
    await appSyncClient.mutate({
      mutation: updateJiraEventMutation,
      variables: {
        input: {
          issueKey,
          status,
          priority,
          severity,
        },
      },
    });
    console.log(`Updated ${issueKey}: status=${status}, priority=${priority}, severity=${severity}`);
  } catch (err) {
    console.error(`Failed to update JiraEvent ${issueKey}:`, err.response?.data || err.message);
  }
}

(async () => {
  try {
    const jiraEvents = await fetchAllJiraEvents();
    for (const event of jiraEvents) {
      const latest = await fetchJiraIssue(event.issueKey);
      if (latest) {
        await updateJiraEvent(event.issueKey, latest.status, latest.priority, latest.severity);
      }
    }
    console.log('Migration complete.');
  } catch (err) {
    console.error('Migration failed:', err);
  }
})();
