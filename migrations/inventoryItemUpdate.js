/**
 * Summary
A script is needed to regenerate and update the inventory overview metrics file located at:
public/reports/inventory/metrics/latest.json.

🧩 Background

The latest.json file in the S3 bucket stores summarized metrics for the inventory module and is automatically updated whenever inventory records are modified. However, due to a previously introduced bug, the file has become outdated and no longer reflects the correct metrics from the latest database state.

To restore consistency, we need a script that can regenerate and overwrite this file using up-to-date data.

🎯 Objective

Write a script (one-time utility) that:

Fetches the current inventory data from the database or relevant service.

Computes the following metrics:

totalAssignedInventories

totalUnassignedInventories

employeesWithoutInventory

leftEmployeesWithInventory

Generates a JSON file in the following format:

{   "generated_at": "ISO_DATE_TIME",   "data": {     "totalAssignedInventories": <number>,     "totalUnassignedInventories": <number>,     "employeesWithoutInventory": <number>,     "leftEmployeesWithInventory": <number>   } } 

Overwrites the latest.json file at the following S3 path:
 public/reports/inventory/metrics/latest.json

📌 Notes

Ensure the timestamp (generated_at) is in ISO 8601 format with timezone (e.g., "2025-06-10T11:45:37.553Z").

Validate metric accuracy against live inventory data.


 * */ 
const AWSAppSyncClient = require("aws-appsync").default;
const gql = require("graphql-tag");
const AWS = require("aws-sdk");
const dotenv = require("dotenv");

dotenv.config();

require("isomorphic-fetch");
console.log(
  "api york hrms graphql endpoint",
  process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT
);
// S3 Key Constants
const INVENTORY_METRICS_KEY = "public/reports/inventory/metrics/latest.json";

// AppSync client setup
const appSyncClient = new AWSAppSyncClient({
  url: process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT,
  region: process.env.REGION,
  auth: {
    type: "AWS_IAM",
    credentials: AWS.config.credentials,
  },
  disableOffline: true,
});

// GraphQL queries
const listInventoryItemsQuery = gql`
  query ListInventoryItems($limit: Int, $nextToken: String) {
    listInventoryItems(limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        type
        assigned_to {
          employee_id
          active
          __typename
        }
        createdAt
        updatedAt
        employeeInventoriesId
        __typename
      }
      nextToken
      __typename
    }
  }
`;

const listEmployeesQuery = gql`
  query ListEmployees($limit: Int, $nextToken: String) {
    listEmployees(limit: $limit, nextToken: $nextToken) {
      items {
        email
        employee_id
        createdAt
        active
        updatedAt
        inventories {
          items {
            id
          }
          nextToken
          __typename
        }
      }
      nextToken
    }
  }
`;

// Helper to fetch all items from paginated GraphQL query
async function fetchAllItems(query) {
  let nextToken = null;
  const allItems = [];
  do {
    const result = await appSyncClient.query({
      query,
      variables: { limit: 1000, nextToken },
      fetchPolicy: "network-only",
    });
    const { items, nextToken: newNextToken } = Object.values(result.data)[0];
    allItems.push(...items);
    nextToken = newNextToken;
  } while (nextToken);
  return allItems;
}

const s3 = new AWS.S3();

/**
 * Saves the given inventory metrics to S3.
 * @param {Object} data - The inventory metrics to save
 * @returns {Promise<void>}
 */
const saveInventoryMetrics = async (data) => {
  console.log("🚀 ~ saveInventoryMetrics ~ data:", data);
  const timestamp = new Date().toISOString();
  const dataToSave = {
    generated_at: timestamp,
    data,
  };

  try {
    await s3
      .putObject({
        Bucket: process.env.STORAGE_DATA_BUCKETNAME,
        Key: INVENTORY_METRICS_KEY,
        Body: JSON.stringify(dataToSave, null, 2),
        ContentType: "application/json",
      })
      .promise();
    console.log("[INFO] Inventory metrics saved to S3 successfully");
  } catch (error) {
    console.error("[ERROR] Saving inventory metrics to S3:", error);
    throw error;
  }
};

/**
 * Calculates and updates inventory metrics.
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @returns {Promise<void>}
 */
const inventoryItemUpdate = async (req, res) => {
  console.log(
    "process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT",
    process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT
  );
  // Initialize metrics
  let metrics = {
    totalAssignedInventories: 0,
    totalUnassignedInventories: 0,
    employeesWithoutInventory: 0,
    leftEmployeesWithInventory: 0,
  };

  try {
    const inventories = await fetchAllItems(listInventoryItemsQuery);
    const employees = await fetchAllItems(listEmployeesQuery);

    // Count assigned/unassigned inventories
    if (Array.isArray(inventories)) {
      inventories.forEach((item) => {
        if (item.employeeInventoriesId) {
          metrics.totalAssignedInventories++;
        } else {
          metrics.totalUnassignedInventories++;
        }
      });
    }

    // Count active employees without inventory
    if (Array.isArray(employees)) {
      metrics.employeesWithoutInventory = employees.filter(
        (emp) =>
          emp.active &&
          (!emp.inventories?.items || emp.inventories.items.length === 0)
      ).length;
    }

    // Count inactive employees with assigned inventory
    if (Array.isArray(inventories)) {
      const leftEmployeesWithInventorySet = new Set();
      inventories.forEach((item) => {
        if (item.assigned_to && item.assigned_to.active === false) {
          leftEmployeesWithInventorySet.add(item.assigned_to.employee_id);
        }
      });
      metrics.leftEmployeesWithInventory = leftEmployeesWithInventorySet.size;
    }

    console.log("[INFO] Calculated Inventory Metrics:", metrics);
    await saveInventoryMetrics(metrics);
    if (res && typeof res.json === "function") {
      return res.json({ status: "success", metrics });
    } else {
      return metrics;
    }
  } catch (error) {
    console.error("[ERROR] inventoryItemUpdate failed:", error);
    if (res && typeof res.status === "function") {
      return res
        .status(500)
        .json({ error: "Failed to update inventory metrics" });
    } else {
      throw error;
    }
  }
};

inventoryItemUpdate()
  .then(() => {
    console.log("[INFO] Inventory item update completed");
  })
  .catch((error) => {
    console.error("[ERROR] Inventory item update failed:", error);
  });
