const AWS = require("aws-sdk");
const fs = require("fs");

AWS.config.update({ region: "us-east-2" });
const dynamoDb = new AWS.DynamoDB.DocumentClient();

const employeeTable = "Employee-n2bv37wxwne4fb6obc2e36aqgy-prod";
const wfhTable = "WorkFromHomeRequest-n2bv37wxwne4fb6obc2e36aqgy-prod";
const JUNE_FIRST_2025_ISO = new Date("2025-06-01T00:00:00Z");
const BATCH_SIZE = 25; // DynamoDB batch write limit
const LOG_FILE = "./wfh_logs_all_employees.txt";
fs.writeFileSync(LOG_FILE, ""); // Clear file at start

async function fetchWfhRequestsAfterJune(email) {
  const params = {
    TableName: wfhTable,
    IndexName: "getWFHByEmployee",
    KeyConditionExpression: "employeeId = :eid",
    ExpressionAttributeValues: {
      ":eid": email,
    },
  };

  let lastKey = null;
  let requests = [];

  do {
    if (lastKey) params.ExclusiveStartKey = lastKey;
    const result = await dynamoDb.query(params).promise();
    requests.push(...result.Items);
    lastKey = result.LastEvaluatedKey;
  } while (lastKey);

  requests = requests.filter(
    (req) =>
      req.status === "APPROVED" &&
      new Date(req.startDate) >= JUNE_FIRST_2025_ISO
  );

  let count = 0;
  requests.sort((a, b) => new Date(a.startDate) - new Date(b.startDate));

  fs.appendFileSync(LOG_FILE, `\n===============================\n`);
  fs.appendFileSync(LOG_FILE, `WFH Log for: ${email}\n`);
  fs.appendFileSync(LOG_FILE, `===============================\n`);
  fs.appendFileSync(
    LOG_FILE,
    `Type     | Delta | Start Date               | End Date               | Running Total\n`
  );
  fs.appendFileSync(
    LOG_FILE,
    `---------|-------|--------------------------|--------------------------|----------------\n`
  );

  for (const item of requests) {
    const startDate = new Date(item.startDate);
    const endDate = new Date(item.endDate);
    const adjustmentType = item.adjustment_type || 'UNKNOWN';
    const wfhLength = item.wfhLength;

    let delta = 0;
    if (startDate.toDateString() === endDate.toDateString()) {
      delta = wfhLength === "FULL_DAY" ? 1 : 0.5;
    } else {
      if (adjustmentType === "DEBIT") {
        let currentDate = new Date(startDate);
        while (currentDate <= endDate) {
          const day = currentDate.getDay();
          if (day !== 0 && day !== 6) {
            delta++;
          }
          currentDate.setDate(currentDate.getDate() + 1);
        }
      } else {
        const oneDay = 24 * 60 * 60 * 1000;
        delta = Math.round((endDate - startDate) / oneDay) + 1;
      }
    }

    if (adjustmentType === "DEBIT") {
      count -= delta;
    } else if (adjustmentType === "CREDIT") {
      count += delta;
    }

    const logLine = `${adjustmentType.padEnd(9)}| ${delta
      .toString()
      .padEnd(
        5
      )} | ${startDate.toISOString()} | ${endDate.toISOString()} | ${count.toFixed(
      2
    )}\n`;
    fs.appendFileSync(LOG_FILE, logLine);
  }

  return count;
}

async function fetchActiveEmployees() {
  let lastEvaluatedKey = null;
  const allEmployees = [];

  do {
    const params = {
      TableName: employeeTable,
      FilterExpression: "active <> :false AND hidden_profile <> :true",
      ExpressionAttributeValues: {
        ":false": false,
        ":true": true,
      },
      ExclusiveStartKey: lastEvaluatedKey,
    };

    const result = await dynamoDb.scan(params).promise();
    allEmployees.push(...result.Items);
    lastEvaluatedKey = result.LastEvaluatedKey;
  } while (lastEvaluatedKey);

  return allEmployees;
}

async function batchUpdateEmployees(updates) {
  const batches = [];
  for (let i = 0; i < updates.length; i += BATCH_SIZE) {
    const batch = updates.slice(i, i + BATCH_SIZE).map((item) => ({
      Update: {
        TableName: employeeTable,
        Key: { email: { S: item.email } },
        UpdateExpression: "SET wfh_balance = :count",
        ExpressionAttributeValues: {
          ":count": { N: item.count.toString() },
        },
      },
    }));
    batches.push(batch);
  }

  for (const batch of batches) {
    const requestItems = {
      TransactItems: batch,
    };
    try {
      await new AWS.DynamoDB().transactWriteItems(requestItems).promise();
    } catch (err) {
      console.error("Batch update failed:", err);
    }
  }
}

(async () => {
  try {
    const employees = await fetchActiveEmployees();
    const updates = [];

    for (const employee of employees) {
      console.log(`Processing ${employee.email}`);
      const count = await fetchWfhRequestsAfterJune(employee.email);
      updates.push({ email: employee.email, count });
    }

    await batchUpdateEmployees(updates);
    console.log("All updates completed.");
  } catch (error) {
    console.error("Error processing employees:", error);
  }
})();