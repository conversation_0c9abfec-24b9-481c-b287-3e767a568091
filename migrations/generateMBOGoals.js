const OpenAI = require('openai');
const AWS = require("aws-sdk");
const AWSAppSyncClient = require("aws-appsync").default;
const { AUTH_TYPE } = require("aws-appsync");
const gql = require("graphql-tag");
const dotenv = require("dotenv");

dotenv.config();

// Get your API key from environment variable
const apiKey = process.env.OPENAI_API_KEY;
if (!apiKey) {
  throw new Error('Please set the OPENAI_API_KEY environment variable.');
}

const openai = new OpenAI({ apiKey });

// Setup AppSync client
const region = process.env.REGION || "us-east-2";
AWS.config.update({ region: region });
const appsyncUrl =
  process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT ||
  "https://7farl5sfnvajvd47qtrnj3e6fa.appsync-api.us-east-2.amazonaws.com/graphql";

const appsyncClient = new AWSAppSyncClient({
  url: appsyncUrl,
  region: region,
  auth: {
    type: AUTH_TYPE.AWS_IAM,
    credentials: AWS.config.credentials,
  },
  disableOffline: true,
});

// GraphQL queries
const getEmployeeByEmail = /* GraphQL */ `
  query GetEmployee($email: String!) {
    getEmployee(email: $email) {
      email
      introduction
      skills {
        items {
          skill {
            name
          }
        }
      }
      SME {
        items {
          sME {
            name
          }
        }
      }
      career_start_date
    }
  }
`;

const listMBOGoalV2s = /* GraphQL */ `
  query ListMBOGoalV2s($filter: ModelMBOGoalV2FilterInput, $limit: Int, $nextToken: String) {
    listMBOGoalV2s(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        role
        experienceRange
        category
        goal
        purpose
        note
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

const systemPrompt = `
You are an expert HR professional specializing in Management by Objectives (MBO) goal setting.
Your task is to analyze an employee's profile and select the most relevant MBO goals for the upcoming quarter from the provided list of available goals.

REQUIREMENTS:
1. Select 5-7 goals by their ID from the provided list.
2. Total weightage of selected goals must equal exactly 120 points.
3. 75% of total weightage (90 points) should be based on their SME (Subject Matter Expertise) and skills.
4. 25% of total weightage (30 points) should be from other categories: process improvement, people management, communication, training, leadership, etc.
5. Must include at least one goal from each category (SME/Skills AND Other categories).
6. Each goal should have a weightage between 10-25 points.
7. Goals should be specific, measurable, achievable, relevant, and time-bound (SMART).
`;

const jsonSchema = {
  type: 'object',
  properties: {
    selected_goal_ids: {
      type: 'array',
      description: 'An array of selected MBOGoalV2 IDs',
      items: {
        type: 'string',
        description: 'ID of the selected goal'
      },
      minItems: 5,
      maxItems: 7
    },
    weightage: {
      type: 'object',
      description: 'Weightage for each selected goal ID',
      additionalProperties: {
        type: 'integer',
        minimum: 10,
        maximum: 25
      }
    },
    success_criteria: {
      type: 'object',
      description: 'Success criteria for each selected goal ID',
      additionalProperties: {
        type: 'string',
        description: 'Specific, measurable criteria for success'
      }
    }
  },
  required: ['selected_goal_ids', 'weightage', 'success_criteria'],
  additionalProperties: false
};

async function getEmployeeProfile(email) {
  try {
    const response = await appsyncClient.query({
      query: gql(getEmployeeByEmail),
      variables: { email },
      fetchPolicy: "no-cache"
    });
    return response.data.getEmployee;
  } catch (error) {
    console.error('Error fetching employee data:', error);
    throw error;
  }
}

async function listAllMBOGoalV2s() {
  let allGoals = [];
  let nextToken = null;
  do {
    const response = await appsyncClient.query({
      query: gql(listMBOGoalV2s),
      variables: { limit: 100, nextToken },
      fetchPolicy: "no-cache"
    });
    const { items, nextToken: newNextToken } = response.data.listMBOGoalV2s;
    allGoals = allGoals.concat(items);
    nextToken = newNextToken;
  } while (nextToken);
  return allGoals;
}

async function generateMBOGoals(email) {
  try {
    console.log(`\n=== Generating MBO Goals for ${email} ===\n`);
    
    // Get employee profile
    const employee = await getEmployeeProfile(email);
    console.log('Employee Profile:');
    console.log('Email:', employee.email);
    console.log('Introduction:', employee.introduction);
    console.log('Skills:', employee.skills.items.map(item => item.skill.name));
    console.log('SME:', employee.SME.items.map(item => item.sME.name));
    console.log('Career Start Date:', employee.career_start_date);
    
    // Get available MBO goals for reference
    const availableGoals = await listAllMBOGoalV2s();
    console.log(`\nAvailable MBO Goal Templates: ${availableGoals.length}`);
    
    // Prepare employee profile for AI
    const employeeProfile = {
      email: employee.email,
      introduction: employee.introduction,
      skills: employee.skills.items.map(item => item.skill.name),
      sme: employee.SME.items.map(item => item.sME.name),
      career_start_date: employee.career_start_date,
      experience_years: employee.career_start_date ? 
        Math.floor((new Date() - new Date(employee.career_start_date)) / (1000 * 60 * 60 * 24 * 365)) : 0
    };
    
    // Call OpenAI to select goal IDs
    const response = await openai.chat.completions.create({
      model: 'gpt-4.1-mini',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: `Employee profile: ${JSON.stringify(employeeProfile, null, 2)}` },
        { role: 'user', content: `Available goal templates: ${JSON.stringify(availableGoals.slice(0, 20), null, 2)}` },
        { role: 'user', content: `Manager ask: I want Gaurav to focus on client expectations management on delivery considering team’s capacity and complexity of requirements. He needs to get better at planning process by understanding priorities and backlog items.}` }
      ],
      response_format: {
        type: 'json_schema',
        json_schema: {
          name: 'selected_goal_ids_schema',
          schema: jsonSchema
        }
      }
    });
    
    const content = response.choices[0].message.content;
    console.log('Content:', content);
    const { selected_goal_ids, weightage, success_criteria } = JSON.parse(content);
    
    // Map IDs to full goal objects
    const selectedGoals = availableGoals.filter(goal => selected_goal_ids.includes(goal.id));
    
    console.log('\n=== Selected MBO Goals ===');
    selectedGoals.forEach(goal => {
      const customWeightage = weightage[goal.id] || goal.experienceRange;
      const customSuccessCriteria = success_criteria[goal.id] || 'Not specified';
      
      console.log(`ID: ${goal.id}\nTitle: ${goal.goal}\nCategory: ${goal.category}\nWeightage: ${customWeightage}\nPurpose: ${goal.purpose}\nNote: ${goal.note}\nSuccess Criteria: ${customSuccessCriteria}\n`);
    });
    
    return { selected_goal_ids, selectedGoals, weightage, success_criteria };
    
  } catch (error) {
    console.error('Error generating MBO goals:', error);
    throw error;
  }
}

// Export function and run if called directly
module.exports = { generateMBOGoals };

// Run if this file is executed directly
if (require.main === module) {
  const email = process.argv[2] || "<EMAIL>";
  generateMBOGoals(email)
    .then(() => {
      console.log('\n=== Goal Generation Complete ===');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Failed to generate goals:', error);
      process.exit(1);
    });
} 