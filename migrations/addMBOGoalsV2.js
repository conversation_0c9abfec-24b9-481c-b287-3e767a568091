const path = require("path");
const dotenv = require("dotenv");
const fs = require("fs");
const csv = require("csv-parse/sync");

dotenv.config();

const AWS = require("aws-sdk");
const AWSAppSyncClient = require("aws-appsync").default;
const { AUTH_TYPE } = require("aws-appsync");
const gql = require("graphql-tag");
const region = process.env.REGION || "us-east-2";
AWS.config.update({ region: region });
const appsyncUrl =
  process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT ||
  "https://7qor74xodrgzfn3nl2e4kcqphi.appsync-api.us-east-2.amazonaws.com/graphql";

const appsyncClient = new AWSAppSyncClient({
  url: appsyncUrl,
  region: region,
  auth: {
    type: AUTH_TYPE.AWS_IAM,
    credentials: AWS.config.credentials,
  },
  disableOffline: true,
});

const createMBOGoalV2 = /* GraphQL */ `
  mutation CreateMBOGoalV2($input: CreateMBOGoalsV2Input!) {
    createMBOGoalsV2(input: $input) {
      id
      role
      experienceRange
      category
      goal
      purpose
      note
    }
  }
`;

async function executeMutation(mutation, variables) {
  try {
    const response = await appsyncClient.mutate({
      mutation: gql(mutation),
      variables,
      fetchPolicy: "no-cache",
    });
    return response;
  } catch (err) {
    console.error("Error while trying to execute mutation", err);
    throw new Error(
      "Failed to execute GraphQL mutation. Please check the logs for more details."
    );
  }
}

// Restore the extractExperienceYears function
function extractExperienceYears(experienceStr) {
  if (!experienceStr) return 0;

  // Convert to lowercase for case-insensitive matching
  const exp = experienceStr.toLowerCase().trim();
  console.log("Processing experience string:", exp);

  // Handle specific cases
  if (exp === "common") return 0;
  if (exp === "<= 3 years") return 1;

  // First try to match the number directly
  const numberMatch = exp.match(/\d+/);
  if (numberMatch) {
    console.log("Found number:", numberMatch[0]);
    return parseInt(numberMatch[0]);
  }

  return 0;
}

async function addMBOGoals() {
  try {
    // Read and parse the CSV file
    const csvFilePath = path.join(__dirname, "goals.csv");
    const fileContent = fs.readFileSync(csvFilePath, "utf-8");
    const records = csv.parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
    });

    // Process each record
    for (const record of records) {
      console.log("Processing record:", record);
      const input = {
        category: record["Category"],
        goal: record["Goals"], // Using Common Goals column as goal
        purpose: record["Purpose"], // Using Purpose column as purpose
        role: record["Goals"], // Using Common Goals as role since it's not in the new format
        experienceRange: extractExperienceYears(record["Experience"]), // Use restored parsing function
      };

      try {
        const response = await executeMutation(createMBOGoalV2, { input });
        console.log(
          "Successfully created MBOGoalV2:",
          response.data.createMBOGoalV2
        );
      } catch (error) {
        console.error("Error creating MBOGoalV2 for record:", record, error);
      }
    }
  } catch (error) {
    console.error("Error processing CSV file:", error);
  }
}

// Run the script
addMBOGoals().then(() => {
  console.log("Finished processing MBO goals");
});