const OpenAI = require('openai');
const readline = require('readline');

// Get your API key from environment variable
const apiKey = process.env.OPENAI_API_KEY;
if (!apiKey) {
  throw new Error('Please set the OPENAI_API_KEY environment variable.');
}

const openai = new OpenAI({ apiKey });

const systemPrompt = `
You are an automated bot for the internal HRMS system.
Understand the given schema and write GraphQL mutation queries with suitable inputs based on the employee's requested actions.
Only allow timesheet and leave related queries.
For timesheet, use default working hours and days: Monday to Friday, 10am to 6pm. Separate entry for each day.
Return your response as a JSON object with two fields:
- mutation: the GraphQL mutation query string
- variables: a JSON object with the required input variables strict in the format specified in the schema for the mutation
`;

// Shortened schema string for JS compatibility (remove decorators/type annotations)
const schema = `
Timesheet schema:

Example mutation:
mutation CreateTimeSheet($input: CreateTimeSheetInput!) {
  createTimeSheet(input: $input) {
    id
    start_time
    end_time
    employeeID
    timeSheetProjectId
    description
  }
}

Example input:
"input": {
    "timeSheetProjectId": "409f714a-d5e5-4150-b9ae-0515783a6e9d",
    "description": "hello",
    "employeeID": "<EMAIL>",
    "start_time": 1746988200,
    "end_time": 1746989100,
  }
`;

const projects = {
    "data": {
      "listProjects": {
        "items": [
          {
            "id": "47ae4a4b-633b-4e9f-993e-9b457de3caf1",
            "name": "VAST Data Digital Marketing Subscription"
          },
          {
            "id": "761894fa-55f2-4b11-b0fe-a5610ee3cb0e",
            "name": "Chirpyest"
          },
          {
            "id": "2dfbf998-9347-449a-a57c-724152b45b1b",
            "name": "Aquifier RevOps project"
          },
          {
            "id": "283cfd08-de01-4c77-8033-5b7a0225621f",
            "name": "Activate OS Maintenance & Support "
          },
          {
            "id": "e19a946d-784e-487d-a4ca-187915c16c1b",
            "name": "Stockpress"
          },
          {
            "id": "21a9034f-daae-467c-9d8b-a00477f7164f",
            "name": "Apparel Impact"
          },
          {
            "id": "51223931-c05f-43d2-9226-e4a46056ca0e",
            "name": "Solverein Website Project"
          },
          {
            "id": "b14f9da4-8603-440d-8894-cbb1e52a657c",
            "name": "CoachMetrix"
          },
          {
            "id": "ba2dcba6-d025-4bcc-beb8-cedd98391a5a",
            "name": "Net Atlantic"
          },
          {
            "id": "b750d772-e73d-4beb-bea0-09c5ad249b00",
            "name": "SchoolBI"
          },
          {
            "id": "38a72cd5-3aa8-4226-bef2-3398e5f0b45e",
            "name": "Stellar Menus"
          },
          {
            "id": "c842f0bc-49bb-42dc-a5e9-267c5b9221b5",
            "name": "Broadlume DataBI"
          },
          {
            "id": "cf6e4f94-0776-4951-9c45-f97a850d9c4b",
            "name": "Ivory Law"
          },
          {
            "id": "c53d6e42-3018-4c26-83c0-ef8024ab1ed6",
            "name": "Nurse 1-1"
          },
          {
            "id": "5ba4b65a-2f27-42b8-bb2b-1262a3a53694",
            "name": "Lunar Solar Website Maintenance & Support"
          },
          {
            "id": "26246e03-1d0c-40ca-bea5-e67cc8af99d2",
            "name": "Cognitive Space"
          },
          {
            "id": "d9ecc74c-2ffe-40c3-a061-53152e5c5172",
            "name": "ShiftGroup Revenue Operations Project"
          },
          {
            "id": "f2024c48-94f7-4da1-9534-27f2c546d0dd",
            "name": "Product Dev Management"
          },
          {
            "id": "9e156930-a02c-4b25-9c7a-62415d93f498",
            "name": "ShiftGroup Website Standup & Testimonial Project"
          },
          {
            "id": "6b2494b7-7211-4cc6-91c3-3939d2928a95",
            "name": "Buyers View Maintenance & Support"
          },
          {
            "id": "b80447af-4bf8-489f-a194-a201bf3fd52c",
            "name": "Squeeze"
          },
          {
            "id": "f327f608-7c47-4a1f-a48d-8858588289e8",
            "name": "Broadlume BMS"
          },
          {
            "id": "0506f299-de10-431e-b568-0c3de8ac5395",
            "name": "Beon, NTG Freight, Transportation Insights "
          },
          {
            "id": "7c7ecf75-27b7-4343-a7ee-50959d05cfba",
            "name": "Lytica Revenue Operations Project"
          },
          {
            "id": "5b5c2447-0e67-443d-8e5e-092f036ae30b",
            "name": "G&A Management"
          },
          {
            "id": "56f550fc-5823-4cba-a9e0-4dd9cb8ce449",
            "name": "Hovr"
          },
          {
            "id": "da73abf1-f1e4-4c43-b303-20101f590c52",
            "name": "CloudValid"
          },
          {
            "id": "c348baab-9799-49a5-af44-8e0f2081ded7",
            "name": "Bretton Trova"
          },
          {
            "id": "afec27c1-0804-45ea-bf8b-f22b401ac8d3",
            "name": "Stay AI Website Maintenance & Support"
          },
          {
            "id": "b0fe62db-d791-4c58-831c-efd3e3670712",
            "name": "Shiftgroup"
          },
          {
            "id": "b1cf0a9d-b1c5-4af2-9721-9d2ac3cebec7",
            "name": "Quantum Xchange"
          },
          {
            "id": "b1679e14-c93d-4fe3-941b-9dd5aa4351e1",
            "name": "Athletes Hospitality Revenue Operations Project"
          },
          {
            "id": "99102a42-d7be-4562-b9ee-61ac84ca1ccc",
            "name": "SchoolBI Website Maintenance & Support"
          },
          {
            "id": "baf2c0f8-e798-4e09-8f6b-d9018f7b4340",
            "name": "Cognitive Space"
          },
          {
            "id": "d39f947d-a424-4878-808a-818bfa01bc46",
            "name": "Buyers View"
          },
          {
            "id": "110eca0e-d497-4de8-9eb4-38d986c1115a",
            "name": "ACL RnD Subscription"
          },
          {
            "id": "e8af7a0e-fc48-4992-bdca-346338fcd5ce",
            "name": "Fix Media"
          },
          {
            "id": "52404daf-ff97-4a53-a920-f7ed7c6ded5f",
            "name": "York IE Internal R&D"
          },
          {
            "id": "b2293ead-2c31-4e55-8214-9b30b2511ea7",
            "name": "York IE Website"
          },
          {
            "id": "ba8b3cb7-b30b-4649-8ae7-8e7b352c7bd9",
            "name": "ContractSafe"
          },
          {
            "id": "fbb9ea49-1c4a-495d-aaae-fb37e365b5de",
            "name": "Medical Ease"
          },
          {
            "id": "ec6558ca-ed98-4134-b62e-754c3afcd39b",
            "name": "Canvs AI SEO & CRM Audit Project"
          },
          {
            "id": "669d091b-7952-4876-8864-b92ed86037c2",
            "name": "NTG Freight Maintenance & Support"
          },
          {
            "id": "e98b7d66-61dd-4b1f-a8ff-296aeca0bd57",
            "name": "Delphi"
          },
          {
            "id": "41c5472d-578b-4a41-b942-eaec5e6ccfba",
            "name": "Blustream"
          },
          {
            "id": "624f9a94-6f4d-481b-af07-703547ecdd34",
            "name": "Transportation Insight Website Standup"
          },
          {
            "id": "6cead53f-3aff-48d0-a6d1-a21cb0fe24c2",
            "name": "TBR Insight Center"
          },
          {
            "id": "e43a2241-00b3-44f0-a09a-377b892d1a86",
            "name": "CreationSpace (fka Seer)"
          },
          {
            "id": "c18dbb08-5641-43ff-a826-af95b6009430",
            "name": "GTM Management"
          },
          {
            "id": "cb948024-d8df-418f-b120-1386f1bd63e8",
            "name": "TopProp"
          },
          {
            "id": "32d32b67-5ca1-40e9-ac1c-b15b43c072d3",
            "name": "Science On Call"
          },
          {
            "id": "3014e78f-1530-46c5-b5e1-c919f728af61",
            "name": "Synaptic"
          },
          {
            "id": "03c7234e-715e-4a44-93b4-9027330582b9",
            "name": "CanvsAI Revenue Operations Project"
          },
          {
            "id": "998ccd33-8f2c-40c0-878e-c0282a3d5330",
            "name": "MilesCX"
          },
          {
            "id": "fd9c5258-761c-4c5a-89dd-b331938f2b1e",
            "name": "Patriot Pay Architecture project"
          },
          {
            "id": "b98d7a46-6456-4f0e-b163-3aaa20c5950b",
            "name": "Idencia"
          },
          {
            "id": "c9c17564-8c8d-467d-a147-7f116b93070e",
            "name": "Connect Care"
          },
          {
            "id": "396088ef-5ed3-4778-b886-9fc6e3f728ea",
            "name": "Stellar Menus Printer Project"
          },
          {
            "id": "35a8bf6a-4728-445e-b371-538848482761",
            "name": "Maverick App"
          },
          {
            "id": "c4748673-5a07-4e87-87a2-da58cc4d3e8f",
            "name": "Waterfall"
          },
          {
            "id": "15c1a22a-4b7b-421b-bae7-9485611849c2",
            "name": "Radical Health"
          },
          {
            "id": "60b36720-e2cc-4d54-88e9-30513bca43aa",
            "name": "ENDVR Website Maintenance & Support"
          },
          {
            "id": "48fd5a75-acc5-4e68-99cd-63bb1c86fe01",
            "name": "Stay AI"
          },
          {
            "id": "accdaa46-58f1-442f-ba97-851dcccbb680",
            "name": "Echo Consulting"
          },
          {
            "id": "50357726-41e2-460b-8910-d6facd80f45b",
            "name": "SubItUp"
          },
          {
            "id": "3b8413f2-d0cf-4cf4-9c44-61e22e025b43",
            "name": "R&D - Platform"
          },
          {
            "id": "17a0f125-992e-47c0-9fa7-be077f3d81a5",
            "name": "Sync Sports"
          },
          {
            "id": "7d39d58d-58b1-4773-93d0-6d1ed4d8a002",
            "name": "Invigorate"
          },
          {
            "id": "731ebcb6-0c1a-48f5-b20d-c96081c66044",
            "name": "Broadlume Website Platform"
          },
          {
            "id": "181d63bd-cec3-4779-a0e3-27251a16051b",
            "name": "NTG Freight Website Standup"
          },
          {
            "id": "f07cb2f1-f7e8-4ca7-854a-e7d233f5ac33",
            "name": "Dataplor Website Standup"
          },
          {
            "id": "99c834bf-5134-49c8-a0ac-f572fa54c0b3",
            "name": "ProxyIQ"
          },
          {
            "id": "dad27603-b3b0-4818-b63d-dc3fd5f493fe",
            "name": "Packback"
          },
          {
            "id": "bec983d0-0898-44df-94f7-365f9b18dcaf",
            "name": "Warning Technologies"
          },
          {
            "id": "771632f6-8126-49ba-ba72-5f175c6df472",
            "name": "SchoolBI Website"
          },
          {
            "id": "08582f72-572c-4995-8d9a-775d9cd49085",
            "name": "Mamba Growth Revenue Operations Project"
          },
          {
            "id": "bfaae5b8-359d-48c3-917e-8bf93e72c0ac",
            "name": "Cranium"
          },
          {
            "id": "376f855a-7eae-4a94-94b8-480d79ee2b4e",
            "name": "Loyalty Landers Google Tag Manager Project"
          },
          {
            "id": "376de392-1f21-4567-9bdc-e89d7311df09",
            "name": "Klearly"
          },
          {
            "id": "7b7671d4-1765-48d7-9cf7-f12416a4b230",
            "name": "40GRID"
          },
          {
            "id": "e8428256-fc94-495e-a8d8-22931d2a2810",
            "name": "Opus1.io Website Standup"
          },
          {
            "id": "f1a203ae-57e1-4ee0-ac03-daa074281d64",
            "name": "Trecco"
          },
          {
            "id": "34b0d16a-493a-4d7b-b07c-5da5e55afb6e",
            "name": "Renter’s Auction"
          },
          {
            "id": "338251c8-6cd6-402d-83ac-96d13f9a0cd9",
            "name": "GTM Management"
          },
          {
            "id": "fa3a4ad9-92d1-4467-a9b7-5b9001a18fa6",
            "name": "Fuel"
          },
          {
            "id": "d090c3d6-51f5-46c5-9a78-c0060b4ad816",
            "name": "Patriot Pay"
          },
          {
            "id": "4c56e0c9-e6ef-44f2-b65a-36e57a21a1ed",
            "name": "No IP"
          },
          {
            "id": "3af08a2b-a108-46a3-b178-7a24c1286411",
            "name": "Network to Code (NTC) Website Maintenance & Support"
          },
          {
            "id": "e3c954b5-14f9-4a2d-b942-64270d0346a6",
            "name": "Total Synergy"
          },
          {
            "id": "a00dfaed-391e-4796-955f-b1c5d23071b1",
            "name": "Lynx RevOps project"
          },
          {
            "id": "6f02939d-72cf-4da4-a315-50e5699ae217",
            "name": "Embark Live"
          },
          {
            "id": "e5737b58-e111-4e01-91d2-1763cfef1937",
            "name": "Lytica Website Maintenance & Support"
          },
          {
            "id": "49bd56b5-8c8c-4fd1-b5bc-42a46b498094",
            "name": "BedRock System Revenue Operations Project"
          },
          {
            "id": "ddd77e4d-708d-4b53-b208-e9c74c7557b2",
            "name": "Broadlume Shopify"
          },
          {
            "id": "b1b3acad-b955-409c-be2b-4b18a72607dd",
            "name": "ETA Transit Digital Marketing Subscription"
          },
          {
            "id": "829d864e-87a7-442e-8072-2f637f8ff911",
            "name": "CloudForge"
          },
          {
            "id": "e4d75a06-e93b-4286-8651-91d692306136",
            "name": "Idencia Oct PM burst"
          },
          {
            "id": "870d1e5e-689e-4734-8887-d471111d90d3",
            "name": "BigNetwork"
          },
          {
            "id": "9840af2d-257f-4f3e-9449-e673dbb42eab",
            "name": "Winrate Website Standup"
          },
          {
            "id": "6defba8a-a659-4dc5-bc6e-0a8d9f705449",
            "name": "Hub Project"
          }
        ]
      }
    }
}

const jsonSchema = {
  type: 'object',
  properties: {
    mutations: {
      type: 'array',
      description: 'An array of objects, each containing a GraphQL mutation query string and its variables. in one item, only one mutation and one variables are allowed. Duplicate mutations if required. Use an array if multiple mutations are required.',
      items: {
        type: 'object',
        properties: {
          mutation: {
            type: 'string',
            description: 'The GraphQL mutation query string.'
          },
          variables: {
            type: 'object',
            description: 'A JSON object with the required input variables for the mutation.'
          }
        },
        required: ['mutation', 'variables'],
        additionalProperties: false
      }
    }
  },
  required: ['mutation', 'variables'],
  additionalProperties: false
};

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const hardcodedPrompt = "fill my time sheet for last week, everyday 8 hours, I worked on hub project everyday";

(async () => {
  try {
    const response = await openai.chat.completions.create({
      model: 'o3',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: `Here is the aws amplify appsync graphql schema: ${schema}` },
        { role: 'user', content: `Current employee email: <EMAIL>` },
        { role: 'user', content: `Here is the list of projects: ${JSON.stringify(projects)}` },
        { role: 'user', content: hardcodedPrompt },
        
        // Calculate Monday and Friday of the current week (Monday = 1, Sunday = 0)
        (() => {
          const now = new Date();
          const day = now.getDay();
          // JS: Sunday=0, Monday=1, ..., Saturday=6
          // If Sunday, treat as 7 for easier math
          const dayOfWeek = day === 0 ? 7 : day;
          // Calculate Monday (subtract days to get to Monday)
          const monday = new Date(now);
          monday.setDate(now.getDate() - (dayOfWeek - 1));
          monday.setHours(0, 0, 0, 0);
          // Calculate Friday (add days to get to Friday)
          const friday = new Date(monday);
          friday.setDate(monday.getDate() + 4);
          friday.setHours(23, 59, 59, 999);
          module.exports._weekRange = { monday, friday }; // for testing if needed
          // Add the message to the messages array
          return { role: 'user', content: `This week is from ${monday.toISOString()} to ${friday.toISOString()}` };
        })(),
      ],
      response_format: {
        type: 'json_schema',
      json_schema: {
        name: 'jira_description_review_schema',
        schema: jsonSchema
      }
      }
    });
    const content = response.choices[0].message.content;
    // --- Setup AppSync client for GraphQL calls ---
    const AWS = require("aws-sdk");
    const AWSAppSyncClient = require("aws-appsync").default;
    const gql = require("graphql-tag");

    // You may need to adjust these environment variables as per your setup
    const appSyncClient = new AWSAppSyncClient({
      url: process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT,
      region: process.env.REGION,
      auth: {
        type: "AWS_IAM",
        credentials: AWS.config.credentials,
      },
      disableOffline: true,
    });

    // Helper to run a mutation
    async function runGraphQLMutation(mutationString, variables) {
      // try {
      //   const result = await appSyncClient.mutate({
      //     mutation: gql`${mutationString}`,
      //     variables,
      //   });
      //   console.log("Mutation result:", JSON.stringify(result.data, null, 2));
      // } catch (err) {
      //   console.error("GraphQL mutation error:", err);
      // }
    }

    try {
      const parsed = JSON.parse(content);
      if (Array.isArray(parsed.mutations)) {
        for (let [idx, mutationObj] of parsed.mutations.entries()) {
          console.log(`GraphQL Mutation Query [${idx + 1}]:\n`, mutationObj.mutation);
          console.log('\nVariables Object:\n', JSON.stringify(mutationObj.variables, null, 2));
          await runGraphQLMutation(mutationObj.mutation, mutationObj.variables);
        }
      } else {
        console.log('GraphQL Mutation Query:\n', parsed.mutation);
        console.log('\nVariables Object:\n', JSON.stringify(parsed.variables, null, 2));
        await runGraphQLMutation(parsed.mutation, parsed.variables);
      }
    } catch (e) {
      console.log('Raw response (not valid JSON):\n', content);
    }
  } catch (e) {
    console.error('Error communicating with OpenAI API:', e);
  }
  rl.close();
})();