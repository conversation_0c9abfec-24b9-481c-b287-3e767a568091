{"name": "hrms", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/graphs": "^1.4.0", "@ant-design/icons": "^4.8.0", "@ant-design/plots": "^1.2.5", "@aws-amplify/auth": "3.4.34", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@craco/craco": "^7.1.0", "@faker-js/faker": "^9.3.0", "@jimp/png": "^0.22.12", "@monaco-editor/react": "^4.6.0", "@reduxjs/toolkit": "^1.9.1", "@sasza/react-panzoom": "^1.18.1", "@vladmandic/face-api": "^1.7.14", "amplify-prompts": "^2.6.2", "antd": "^4.21.7", "aws-amplify": "^4.3.20", "aws-amplify-react": "^5.1.9", "aws-cdk-lib": "~2.46.0", "aws-sdk": "^2.1368.0", "axios": "^1.3.0", "craco": "^0.0.3", "craco-less": "^3.0.1", "crypto-js": "^4.1.1", "csv-parse": "^5.6.0", "csv-parser": "^3.0.0", "csv-stringify": "^6.5.2", "dayjs": "^1.11.7", "emoji-picker-react": "^4.12.2", "firebase": "^10.5.2", "gm": "^1.25.0", "graphql-ttl-transformer": "^2.0.2", "html2canvas": "^1.4.1", "jimp": "^1.6.0", "lodash": "^4.17.21", "markdown-it": "^13.0.1", "marked": "^4.2.5", "moment": "^2.29.4", "monaco-editor": "^0.52.0", "openai": "^4.68.4", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.1", "react-highlight-words": "^0.18.0", "react-infinite-scroll-component": "^6.1.0", "react-joyride": "^2.9.3", "react-markdown": "^10.1.0", "react-markdown-editor-lite": "^1.3.4", "react-phone-number-input": "^3.4.10", "react-quill": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "redux": "^4.2.0", "redux-persist": "^6.0.0", "redux-persist-transform-encrypt": "^4.0.0", "sharp": "^0.29.3", "typescript": "^5.8.3", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "add-module": "./create-module.sh "}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": [">0.2%", "not dead", "not op_mini all"], "devDependencies": {"@svgr/webpack": "^8.1.0", "husky": "^4.3.8", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "tailwindcss": "^3.4.11"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write"]}}