{"version": "1", "cognitoConfig": {"identityPoolName": "yorkhrmsfa131fa1_identitypool_fa131fa1", "allowUnauthenticatedIdentities": false, "resourceNameTruncated": "yorkhrfa131fa1", "userPoolName": "yorkhrmsfa131fa1_userpool_fa131fa1", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email"], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "yorkhrfa131fa1_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "fa131fa1", "resourceName": "yorkhrmsfa131fa1", "authSelections": "identityPoolAndUserPool", "useDefault": "manual", "usernameAttributes": ["email"], "userPoolGroupList": ["Executive", "UnitTeamLeader", "Hr", "SquadLeader", "TechnicalManager", "admin", "ITAdmin", "Rec<PERSON>er"], "serviceName": "Cognito", "usernameCaseSensitive": false, "useEnabledMfas": true, "authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "unauthRoleArn": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}, "breakCircularDependency": true, "dependsOn": [{"category": "function", "resourceName": "yorkhrmsfa131fa1PreSignup", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}, {"category": "function", "resourceName": "yorkhrmsfa131fa1PostAuthentication", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}], "userPoolGroups": false, "adminQueries": false, "triggers": {"PostAuthentication": ["custom"], "PreSignup": ["custom"]}, "hostedUI": true, "parentStack": {"Ref": "AWS::StackId"}, "permissions": [], "authTriggerConnections": "[\n  {\n    \"triggerType\": \"PostAuthentication\",\n    \"lambdaFunctionName\": \"yorkhrmsfa131fa1PostAuthentication\"\n  },\n  {\n    \"triggerType\": \"PreSignUp\",\n    \"lambdaFunctionName\": \"yorkhrmsfa131fa1PreSignup\"\n  }\n]", "authProviders": [], "thirdPartyAuth": false, "hostedUIDomainName": "york-auth-sso", "authProvidersUserPool": ["Google"], "hostedUIProviderMeta": "[{\"ProviderName\":\"Google\",\"authorize_scopes\":\"openid email profile\",\"AttributeMapping\":{\"email\":\"email\",\"username\":\"sub\"}}]", "oAuthMetadata": "{\"AllowedOAuthFlows\":[\"code\"],\"AllowedOAuthScopes\":[\"phone\",\"email\",\"openid\",\"profile\",\"aws.cognito.signin.user.admin\"],\"CallbackURLs\":[\"http://localhost:3000/login/\",\"https://stage-hub.allcloudworks.com/login/\",\"https://hub.york.ie/login/\",\"https://york-auth-sso-staging.auth.us-east-2.amazoncognito.com/\",\"https://york-auth-sso-staging.auth.us-east-2.amazoncognito.com/oauth2/idpresponse/\"],\"LogoutURLs\":[\"http://localhost:3000/login/\",\"https://stage-hub.allcloudworks.com/login/\",\"https://hub.york.ie/login/\",\"http://localhost:3000/login/\",\"https://stage-hub.allcloudworks.com/login/\",\"https://stage-hub.allcloudworks.com/login/\",\"http://localhost:3000/login/\",\"https://stage-hub.allcloudworks.com/login/\",\"https://hub.york.ie/login/\"]}"}}