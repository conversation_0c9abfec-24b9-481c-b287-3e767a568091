import { AmplifyApiGraphQlResourceStackTemplate } from '@aws-amplify/cli-extensibility-helper';


export function override(resources: AmplifyApiGraphQlResourceStackTemplate) {
    const amplifyMetaJson = require('../../../amplify-meta.json');
    const envName = amplifyMetaJson.providers.awscloudformation.StackName.split("-").slice(-2, -1).pop();
    let cloudWatchLogsRoleArn = "arn:aws:iam::323526869397:role/service-role/appsync-graphqlapi-logs-us-east-2";
    if (envName === "prod") {
        cloudWatchLogsRoleArn = "arn:aws:iam::333798832955:role/appsync-graphqlapi-logs-us-east-2";
    }
    resources.api.GraphQLAPI.logConfig = {
        cloudWatchLogsRoleArn: cloudWatchLogsRoleArn,
        excludeVerboseContent: false,
        fieldLogLevel: 'NONE'
    }
}