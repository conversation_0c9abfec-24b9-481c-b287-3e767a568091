enum LEAVE_TYPE {
  SICK
  PRIVILEGE
  MATERNITY
  PATERNITY
  UNPAID
}

enum LEAVE_ADJUSTMENT_TYPE {
  CREDIT
  DEBIT
}

enum PROJECT_STATUS {
  GREEN
  RED
  YELLOW
  INACTIVE
  POTENTIAL_DOWNGRADE
  CANCELLATION
}

enum GENDER {
  MALE
  FEMALE
  OTHER
}

enum LEAVE_LENGTH {
  FULL_DAY
  FIRST_HALF
  SECOND_HALF
}

enum CURRENCY {
  USD
  INR
}

enum TICKET_STATUS {
  ACTIVE
  PENDING
  CLOSED
}
enum ACCOUNT_STATUS {
  ACTIVE #Active User
  INACTIVE # Not Active User
  RESIGNED # Resigned but not inactive yet (Formalities pending) - yet to develop frontend
  PENDING_PASSWORD # Logged in but password pending
  ONBOARDING_PENDING # When user is on fetch data screen to enter personal mail and fetch data
  ONBOARDING_FORM # When user is on onboarding step form
  PENDING_APPROVAL # HR Approaval
  HIDDEN # Hidden profiles not visible to anyone
}

enum FREQUENCY {
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
}

enum COMPLETION_TYPE {
  AUTOMATIC
  MANUAL
}

enum EXECUTABLE_STATUS {
  PENDING
  COMPLETED
}
type Employee
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "admin"
          "ITAdmin"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
      {
        allow: owner
        ownerField: "email"
        identityClaim: "email"
        operations: [read, update]
      }
    ]
  )
  @model(subscriptions: null) {
  jiraEvents: [JiraEvent] @hasMany
  jiraEventsHistory: [JiraEventHistory] @hasMany
  email: String! @primaryKey
  employee_id: String
  account_status: ACCOUNT_STATUS
  first_name: String
  last_name: String
  title: Designation @hasOne
  mobile: String
  reward_points: Int
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "UnitTeamLeader", "admin"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
      ]
    )

  slack_id: String
  blood_group: String
  emergency_contact_num: String
  emergency_contact_relation: String
  married: Boolean
  spouse_full_name: String
  reporting_to: Employee @hasOne
  skills: [Skill]
    @manyToMany(relationName: "EmployeeSkill")
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [create, update, read, delete]
        }
      ]
    )
  SME: [SME]
    @manyToMany(relationName: "EmployeeSME")
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [create, update, read, delete]
        }
      ]
    )
  pan_card: String
  aadhar_card: String
  form12BB: String
  experience_letter: String
  passport_photo: String
  address_proof: String
  resignation_letter: String
  salary_slip: String
  salary_slip_1: String
  salary_slip_2: String
  personal_email: String
  york_appointment: String
  york_agreement: String
  reffered_by: String
  address: String
  inventories: [InventoryItem] @hasMany
  # saas_subscription: [SaasSubscription] @hasMany // Commented out as not used
  squad: Squad @belongsTo
  guild: Guild @belongsTo
  salary: [Salary]
    @hasMany
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [] }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: []
        }
      ]
    )
  bank_details: [BankDetails] @hasMany
  birth_date: AWSDate
  anniversary_date: AWSDate
  career_start_date: AWSDate
  york_start_date: AWSDate
  york_end_date: AWSDate
  profile_pic: String
  profile_pic_requested: String
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [create, update, read, delete]
        }
      ]
    )
  active: Boolean
  documents: [String]
  gender: GENDER
  facebook_link: String
  linkedin_link: String
  twitter_link: String
  instagram_link: String
  usual_starting_time: AWSTimestamp
  epf: String
  uan: String
  employee_project_allocation: [EmployeeProjectAllocation] @hasMany
  hidden_profile: Boolean @default(value: "false")
  interviews: [Interview] @manyToMany(relationName: "InterviewPanelists")
  secret_santa: String # removed connection with self
  isInterviewer: Boolean @default(value: "false")
  timesheet_entries: [TimeSheet]
    @hasMany
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter"]
          operations: [create, read, update, delete]
        }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [create, update, read, delete]
        }
      ]
    )
  leaves: [Leave] @hasMany
  leaveRequests: [LeaveRequest] @hasMany
  certification: [EmployeeCertificates] @hasMany
  festivalLeaves: [FestivalLeave]
    @hasMany
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter"]
          operations: [create, read, update, delete]
        }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [create, update, read, delete]
        }
      ]
    )
  mbos: [MBO]
    @hasMany
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: [
            "Hr"
            "UnitTeamLeader"
            "SquadLeader"
            "Executive"
            "Recruiter"
          ]
          operations: [create, read, update, delete]
        }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [read]
        }
      ]
    )
  checklistItemResponses: [ChecklistItemResponse]
    @hasMany
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [] }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: []
        }
      ]
    )
  sick_leave_balance: Float
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
      ]
    )
  privilege_leave_balance: Float
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
      ]
    )
  coins_transactions: [CoinTransaction] @hasMany
  zoomMeetingsHosted: [ZoomMeeting] @hasMany
  zoomMeetingsParticipated: [ZoomMeetingInternalParticipant] @hasMany
  projectMeetings: [ProjectMeetings]
    @manyToMany(relationName: "ProjectMeetingEmployees")
  day_start_time: [String]
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [create, update, read, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
      ]
    )
  day_end_time: [String]
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [create, update, read, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
      ]
    )
  allocatedDesk: String
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [create, update, read, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
      ]
    )
  wishlist: [String]
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [create, update, read, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
      ]
    )

  projectPasswords: [ProjectPasswords]
    @manyToMany(relationName: "ProjectPasswordEmployeesAccess")
  kudos: [Kudos] @manyToMany(relationName: "EmployeeKudos")

  introduction: String  # Quick Introduction
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [create, update, read, delete]
        }
      ]
    )

  configurations: AWSJSON  # save UI configurations
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
        {
          allow: owner
          ownerField: "email"
          identityClaim: "email"
          operations: [create, update, read, delete]
        }
      ]
    )
  metadata: AWSJSON
  wfh_balance: Float
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [read] }
      ]
    )
  country: Country @default(value: "IN")
  tags: [String]
}

enum Country {
  IN
  US
}

enum AwardType {
  CERTIFICATE
  BADGE
}

type EmployeeCertificates
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "ITAdmin"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
      {
        allow: owner
        ownerField: "employeeCertificationId"
        identityClaim: "email"
        operations: [read]
      }
    ]
  )
  @model(subscriptions: null) {
  S3Key: String!
  title: String
  provider: String
  url: String
  employee: Employee! @belongsTo
  employeeCertificationId: String!
    @index(
      name: "getCertificationByEmployee"
      queryField: "getCertificationByEmployee"
    )
  provided_date: AWSDate
  type: AwardType
}

type BankDetails
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "Recruiter"]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "employeeBank_detailsId"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  bank_name: String!
  account_number: String!
  account_holder_name: String!
  IFSC_code: String!
  branch: String!
  employee: Employee @belongsTo
  current: Boolean @default(value: "false")
}

enum ChecklistType {
  ONBOARDING
  OFFBOARDING
}

type ChecklistItem
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "Recruiter"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  title: String!
  new_hire_only: Boolean @default(value: "false")
  type: ChecklistType
}

type ChecklistItemResponse
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "Recruiter"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  checklistItem: ChecklistItem @hasOne
  employee: Employee @belongsTo
  response: Boolean @default(value: "false")
}

type Leave
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
      {
        allow: owner
        ownerField: "employeeLeavesId"
        identityClaim: "email"
        operations: [read]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  type: LEAVE_TYPE!
  start_time: AWSDateTime
  end_time: AWSDateTime
  adjustment_type: LEAVE_ADJUSTMENT_TYPE!
  comp_off: CompOff @belongsTo
  employee: Employee! @belongsTo
  employeeLeavesId: String
    @index(
      name: "LeaveByEmployee"
      queryField: "LeaveByEmployee"
      sortKeyFields: ["createdAt"]
    )
  count: Float!
  description: String
  leave_length: LEAVE_LENGTH! @default(value: "FULL_DAY")
  createdAt: String
  DoNotProcess: Boolean
  approved_by: Employee @hasOne
}

type LeaveRequest
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "employeeLeaveRequestsId"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model {
  id: ID!
  type: LEAVE_TYPE!
  start_time: AWSDateTime!
  end_time: AWSDateTime!
  adjustment_type: LEAVE_ADJUSTMENT_TYPE!
  employee: Employee! @belongsTo
  employeeLeaveRequestsId: String
    @index(
      name: "LeaveRequestByEmployee"
      queryField: "LeaveRequestByEmployee"
      sortKeyFields: ["createdAt"]
    )
  comment: String
  comp_off: CompOff @belongsTo
  leave_length: LEAVE_LENGTH! @default(value: "FULL_DAY")
  createdAt: String
}

type WorkFromHomeRequest
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "employeeId"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model {
  id: ID!
  employeeId: String!
    @index(
      name: "getWFHByEmployee"
      queryField: "getWFHByEmployee"
      sortKeyFields: ["updatedAt"]
    )
  employee: Employee @hasOne(fields: ["employeeId"])
  reason: String!
  startDate: AWSDateTime!
  endDate: AWSDateTime!
  wfhLength: LEAVE_LENGTH
  status: KUDOS_STATUS
    @default(value: "SUBMITTED")
    @index(
      name: "getWFHByStatus"
      queryField: "getWFHByStatus"
      sortKeyFields: ["updatedAt"]
    )
  requestedAt: AWSDateTime!
  reviewedBy: String
  reviewedByDetailed: Employee @hasOne(fields: ["reviewedBy"])
  comments: [AWSJSON]
  updatedAt: String
  adjustment_type: LEAVE_ADJUSTMENT_TYPE
}

type FestivalLeave
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "employeeFestivalLeavesId"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  year: Int!
  name: String!
  date: AWSDate!
  day: String!
  employee: Employee! @belongsTo
}

type CompOff
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      { allow: private, operations: [create, read, update, delete] }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  start_time: AWSDateTime!
  end_time: AWSDateTime!
  leave: Leave @hasOne
  leave_request: LeaveRequest @hasOne
}

type Designation
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  name: String!
  level: Int!
  overall_level: Int!
  min_experience: Int # save in months
  max_experience: Int
  salary_band_low: Int
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "UnitTeamLeader", "Recruiter"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [] }
      ]
    )
  salary_band_high: Int
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "UnitTeamLeader", "Recruiter"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [] }
      ]
    )
}

type Salary
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive"]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "employeeSalaryId"
        identityClaim: "email"
        operations: [read]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  employee: Employee! @belongsTo
  start_time: AWSDate!
  end_time: AWSDate!
  ctc: Int!
  active: Boolean @default(value: "false")
}

type Skill
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      {
        allow: private
        provider: userPools
        operations: [read, create, delete, update]
      }
    ]
  )
  @model(subscriptions: null) {
  name: String! @primaryKey
  metadata: AWSJSON
  employees: [Employee] @manyToMany(relationName: "EmployeeSkill")
  HiringPosition: [HiringPosition]
    @manyToMany(relationName: "HiringPositionSkill")
  Candidate: [Candidate] @manyToMany(relationName: "CandidateSkill")
}

type SME
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      {
        allow: private
        provider: userPools
        operations: [read, create, delete, update]
      }
    ]
  )
  @model(subscriptions: null) {
  name: String! @primaryKey
  metadata: AWSJSON
  employees: [Employee] @manyToMany(relationName: "EmployeeSME")
  HiringPosition: [HiringPosition]
    @manyToMany(relationName: "HiringPositionSME")
}

type Squad
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  name: String! @primaryKey
  budget: Int! @default(value: "0")
  squad_manager: Employee! @hasOne
  employee: [Employee] @hasMany
  project: [Project] @hasMany
  active: Boolean @default(value: "true")
  ProductUnit: ProductUnit @belongsTo
}

type ProductUnit
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  name: String! @primaryKey
  unit_manager: Employee! @hasOne
  squad: [Squad] @hasMany
  active: Boolean @default(value: "true")
}

type Guild
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  name: String! @primaryKey
  guild_manager: Employee! @hasOne
  employee: [Employee] @hasMany
  active: Boolean @default(value: "true")
}

type Client
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  name: String!
  projects: [Project] @hasMany
}

type ProjectBuckets {
  name: String
  hours: Int
  cost: Float
  isActive: Boolean
  reminder: Boolean
}

enum ProjectType {
  INTERNAL
  EXTERNAL
}

type Project
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  name: String!
  notion_dashboard_link: AWSURL
  notion_backlog_link: AWSURL
  squad: Squad! @belongsTo
  employee_project_allocation: [EmployeeProjectAllocation]! @hasMany
  client: Client! @belongsTo
  mrr: Int! @default(value: "0")
  fixed_cost: Int! @default(value: "0")
  budget: Int! @default(value: "0")
  project_buckets: [ProjectBuckets]
  start_time: AWSDateTime!
  end_time: AWSDateTime
  product_strategist: Employee! @hasOne
  product_manager: Employee! @hasOne
  client_POC: String
  status: PROJECT_STATUS!
  dev_principal: Employee! @hasOne
  time_tracking_required: Boolean!
  notes: [AWSJSON]
  extra_links: [String]
  project_history: [ProjectHistory] @hasMany
  jiraEvents: [JiraEvent] @hasMany
  jiraEventsHistory: [JiraEventHistory] @hasMany
  meetings: [ProjectMeetings] @hasMany(fields: ["id"], indexName: "byProjectID")
  ProjectTag: [ProjectTag] @manyToMany(relationName: "TagProjectRelation")
  project_type: ProjectType @default(value: "EXTERNAL")
  internal_slack_channel_id: String
  external_slack_channel_id: String
  project_passwords: [ProjectPasswords] @hasMany
  jiraProjectId: String
    @index(name: "byJiraProjectId", queryField: "ProjectByJiraProjectId") ## 10024
  promptConfigurations: AWSJSON
  bursts: [AWSJSON]
  potential_downgrade_date: String
  cancellation_date: String
  project_lead: Employee @hasOne
  logo: String
  domain: String # Healthcare, Fintech, etc.
  toolsUsed: [String] # Tools used in the project
  techStack: [String] # Tech stack used in the project
  project_app_type: String # Web, Mobile, etc.
  design_link: String # Link to design files or prototypes
  project_notes: [ProjectNote] @hasMany
}

type ProjectNote
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  Project: Project @belongsTo
  note: String!
  date: String
  addedby: Employee @hasOne
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

type ProjectTag
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  title: String
  Project: [Project] @manyToMany(relationName: "TagProjectRelation")
}

type EmployeeProjectAllocation
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  allocation: Int! @default(value: "0")
  employee: Employee! @belongsTo
  project: Project! @belongsTo
  title: String!
}

type ProjectPasswords
  @model(subscriptions: null)
  @auth(
    rules: [
      {
        allow: groups
        groups: ["Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
    ]
  ) {
  id: ID
  projectId: ID! @index(name: "byProjectID", queryField: "PasswordsByProjectID")
  project: Project @belongsTo(fields: ["projectId"])
  passwordKey: String
  encryptedPassword: String!
  createdAt: AWSDateTime
  employees: [Employee]
    @manyToMany(relationName: "ProjectPasswordEmployeesAccess")
}

type TimeSheet  # need lambda authorizer here
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "employeeTimesheet_entriesId"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
          "TechnicalManager"
        ]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  start_time: AWSTimestamp!
  end_time: AWSTimestamp!
  employeeID: String!
    @index(
      name: "timeSheetByEmployee"
      queryField: "timeSheetByEmployee"
      sortKeyFields: ["createdAt"]
    )
  timeSheetProjectId: ID!
    @index(
      name: "timeSheetByProject"
      queryField: "timeSheetByProject"
      sortKeyFields: ["createdAt"]
    )
  projectBucket: String
  employee: Employee! @belongsTo(fields: ["employeeID"])
  project: Project! @hasOne
  description: String!
  createdAt: String
  approval_status: String
    @default(value: "pending")
    @index(
      name: "timeSheetByStatus"
      queryField: "timeSheetByStatus"
      sortKeyFields: ["createdAt"]
    )
  approved_by: Employee @hasOne
  comments: [String]
  related_notifications: [ID]
}

type MBOAssignedGoal {
  category: String
  goal: String
  weightage: Int
  note: String
  updatedAt: String
  evaluated: Int
  order: Int
  customSuccessCriteria: String
  purpose: String
}

type MBO  # need lambda authorizer here
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "employeeMbosId"
        identityClaim: "email"
        operations: [read, update]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "TechnicalManager"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model {
  id: ID!
  year: Int!
  quarter: Int!
  employee: Employee! @belongsTo
  grade: String
  hard_skill: [String]
  hard_skill_grade: String
  soft_skill: [String]
  soft_skill_grade: String
  trainings: [String]
  target: [String]
  comments: String
  status: String
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: [
            "Hr"
            "Executive"
            "UnitTeamLeader"
            "SquadLeader"
            "TechnicalManager"
            "Recruiter"
          ]
          operations: [create, read, update, delete]
        }
        {
          allow: owner
          ownerField: "employeeMbosId"
          identityClaim: "email"
          operations: [read, update]
        }
      ]
    )
  is_skill_published: Boolean
  is_grade_published: Boolean
  is_comment_published: Boolean
  employeeMbosId: String
    @index(
      name: "MBOByEmployee"
      queryField: "GetMBOByEmployee"
      sortKeyFields: ["createdAt"]
    )
  draftValues: AWSJSON
  lastUpdatedBy: Employee @hasOne
  addedBy: Employee @hasOne
  createdAt: String
  goals: [MBOAssignedGoal]
}

type MBOGoal
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader"]
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["SquadLeader", "TechnicalManager", "Recruiter"]
        operations: [read]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  role: String!
  experienceRange: [Int] # save in months
  hardSkills: [String]
  softSkills: [String]
}

type MBOGoalsV2
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader"]
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["SquadLeader", "TechnicalManager", "Recruiter"]
        operations: [read]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  role: String!
  experienceRange: Int!
  category: String!
  goal: String!
  purpose: String!
  note: String
}

type NoticeBoard
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model {
  id: ID!
  title: String!
  description: String!
  expiry: AWSTimestamp!
  from: Employee! @hasOne
  type: String
  metaData: AWSJSON
  reactions: AWSJSON
}

type LeaveNotice
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "Recruiter"]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "leaveNoticeToId"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
    ]
  )
  @model {
  id: ID!
  from: Employee! @hasOne
  leaveNoticeToId: String!
  description: String
  title: String!
  expiry: AWSTimestamp! @ttl
}

type Wish
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "Recruiter"]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "wishToId"
        identityClaim: "email"
        operations: [read]
      }
      {
        allow: owner
        ownerField: "wishFromId"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
    ]
  )
  @model {
  id: ID!
  from: Employee! @hasOne
  wishToId: String!
  comments: String!
  expiry: AWSTimestamp! @ttl
}

type Quotation
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Executive"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  project: String!
  confidence: String
  metadata: AWSJSON
  active: Boolean!
  quotationCosts: [QuotationCost] @hasMany
  margin: AWSJSON
  EmployeeTimeAllocationBooking: [EmployeeTimeAllocationBooking] @hasMany
  proposedSOW: String
  signedSOW: String
  version: [String]
  client: Client @hasOne
}

type QuotationItem
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Executive"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  position: String!
  hourly_rate: Int!
  country: String
}
# compulsory fields, default, connections, auth

type QuotationCost
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Executive"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  position: String!
  quotationItem: QuotationItem! @hasOne
  hours: Int!
  version: String @default(value: "1")
  approved_hiring: Boolean @default(value: "false")
  quotationQuotationCostsId: ID!
    @index(
      name: "QuotationCostByQuotation"
      queryField: "GetQuotationCostsByQuotationID"
      sortKeyFields: ["createdAt", "version"]
    )
  createdAt: String
}
# compulsory fields, default, connections, auth

type Budget
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  amount: Int!
  start_date: AWSDate!
  end_date: AWSDate!
  name: String!
  expenses: [Expense] @hasMany
}

type Expense
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  amount: Int!
  date: AWSDate!
  title: String!
  description: String
  type: String!
  invoice: String
  # if expense type is individual
  employee: Employee @hasOne
  budget: Budget @belongsTo
}

enum FORCASTING_FLOW_TYPE {
  IN
  OUT
}

type Forcasting
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Executive"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  amount: Int!
  start_time: AWSDate!
  end_time: AWSDate!
  title: String!
  flow: FORCASTING_FLOW_TYPE!
  created_by: Employee @hasOne
}

type InventoryItem
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "ITAdmin"]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "employeeInventoriesId"
        identityClaim: "email"
        operations: [read]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  title: String!
  serial_number: String
  price: Float!
  configuration: String
  purchased_date: AWSTimestamp!
  assigned_date: AWSTimestamp
  type: String
  assigned_to: Employee @belongsTo
  invoice: String
  warranty_period: Int
  asset_tag: String
  vendor_name: String
}

type InventoryStock
  @model(subscriptions: null)
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "ITAdmin"]
        operations: [create, read, update, delete]
      }
    ]
  ) {
  id: ID!
  title: String!
  vendor_name: String
  price_per_item: Float!
  stock_count: Int!
  configurations: String
  items: [InventoryStockItem]
}

type InventoryStockItem {
  asset_tag: String!
  serial_number: String!
  purchased_date: AWSDateTime!
  invoices: String
  warranty_period: Int!
}

# type SaasSubscription ## commented as it is not used in the current schema
#   @auth(
#     rules: [
#       {
#         allow: private
#         provider: iam
#         operations: [create, read, update, delete]
#       }
#       {
#         allow: groups
#         groups: ["Hr", "Executive"]
#         operations: [create, read, update, delete]
#       }
#     ]
#   )
#   @model(subscriptions: null) {
#   id: ID!
#   title: String!
#   amount: Int!
#   start_time: AWSDate!
#   end_time: AWSDate!
#   provider: String!
#   currency: CURRENCY!
#   assigned_to: Employee @belongsTo
# }

type MeetingSchedule
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "SquadLeader"
          "UnitTeamLeader"
          "TechnicalManager"
        ]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "owner"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  owner: String!
    @index(
      name: "MeetingScheduleByOwner"
      queryField: "GetMeetingScheduleByOwner"
      sortKeyFields: ["createdAt"]
    )
  ownerDetails: Employee @hasOne(fields: ["owner"])
  title: String!
  id: ID!
  type: String
  meetingSchedules_items: [MeetingSchedulesItem]
    @hasMany(indexName: "byMeetingSchedule", fields: ["id"])
  createdAt: String
}

type MeetingSchedulesItem
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "SquadLeader"
          "UnitTeamLeader"
          "TechnicalManager"
        ]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "ownerID"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  with: String!
  withDetails: Employee @hasOne(fields: ["with"])
  ownerID: String!
    @index(
      name: "MeetingScheduleByOwner"
      queryField: "GetMeetingScheduleDetailsByOwner"
      sortKeyFields: ["createdAt"]
    )
  ownerIDDetails: Employee @hasOne(fields: ["ownerID"])
  scheduled_at: String!
  mom: String
  zoomMeetingID: String
    @index(
      name: "byZoomMeetingID"
      queryField: "getMeetingScheduleItemByZoomId"
    )
  meetingScheduleId: ID! @index(name: "byMeetingSchedule")
  createdAt: String
}

# Project History
enum COST_TYPE {
  MRR
  FIXED_COST
}

type ProjectHistory
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Executive", "UnitTeamLeader"]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "ownerID"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  Project: Project @belongsTo
  start_time: AWSDateTime
  end_time: AWSDateTime
  employee_project_allocation: AWSJSON!
  costType: COST_TYPE
  cost: Int! @default(value: "0")
  product_strategist: Employee! @hasOne
  product_manager: Employee! @hasOne
  client_POC: String
  dev_principal: Employee! @hasOne
}

enum JobType {
  FULL_TIME
  PART_TIME
}

enum Team {
  PRODUCT
  ENGINEERING
}

type HiringPosition
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  position: Designation @hasOne
  open: Boolean @default(value: "true")
  candidates: [Candidate] @hasMany
  hired_candidate: Candidate @hasOne
  hired_date: AWSDateTime
  description: String
  base_salary: Int
  department: String
  deadline: AWSDate # for HR
  potential_closeDate: AWSDate # for HR
  title: String
  SME: [SME] @manyToMany(relationName: "HiringPositionSME")
  skills: [Skill] @manyToMany(relationName: "HiringPositionSkill")
  guild: Guild @hasOne
  potential_squad: Squad @hasOne
  status: String
    @index(
      name: "listJobPositionByStatus"
      queryField: "listJobPositionByStatus"
    )
  required_experience: Float
  potential_project: String
  total_position_budget: Int
  key_responsibility: String
  qualification: String
  additional_info: String
  location: String
  no_of_vacancy: Int
  team: Team
  priority: Int @default(value: "1")
  isPracticalNeeded: Boolean
  job_type: JobType
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter", "UnitTeamLeader"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [] }
      ]
    )
  expected_position_budget: Int
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter", "UnitTeamLeader"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [] }
      ]
    )
  allocated_position_budget: Int
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter", "UnitTeamLeader"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [] }
      ]
    )
  owner: Employee @hasOne(fields: ["hiringPositionOwnerId"])
  hiringPositionOwnerId: String
    @index(name: "listJobPositionByOwner", queryField: "listJobPositionByOwner")
  notes: [NoteType]
  benchEmployeeSuggestion: AWSJSON
}

type NoteType {
  #for Easy Access no need to make many to many relation between multiple tables
  owner_name: String
  owner_email: String!

  note: String!
  createdAt: String
}

enum CANDIDATE_HIRING_STATUS {
  HIRED
  INTERVIEWED
  INITIATED
}

type Candidate
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
      {
        allow: owner
        ownerField: "candidateReffered_byId"
        identityClaim: "email"
        operations: [read]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  first_name: String
  last_name: String
  email: String
    @index(name: "getcandidateByEmail", queryField: "getcandidateByEmail")
  mobile: String
  employee_relation_with_candidate: String
  position: HiringPosition @belongsTo(fields: ["hiringPositionCandidatesId"])
  hiringPositionCandidatesId: ID
    @index(name: "getCandidateByJobPost", queryField: "getCandidateByJobPost")
  eligible_hiring_positions: [ID]
  reffered_by: Employee @hasOne(fields: ["candidateReffered_byId"])
  candidateReffered_byId: String
    @index(
      name: "getCandidatesByRefferalEmail"
      queryField: "getCandidatesByRefferalEmail"
    )
  location: String
  linkedin_profile: String
  interview: [Interview] @hasMany
  confidence: Float
  status: String
    @index(name: "listCandidatesByStatus", queryField: "listCandidatesByStatus")
  cover_letter: String
  exeperience: Float
  notice_period: Int
  portfolio_link: String
  github_link: String
  other_link: String
  s3_resume_key: String
    @index(
      name: "getCandidateByResumeKey"
      queryField: "getCandidateByResumeKey"
    )
  human_integration_done: Boolean @default(value: "false") ## check if any HR had updated it!!!
  education: AWSJSON
  ready_for_relocation: Boolean
  contact_time: [String]
  past_exeperience: AWSJSON
  candidate_prescreen_done: Boolean
  skills: [Skill] @manyToMany(relationName: "CandidateSkill")
  candidate_source: String
  current_ctc: Int
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter", "UnitTeamLeader"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [] }
      ]
    )
  expected_ctc: Int
    @auth(
      rules: [
        {
          allow: private
          provider: iam
          operations: [create, read, update, delete]
        }
        {
          allow: groups
          groups: ["Hr", "Executive", "Recruiter", "UnitTeamLeader"]
          operations: [create, read, update, delete]
        }
        { allow: private, provider: userPools, operations: [] }
      ]
    )
  notes: [NoteType]
  aiInterviewLocked: Boolean @default(value: "false")
  aiInterviewAnswers: AWSJSON
  dnd_enabled: Boolean @default(value: "false")
  email_sent_count: Int @default(value: "0")
  last_email_sent: AWSTimestamp
  externalReferralsReferralsId: ID
    @index(
      name: "listCandidatesByExternalReferID"
      queryField: "listCandidatesByExternalReferID"
    )
  external_referral_detailed: ExternalReferrals @belongsTo
  is_referral_bonus_released: Boolean @default(value: "false") # useful for internal and extrenal referrals Tracking
}

type Interview
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
      {
        allow: owner
        ownerField: "interviewTaken_byId"
        identityClaim: "email"
        operations: [read, update]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  title: String # Like Round1, Round 2
  status: String
    @default(value: "Interviewer Time Selection")
    @index(name: "listInterviewsByStatus", queryField: "listInterviewsByStatus")
  interview_date: String
  interview_recordings: String
  interviewTaken_byId: String
    @index(
      name: "listInterviewsByInterviewer"
      queryField: "listInterviewsByInterviewer"
    )
  taken_by: Employee @hasOne(fields: ["interviewTaken_byId"])
  candiate: Candidate @belongsTo
  employee_feedback: AWSJSON
  candiate_feedback: AWSJSON
  overall_score_candidate: Float
  overall_score_employee: Float
  overall_words: String
  panelists: [Employee] @manyToMany(relationName: "InterviewPanelists")
  notes: [NoteType]

  added_by_email: String
  added_by: Employee @hasOne(fields: ["added_by_email"])
  interviewer_time: [AWSTimestamp]
  candidate_time: [AWSTimestamp] ## if selected then need to show other time selected flow.
  selected_time: AWSTimestamp # candidate will choose time selected by interviewer
  ZoomMeetingID: String
    @index(
      name: "listInterviewsByZoomMeetingID"
      queryField: "listInterviewsByZoomMeetingID"
    )
  aiFeedback: AWSJSON
}

type EmployeeTimeAllocationBooking
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive"]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "leaveNoticeToId"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
    ]
  )
  @model {
  id: ID!
  employee: Employee! @hasOne
  allocation: Int! @default(value: "0")
  Quotation: Quotation! @belongsTo
  title: String!
}

type FeedBackConversation
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      { allow: private, operations: [create, read] }
      { allow: owner, operations: [create, read, update, delete] }
    ]
  )
  @model {
  id: ID!
  to: String!
    @index(
      name: "FeedbackByEmployee"
      queryField: "FeedbackByEmployee"
      sortKeyFields: ["createdAt"]
    )
  ToEmployee: Employee! @hasOne(fields: ["to"])
  from: String
    @auth(
      rules: [
        { allow: private, provider: iam, operations: [create] }
        { allow: private, operations: [create] }
      ]
    )

  messages: [FeedBackMessage] @hasMany
  title: String!
  description: String
  createdAt: String
}

type FeedBackMessage
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      { allow: private, operations: [create, read, update, delete] }
    ]
  )
  @model {
  id: ID!
  ConversationID: ID!
  # Conversation: FeedBackConversation! @belongsTo(fields: ["ConversationID"])
  Message: String!
  isRead: Boolean @default(value: "false")
  isFromOpponent: Boolean # This will set to true if message is not from Conversation Starter
}

type NotificationTokens
  @model(subscriptions: null)
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      { allow: private, operations: [create, read, update, delete] }
    ]
  ) {
  employeeEmail: String! @primaryKey
  notificationToken: String
}

type Notification
  @model
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
      {
        allow: private
        provider: userPools
        operations: [read, create, update, delete]
      }
    ]
  ) {
  id: ID!
  ToAccount: String
    @index(
      name: "GetNotificationByUser"
      queryField: "GetNotificationByUser"
      sortKeyFields: ["createdAt"]
    )
  To: Employee @hasOne(fields: ["ToAccount"])
  AdminNotification: Boolean @default(value: "false") #if Admin has Created otherwise it is System Generated
  Type: String
  Message: String!
  Metadata: AWSJSON
  isRead: Boolean @default(value: "false")
  isEmailSent: Boolean @default(value: "false")
  createdAt: String
  timeSensitive: Boolean @default(value: "false")
}

type Product
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "admin"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model {
  id: ID!
  title: String!
  description: String!
  price: Int!
  assets: [String]! # can be -> String!
}

type Order
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "admin"]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "orderEmployeeId"
        identityClaim: "email"
        operations: [read]
      }
    ]
  )
  @model {
  id: ID!
  product: Product @hasOne
  employee: Employee @hasOne
  status: String
  order_complete_picture: String
  ai_description: String
}

type CoinTransaction
  @model
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "admin"]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "employeeCoins_transactionsId"
        identityClaim: "email"
        operations: [read]
      }
    ]
  ) {
  id: ID!
  coins: Int!
  adjustment_type: LEAVE_ADJUSTMENT_TYPE! # using same as its already defined.
  employee: Employee! @belongsTo
  transaction_date: AWSDateTime
  approved_by: Employee @hasOne
  balance: Int
  comment: String
}

# To Save Documents
type DocumentMaster
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "Recruiter"]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "owner"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
    ]
  )
  @model {
  id: ID!
  ## Candidates, HiringPosition, Employee Etc.
  Type: String!
    @index(name: "getDocumentByType", queryField: "getDocumentByType")
  ## ID of Type so if its for candidate then save candidate id.
  Relation_ID: String!
    @index(
      name: "getDocumentByRelationID"
      queryField: "getDocumentByRelationID"
    )
  S3_Key: String!
  owner: String!
    @index(name: "getDocumentByOwner", queryField: "getDocumentByOwner")
  ownerDetails: Employee @hasOne(fields: ["owner"])
  metaData: AWSJSON
}

enum OPERATION_TYPE {
  CREATE
  UPDATE
  DELETE
  READ
  LIST
}

# To Save Event Logs
type EventLogsMaster
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "Recruiter"]
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "actionBy"
        identityClaim: "email"
        operations: [create, read, update, delete]
      }
    ]
  )
  @model {
  id: ID!
  ## Password, Link Etc.
  Type: String!
    @index(
      name: "getEventLogsByType"
      queryField: "getEventLogsByType"
      sortKeyFields: ["createdAt"]
    )
  ## ID of Type so if its for passwords etc.
  Relation_ID: String!
    @index(
      name: "getEventLogsByRelationID"
      queryField: "getEventLogsByRelationID"
      sortKeyFields: ["createdAt"]
    )
  operationType: OPERATION_TYPE
  actionBy: String
  actionByDetails: Employee @hasOne(fields: ["actionBy"])
  metaData: AWSJSON
  createdAt: String
  expiry: AWSTimestamp! @ttl
}

type AIAssessmentFeedback
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive"]
        operations: [read, update, delete]
      }
    ]
  )
  @model {
  id: ID!
  candidate: Candidate @hasOne

  overallExperience: Int # Rating from 1 to 5
  interviewLength: String # Too long / Too short / Just right
  technicalGlitches: Boolean # True if glitches occurred, false otherwise
  glitchDetails: String # Description of glitches if technicalGlitches is true
  questionQuality: Int # Rating from 1 to 5
  sufficientTime: Boolean # True if time given was sufficient
  recommend: Boolean # True if candidate would recommend the process
  instructionsClarity: Boolean # True if instructions were clear
  suggestions: String # Open-ended suggestions from the candidate
}

type ZoomMeeting
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive"]
        operations: [read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model {
  id: ID!
  host: Employee! @belongsTo(fields: ["hostEmployeeID"])
  hostEmployeeID: String!
  meetingid: String!
    @index(name: "byMeetingID", queryField: "getZoomMeetingsByMeetingID")
  s3_key: String
  internalParticipants: [ZoomMeetingInternalParticipant]
    @hasMany(indexName: "byMeeting", fields: ["id"])
  externalParticipants: [ZoomMeetingExternalParticipant]
    @hasMany(indexName: "byMeeting", fields: ["id"])
  start_time: AWSTimestamp
  topic: String
}

type ZoomMeetingInternalParticipant
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model {
  id: ID!
  zoomMeetingID: ID!
    @index(name: "byMeeting", queryField: "listInternalParticipantsByMeeting")
  employeeID: String!
    @index(name: "byEmployee", queryField: "listInternalParticipantsByEmployee")
  employee: Employee! @belongsTo(fields: ["employeeID"])
  zoomMeeting: ZoomMeeting! @belongsTo(fields: ["zoomMeetingID"])
  join_time: AWSTimestamp
  leave_time: AWSTimestamp
  status: String
}

type ZoomMeetingExternalParticipant
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model {
  id: ID!
  zoomMeetingID: ID!
    @index(name: "byMeeting", queryField: "listExternalParticipantsByMeeting")
  zoomMeeting: ZoomMeeting! @belongsTo(fields: ["zoomMeetingID"])
  name: String
  join_time: AWSTimestamp
  leave_time: AWSTimestamp
  status: String
}

type GoogleCalendarToken
  @model(subscriptions: null)
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: owner
        ownerField: "email"
        identityClaim: "email"
        operations: [read]
      }
    ]
  ) {
  email: String! @primaryKey
  employee: Employee @hasOne(fields: ["email"])
  access_token: String! # Google API access token
  refresh_token: String! # Google API refresh token
  expires_at: AWSDateTime! # Token expiration date
  grant_code: String!
}

enum MEETING_TYPE {
  WEEKLY_CLIENT_CALL
  TRAINING_SESSION
  PROJECT_DISCUSSION
  SPRINT_GROOMING
  SPRINT_PLANNING
  SPRINT_RETRO
  AD_HOC
  OTHER
}

# Can save all meetings happened or going to happen for any project including recurring or adhoc
# unsaid rule of one meetingid <-> one project
type ProjectMeetings
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
      {
        allow: private
        provider: userPools
        operations: [read, update, delete, create]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  type: MEETING_TYPE!
  meetingID: String!
    @index(name: "byMeetingID", queryField: "getProjectMeetingsByMeetingID")
  isRecurring: Boolean
  projectID: ID!
    @index(name: "byProjectID", queryField: "getProjectMeetingsByProjectID")
  project: Project! @belongsTo(fields: ["projectID"])
  isExternal: Boolean
  projectMeetingsNotes: [ProjectMeetingsNote] @hasMany
  internalEmployees: [Employee]!
    @manyToMany(relationName: "ProjectMeetingEmployees")
  title: String
}

# For saving Notes ofr any meeting that happend with the project.
type ProjectMeetingsNote
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  notes: AWSJSON
  zoomMeetingID: ID!
  zoomMeeting: ZoomMeeting @hasOne(fields: ["zoomMeetingID"])
  projectMeeting: ProjectMeetings @belongsTo
  projectID: String
    @index(
      name: "ProjectMeetingNotesByProjectID"
      queryField: "GetProjectMeetingNotesByProjectID"
      sortKeyFields: ["createdAt"]
    )
  createdAt: String
  actionItems: [ProjectMeetingActionItem] @hasMany
  redFlags: [ProjectMeetingRedFlag] @hasMany
}

# For saving Notes ofr any meeting that happend with the project.
type ProjectMeetingActionItem
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  title: String
  completed: Boolean @default(value: "false")
  ProjectMeetingsNote: ProjectMeetingsNote @belongsTo
}

# For saving red flags for any meeting that happend with the project.
type ProjectMeetingRedFlag
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  title: String
  uniqueID: String
  completed: Boolean @default(value: "false")
  ProjectMeetingsNote: ProjectMeetingsNote @belongsTo
  status: TICKET_STATUS
    @default(value: "PENDING")
    @index(
      name: "redflagByStatus"
      queryField: "GetRedflagByStatus"
      sortKeyFields: ["createdAt"]
    )
  projectID: String
    @index(
      name: "ByProjectID"
      queryField: "GetRedFlagByProjectID"
      sortKeyFields: ["createdAt"]
    )
  assignee: Employee @hasOne
  comments: [ProjectMeetingRedFlagComment] @hasMany
  createdAt: String
}

# For saving red flags for any meeting that happend with the project.
type ProjectMeetingRedFlagComment
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  comment: String
  ProjectMeetingRedFlag: ProjectMeetingRedFlag @belongsTo
  employee: Employee @hasOne
}

# For saving Notes for any Employee can be usefull for creating persona.
type EmployeeMeetings
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  aiFeedback: AWSJSON
  email: String
  zoomMeetingID: ID!
  zoomMeeting: ZoomMeeting @hasOne(fields: ["zoomMeetingID"])
  employee: Employee @hasOne(fields: ["email"])
}

type CodeQualityAssessment
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "TechnicalManager"
          "SquadLeader"
        ]
        operations: [read]
      }
      {
        allow: owner
        ownerField: "email"
        identityClaim: "email"
        operations: [read]
      }
    ]
  )
  @model {
  id: ID!
  pullRequestLink: String! # ID of the associated Pull Request
  changeNumber: Int!
  patchSetNumber: Int! # ID of the associated revision
  areCodeChangesOptimized: Boolean # True if the code changes are optimized
  areCodeChangesRelative: Boolean # True if the changes are relevant and necessary
  isCodeFormatted: Boolean # True if code adheres to formatting guidelines
  isCodeWellWritten: Boolean # True if code is clear and understandable
  areCommentsWritten: Boolean # True if adequate comments are present
  score: Int # Overall quality score out of 10
  isCommitMessageWellWritten: Boolean # True if the commit message is well-written
  isNamingConventionFollowed: Boolean # True if naming conventions are followed
  areThereAnySpellingMistakes: Boolean # True if no spelling or grammatical errors
  securityConcernsAny: Boolean # True if security concerns exist
  sizeInsertions: Int # Number of insertions
  feedbackDetails: AWSJSON # JSON field to store detailed strings like:
  # {
  #   "scoreReason": "Reason for the given score",
  #   "missingElements": "Details of missing elements or improvements",
  #   "loopholes": "Potential loopholes or issues",
  #   "securityConcernsReason": "Explanation of security concerns if any"
  # }
  email: String!
    @index(
      name: "byEmail"
      queryField: "getCodeQualityAssessmentByEmail"
      sortKeyFields: ["createdAt"]
    )
  employee: Employee @hasOne(fields: ["email"])
  projectID: ID!
    @index(
      name: "byProjectID"
      queryField: "GetCodeQualityAssessmentByProjectID"
      sortKeyFields: ["createdAt"]
    )
  project: Project @hasOne(fields: ["projectID"])
  analyticsStatus: String
    @default(value: "PENDING")
    @index(
      name: "byAnalyticsSatatus"
      queryField: "GetCodeQualityAssessmentByAnalyticsStatus"
      sortKeyFields: ["createdAt"]
    ) ## PENDING Or DONE
  createdAt: String
  isCodeDuplicated: Boolean # True if security concerns exist
  areConstantsDefinedCentrally: Boolean # True if security concerns exist
  cyclomaticComplexityScore: Int # True if security concerns exist
  cyclomaticComplexityReason: String # True if security concerns exist
  isCodeDuplicatedReason: String # True if security concerns exist
  areConstantsDefinedCentrallyReason: String # True if security concerns exist
  isCodeModular: Boolean # True if security concerns exist
  isCodeModularReason: String # True if security concerns exist,
  isLoggingDoneProperly: Boolean # True if security concerns exist
  isLoggingDoneProperlyReason: String # True if security concerns exist
}

# For External Referrals Details
type ExternalReferrals
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: [
          "Hr"
          "Executive"
          "UnitTeamLeader"
          "SquadLeader"
          "Recruiter"
        ]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  email: String
    @index(name: "getReferrerByEmail", queryField: "getReferrerByEmail")
  name: String
  contactNumber: String
  isCandidate: Candidate @hasOne(fields: ["id"])

  referrals: [Candidate] @hasMany
}

enum KUDOS_STATUS {
  SUBMITTED
  APPROVED
  REJECTED
}

type Kudos
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive"]
        operations: [create, read, update]
      }
      { allow: private, provider: userPools, operations: [create, read] }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  recipient: [Employee] @manyToMany(relationName: "EmployeeKudos")
  senderId: String
    @index(
      name: "getKudosBySenderID"
      queryField: "getKudosBySenderID"
      sortKeyFields: ["createdAt"]
    )
  senderDetails: Employee @hasOne(fields: ["senderId"])
  message: String!
  category: String!
  updatedAt: AWSDateTime!
  createdAt: String!
  status: KUDOS_STATUS
    @default(value: "SUBMITTED")
    @index(
      name: "getKudosByStatus"
      queryField: "getKudosByStatus"
      sortKeyFields: ["createdAt"]
    )
  reactions: AWSJSON
}

enum LINK_STATUS {
  ACTIVE
  EXPIRED
  DISABLED
}

type Links
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: private
        groups: ["Hr", "Executive"]
        operations: [create, read, update]
      }
      { allow: private, provider: userPools, operations: [create, read] }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  metadata: AWSJSON
  reusable: Boolean @default(value: "false")
  sharedWith: [AWSEmail]
  title: String
  validTill: AWSTimestamp
  Type: String! @index(name: "getLinksByType", queryField: "getLinksByType")
  ## ID of Type so if its for passwords etc.
  Relation_ID: String!
    @index(
      name: "getLinksByRelationID"
      queryField: "getLinksByRelationID"
      sortKeyFields: ["createdAt"]
    )
  status: LINK_STATUS @default(value: "ACTIVE")
  createdBy: String!
  createrDetails: Employee @hasOne(fields: ["createdBy"])
  createdAt: AWSDateTime!
}

type Reactions
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: private
        provider: userPools
        operations: [create, read, delete]
      }
    ]
  )
  @model {
  id: ID!

  reactableId: ID!  # ID of the Notice, Kudos, etc.
    @index(
      name: "getReactionsByReactableId"
      queryField: "getReactionsByReactableId"
      sortKeyFields: ["createdAt"]
    )
  reactableType: String! # Notice, Kudos etc.
  reaction: String! # "😂", "🔥", etc.
  employee: String!
  employeeDetails: Employee @hasOne(fields: ["employee"])
  createdAt: AWSDateTime!
}

# JiraEventHistory: append-only table
type JiraEventHistory
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
    ]
  )
  @model(subscriptions: null) {
  id: ID!
  eventType: String!
  issueKey: ID
    @index(
      name: "byIssueKeyHistory"
      queryField: "getJiraEventHistoryByIssueKey"
    )
  issueSummary: String
  description: String
  descriptionQualityAI: AWSJSON # <-- Added field
  sprintId: String
  sprintName: String
  boardId: String
  boardName: String
  storyPoints: Float
  employeeEmail: String
    @index(
      name: "byEmployeeEmailHistory"
      queryField: "getJiraEventHistoryByEmployee"
    )
  employee: Employee @belongsTo(fields: ["employeeEmail"])
  issueCreatorEmail: String
    @index(
      name: "byIssueCreatorEmailHistory"
      queryField: "getJiraEventHistoryByCreator"
    )
  issueCreator: Employee @belongsTo(fields: ["issueCreatorEmail"])
  projectId: ID
    @index(
      name: "byProjectIdHistory"
      queryField: "getJiraEventHistoryByProject"
    )
  project: Project @belongsTo(fields: ["projectId"])
  parentIssueKey: String
    @index(
      name: "byParentIssueKeyHistory"
      queryField: "getJiraEventHistoryByParentIssueKey"
    )
  createdAt: AWSDateTime
  jiraEvent: JiraEvent @belongsTo(fields: ["issueKey"])
  issueType: IssueType # <-- Added field
  status: String
    @index(name: "byStatusHistory", queryField: "getJiraEventHistoryByStatus")
  priority: String
  severity: String
}

# JiraEvent: up-to-date table, issueKey is the primary key
type JiraEvent
  @auth(
    rules: [
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
      { allow: private, provider: userPools, operations: [read] }
    ]
  )
  @model {
  issueKey: ID! @primaryKey
  eventType: String!
  issueSummary: String
  description: String
  descriptionQualityAI: AWSJSON # <-- Added field
  sprintId: String
  sprintName: String
  boardId: String
  boardName: String
  storyPoints: Float
  employeeEmail: String
    @index(name: "byEmployeeEmail", queryField: "getJiraEventsByEmployee")
  employee: Employee @belongsTo(fields: ["employeeEmail"])
  issueCreatorEmail: String
    @index(name: "byIssueCreatorEmail", queryField: "getJiraEventsByCreator")
  issueCreator: Employee @belongsTo(fields: ["issueCreatorEmail"])
  jiraProjectId: String
  projectId: ID  ## This is for hub's project UUID
    @index(name: "byProjectId", queryField: "getJiraEventsByProject")
  project: Project @belongsTo(fields: ["projectId"])
  parentIssueKey: String
    @index(
      name: "byParentIssueKey"
      queryField: "getJiraEventsByParentIssueKey"
    )
  createdAt: AWSDateTime
  history: [JiraEventHistory] @hasMany(fields: ["issueKey"])
  issueType: IssueType # <-- Added field
  status: String @index(name: "byStatus", queryField: "getJiraEventsByStatus")
  priority: String
  severity: String
}

enum IssueType {
  EPIC
  BUG
  TASK
  STORY
  SUBTASK
  OTHER
}

type Group
  @model
  @auth(
    rules: [
      { allow: private, provider: iam }
      { allow: groups, groups: ["Executive"] }
    ]
  ) {
  id: ID!
  name: String!
  description: String
  permissions: AWSJSON
  assignments: [GroupAssignment] @hasMany
  leaderEmail: String
  leader: Employee @hasOne(fields: ["leaderEmail"])
}

type GroupAssignment
  @model
  @auth(
    rules: [
      { allow: private, provider: iam }
      { allow: groups, groups: ["Executive"] }
    ]
  ) {
  id: ID!
  userEmail: String!
    @index(name: "userEmail", queryField: "getEmployeesGroupAssignmentByEmail")
  user: Employee @hasOne(fields: ["userEmail"])

  groupId: ID!
    @index(
      name: "byGroupID"
      queryField: "getEmployeesGroupAssignmentByGroupID"
    )
  group: Group @belongsTo(fields: ["groupId"])
}

type Celebration
  @model
  @auth(
    rules: [
      # Everyone can read
      { allow: private, provider: userPools, operations: [read] }
      # Owner can create, update, read, delete their own
      {
        allow: owner
        ownerField: "employeeEmail"
        identityClaim: "email"
        operations: [create, update, read, delete]
      }
    ]
  ) {
  id: ID!
  title: String!
  note: String
  employeeEmail: String!
  createdAt: AWSDateTime!
  employee: Employee @hasOne(fields: ["employeeEmail"])
}

type ExecutionRule
  @model(subscriptions: null)
  @auth(
    rules: [
      { allow: private, provider: userPools, operations: [read] }
      {
        allow: private
        provider: iam
        operations: [create, read, update, delete]
      }
      {
        allow: groups
        groups: ["Hr", "Executive", "UnitTeamLeader", "SquadLeader"]
        operations: [create, read, update, delete]
      }
    ]
  ) {
  id: ID!
  title: String!
  description: String
  frequency: FREQUENCY
  allowedDelay: String # in days
  responsibleGroup: Group @hasOne
  authorityGroup: Group @hasOne
  completionType: COMPLETION_TYPE
  status: String
  ruleDefination: AWSJSON
}

type Executable
  @model(subscriptions: null)
  @auth(
    rules: [
      # Everyone can read
      { allow: private, provider: userPools, operations: [read] }
      # Owner can create, update, read, delete their own
      {
        allow: owner
        ownerField: "employeeEmail"
        identityClaim: "email"
        operations: [create, update, read, delete]
      }
    ]
  ) {
  id: ID!
  rule: ExecutionRule @hasOne
  expiry: AWSTimestamp! @ttl
  status: EXECUTABLE_STATUS
  metaData: AWSJSON
}

type ExecutableHistory
  @model(subscriptions: null)
  @auth(
    rules: [
      # Everyone can read
      { allow: private, provider: userPools, operations: [read] }
      # Owner can create, update, read, delete their own
      {
        allow: owner
        ownerField: "employeeEmail"
        identityClaim: "email"
        operations: [create, update, read, delete]
      }
    ]
  ) {
  id: ID!
  rule: ExecutionRule @hasOne
  status: EXECUTABLE_STATUS
  metaData: AWSJSON
}
