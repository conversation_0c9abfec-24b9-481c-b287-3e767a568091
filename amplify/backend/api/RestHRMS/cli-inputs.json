{"version": 1, "paths": {"/getSalarySlip": {"name": "/getSalarySlip", "lambdaFunction": "getSalarySlip", "permissions": {"setting": "private", "auth": ["read"], "groups": {"Executive": ["read"], "UnitTeamLeader": ["read"], "Hr": ["read"], "SquadLeader": ["read"], "TechnicalManager": ["read"], "ITAdmin": ["read"], "Recruiter": ["read"], "admin": ["read"]}}}, "/availableSalarySlips": {"name": "/availableSalarySlips", "lambdaFunction": "getAvailableSalarySlips", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"], "groups": {"Executive": ["create", "read", "update", "delete"], "UnitTeamLeader": ["create", "read", "update", "delete"], "Hr": ["create", "read", "update", "delete"], "SquadLeader": ["create", "read", "update", "delete"], "TechnicalManager": ["create", "read", "update", "delete"], "ITAdmin": ["create", "read", "update", "delete"], "Recruiter": ["create", "read", "update", "delete"], "admin": ["create", "read", "update", "delete"]}}}, "/JobPosition": {"name": "/JobPosition", "lambdaFunction": "getJobPosition", "permissions": {"setting": "open"}}, "/notice": {"name": "/notice", "lambdaFunction": "Notice", "permissions": {"groups": {"Executive": ["create", "read", "update", "delete"], "Hr": ["create", "read", "update", "delete"]}, "setting": "private"}}, "/interview": {"name": "/interview", "lambdaFunction": "interview", "permissions": {"setting": "open"}}, "/zoom": {"name": "/zoom", "lambdaFunction": "zoomWebhookResponder", "permissions": {"setting": "open"}}, "/jobPostSharing": {"name": "/jobPostSharing", "lambdaFunction": "jobPostSharingGenerator", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"], "groups": {"Executive": ["create", "read", "update", "delete"], "UnitTeamLeader": ["create", "read", "update", "delete"], "Hr": ["create", "read", "update", "delete"], "SquadLeader": ["create", "read", "update", "delete"], "TechnicalManager": ["create", "read", "update", "delete"], "admin": ["create", "read", "update", "delete"], "ITAdmin": ["create", "read", "update", "delete"], "Recruiter": ["create", "read", "update", "delete"]}}}, "/calendar": {"name": "/calendar", "lambdaFunction": "GoogleCalendarHelper", "permissions": {"groups": {"Executive": ["create", "read", "update", "delete"], "UnitTeamLeader": ["create", "read", "update", "delete"], "Hr": ["create", "read", "update", "delete"], "SquadLeader": ["create", "read", "update", "delete"], "TechnicalManager": ["create", "read", "update", "delete"], "admin": ["create", "read", "update", "delete"], "ITAdmin": ["create", "read", "update", "delete"], "Recruiter": ["create", "read", "update", "delete"]}, "setting": "private"}}, "/gerritWebhook": {"name": "/gerritWebhook", "lambdaFunction": "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "permissions": {"setting": "open"}}, "/reports": {"name": "/reports", "lambdaFunction": "updateReportsFromAPI", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"], "groups": {"Executive": ["create", "read", "update", "delete"], "UnitTeamLeader": ["create", "read", "update", "delete"], "Hr": ["create", "read", "update", "delete"], "Recruiter": ["create", "read", "update", "delete"]}}}, "/public": {"name": "/public", "lambdaFunction": "PublicAPIWorker", "permissions": {"setting": "open"}}, "/api": {"name": "/api", "lambdaFunction": "API", "permissions": {"setting": "private", "auth": ["create", "read", "update", "delete"], "groups": {"Executive": ["create", "read", "update", "delete"], "UnitTeamLeader": ["create", "read", "update", "delete"], "Hr": ["create", "read", "update", "delete"], "SquadLeader": ["create", "read", "update", "delete"], "TechnicalManager": ["create", "read", "update", "delete"], "admin": ["create", "read", "update", "delete"], "ITAdmin": ["create", "read", "update", "delete"], "Recruiter": ["create", "read", "update", "delete"]}}}}}