/* Amplify Params - DO NOT EDIT
  API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT
  API_YORKHRMSGRAPHQL_GRAPHQLAPIIDOUTPUT
  ENV
  REGION
  STORAGE_DATA_BUCKETNAME
Amplify Params - DO NOT EDIT */

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
const { wrapHandler, captureError } = require("/opt/sentryWrapper");

const AWS = require("aws-sdk");
const queries = require("./appQueries.js");
const s3 = new AWS.S3({
  region: process.env.REGION, // Ensure the region is correctly set
});

const {
  getLatestEmployeeData,
  generateGerritAnalytics,
  calculateJiraEventAnalytics
} = require("./utils.js");

const statusOrder = [
  "Hired",
  "Waiting for Approval",
  "Interview Done",
  "Interview",
  "Ready for Interview",
  "Owner Approval",
  "AI Assessment",
  "HR Intervention",
];

const S3KeysPerReportType = {
  "candidate-per-jobpost": "hiring/candidates-count-per-job-position",
  "code-quality": "code-quality/overview",
  "project-redflag": "projects/redflags-overview-per-project",
  "code-quality-by-employee": "code-quality/employees/overview",
  "jira-analytics": "jira/analytics/overview"
};

const analyticsStatus = {
  PENDING: "PENDING",
  COMPLETE: "COMPLETE",
};

const initialCodeAssessment = {
  codeReviewMetrics: {
    averageQualityScore: 0,
    percentagePRsWithIssues: 0,
    percentagePRsWithSecurityConcerns: 0,
    totalPRsAnalyzed: 0,
  },
  categorizePRsByScoreData: [
    {
      category: "0-4",
      count: 0,
    },
    {
      category: "5-7",
      count: 0,
    },
    {
      category: "8-10",
      count: 0,
    },
  ],
  countPRsWithIssuesData: [
    {
      issue: "Missing Comments",
      count: 0,
    },
    {
      issue: "Unoptimized Changes",
      count: 0,
    },
    {
      issue: "Irrelevant Changes",
      count: 0,
    },
    {
      issue: "Poorly Formatted Code",
      count: 0,
    },
    {
      issue: "Poorly Written Code",
      count: 0,
    },
    {
      issue: "Security Concerns",
      count: 0,
    },
  ],
  prsAverageScoreOverTimeData: [],
  codeReviewTable: [],
};

// Utils
// Group candidates by status
function groupCandidatesByStatus(candidates) {
  const grouped = Object.entries(
    candidates.reduce((grouped, candidate) => {
      const status = candidate.status || "UNKNOWN";
      grouped[status] = (grouped[status] || 0) + 1;
      return grouped;
    }, {})
  ).map(([status, count]) => ({ status, count }));

  // Sort by status order
  return grouped.sort((a, b) => {
    const indexA = statusOrder.indexOf(a.status);
    const indexB = statusOrder.indexOf(b.status);
    return (
      (indexA === -1 ? Infinity : indexA) - (indexB === -1 ? Infinity : indexB)
    );
  });
}

function transformPRData(prData) {
  const totalPRs = prData.length;
  const totalScore = prData.reduce((sum, pr) => sum + pr.score, 0);
  const averageQualityScore = totalScore / totalPRs;

  const prsWithIssues = prData.filter(
    (pr) => !pr.areCommentsWritten || pr.securityConcernsAny
  );
  const percentagePRsWithIssues = (prsWithIssues.length / totalPRs) * 100;

  const prsWithSecurityConcerns = prData.filter((pr) => pr.securityConcernsAny);
  const percentagePRsWithSecurityConcerns =
    (prsWithSecurityConcerns.length / totalPRs) * 100;

  const categorizePRsByScoreData = {
    "0-4": 0,
    "5-7": 0,
    "8-10": 0,
  };

  prData.forEach((pr) => {
    if (pr.score <= 4) categorizePRsByScoreData["0-4"]++;
    else if (pr.score <= 7) categorizePRsByScoreData["5-7"]++;
    else categorizePRsByScoreData["8-10"]++;
  });

  const categorizedScores = Object.entries(categorizePRsByScoreData).map(
    ([category, count]) => ({
      category,
      count,
    })
  );

  const countPRsWithIssuesData = [
    {
      issue: "Missing Comments",
      count: prData.filter((pr) => !pr.areCommentsWritten).length,
    },
    {
      issue: "Unoptimized Changes",
      count: prData.filter((pr) => !pr.areCodeChangesOptimized).length,
    },
    {
      issue: "Irrelevant Changes",
      count: prData.filter((pr) => !pr.areCodeChangesRelative).length,
    },
    {
      issue: "Poorly Formatted Code",
      count: prData.filter((pr) => !pr.isCodeFormatted).length,
    },
    {
      issue: "Poorly Written Code",
      count: prData.filter((pr) => !pr.isCodeWellWritten).length,
    },
    {
      issue: "Security Concerns",
      count: prData.filter((pr) => pr.securityConcernsAny).length,
    },
  ];

  const prsAverageScoreOverTimeData = prData.reduce((acc, pr) => {
    const date = new Date(pr.createdAt).toLocaleString("default", {
      month: "short",
      year: "2-digit",
    });
    if (!acc[date]) acc[date] = { date, "Average Score": 0, count: 0 };
    acc[date]["Average Score"] += pr.score;
    acc[date].count++;
    return acc;
  }, {});

  const prsAverageScoreOverTime = Object.values(
    prsAverageScoreOverTimeData
  ).map((item) => ({
    date: item.date,
    "Average Score": parseFloat(
      (item["Average Score"] / item.count).toFixed(2)
    ),
    count: item.count,
  }));

  const codeReviewTable = prData.reduce((acc, pr) => {
    if (!acc[pr.projectID]) {
      acc[pr.projectID] = {
        id: pr.projectID,
        name: pr?.project?.name,
        totalPrsReviewed: 0,
        avgQualityScore: 0,
        percentageOfPrsWithIssues: 0,
      };
    }
    acc[pr.projectID].totalPrsReviewed++;
    acc[pr.projectID].avgQualityScore += pr.score;
    if (!pr.areCommentsWritten || pr.securityConcernsAny)
      acc[pr.projectID].percentageOfPrsWithIssues++;
    return acc;
  }, {});

  const codeReviewTableData = Object.values(codeReviewTable).map((project) => ({
    ...project,
    avgQualityScore: parseFloat(
      (project.avgQualityScore / project.totalPrsReviewed).toFixed(2)
    ),
    percentageOfPrsWithIssues: parseFloat(
      (
        (project.percentageOfPrsWithIssues / project.totalPrsReviewed) *
        100
      ).toFixed(2)
    ),
  }));

  return {
    codeReviewMetrics: {
      averageQualityScore: parseFloat(averageQualityScore.toFixed(2)),
      percentagePRsWithIssues: parseFloat(percentagePRsWithIssues.toFixed(2)),
      percentagePRsWithSecurityConcerns: parseFloat(
        percentagePRsWithSecurityConcerns.toFixed(2)
      ),
      totalPRsAnalyzed: totalPRs,
    },
    categorizePRsByScoreData: categorizedScores,
    countPRsWithIssuesData,
    prsAverageScoreOverTimeData: prsAverageScoreOverTime,
    codeReviewTable: codeReviewTableData,
  };
}

// Generate the candidate overview
async function generateCandidatesPerJobPostOverview() {
  const hiringPositions = await queries.listActiveHiringPositions();
  const overview = {};

  for (const position of hiringPositions) {
    const positionId = position.id;
    const candidates = await queries.listCandidateByJobPostAction(positionId);
    const statusCounts = groupCandidatesByStatus(candidates);

    overview[positionId] = {
      status_counts: statusCounts,
      total_candidates: candidates.length,
    };
  }
  return { type: "candidate-per-jobpost", data: overview };
}

// Get stored JSON data from bucket
async function getLatestJsonFromS3(s3Key) {
  const params = {
    Bucket: process.env.STORAGE_DATA_BUCKETNAME,
    Key: `public/reports/${s3Key}/latest.json`,
  };

  try {
    const data = await s3.getObject(params).promise();
    return JSON.parse(data.Body.toString("utf-8"));
  } catch (error) {
    if (error.code === "NoSuchKey") {
      return null;
    }
    console.error("Error retrieving data from S3:", error);
    throw error;
  }
}

// Append the Old and new Analytics data
function appendAnalyticsData(result1, result2) {
  const totalPRsAnalyzed =
    result1.codeReviewMetrics.totalPRsAnalyzed +
    result2.codeReviewMetrics.totalPRsAnalyzed;

  const totalAverageQualityScore =
    (result1.codeReviewMetrics.averageQualityScore *
      result1.codeReviewMetrics.totalPRsAnalyzed +
      result2.codeReviewMetrics.averageQualityScore *
        result2.codeReviewMetrics.totalPRsAnalyzed) /
    totalPRsAnalyzed;

  const totalPercentagePRsWithIssues =
    (((result1.codeReviewMetrics.percentagePRsWithIssues / 100) *
      result1.codeReviewMetrics.totalPRsAnalyzed +
      (result2.codeReviewMetrics.percentagePRsWithIssues / 100) *
        result2.codeReviewMetrics.totalPRsAnalyzed) /
      totalPRsAnalyzed) *
    100;

  const totalPercentagePRsWithSecurityConcerns =
    (((result1.codeReviewMetrics.percentagePRsWithSecurityConcerns / 100) *
      result1.codeReviewMetrics.totalPRsAnalyzed +
      (result2.codeReviewMetrics.percentagePRsWithSecurityConcerns / 100) *
        result2.codeReviewMetrics.totalPRsAnalyzed) /
      totalPRsAnalyzed) *
    100;

  const totalCategorizedScores = result1.categorizePRsByScoreData.map(
    (item, index) => ({
      category: item.category,
      count: item.count + result2.categorizePRsByScoreData[index].count,
    })
  );

  const totalCountPRsWithIssuesData = result1.countPRsWithIssuesData.map(
    (item, index) => ({
      issue: item.issue,
      count: item.count + result2.countPRsWithIssuesData[index].count,
    })
  );

  const averageScoreMap = {};
  result1.prsAverageScoreOverTimeData
    .concat(result2.prsAverageScoreOverTimeData)
    .forEach((item) => {
      if (!averageScoreMap[item.date]) {
        averageScoreMap[item.date] = {
          "Average Score": 0,
          count: 0,
        };
      }

      averageScoreMap[item.date]["Average Score"] +=
        item["Average Score"] * item["count"];
      averageScoreMap[item.date].count += item["count"];
    });

  const totalPrsAverageScoreOverTime = Object.entries(averageScoreMap).map(
    ([date, data]) => ({
      date,
      "Average Score": parseFloat(
        (data["Average Score"] / data.count).toFixed(2)
      ),
      count: data.count,
    })
  );

  const reviewTableMap = {};
  result1.codeReviewTable.concat(result2.codeReviewTable).forEach((project) => {
    if (!reviewTableMap[project.id]) {
      reviewTableMap[project.id] = {
        ...project,
        totalPrsReviewed: 0,
        avgQualityScore: 0,
        percentageOfPrsWithIssues: 0,
      };
    }
    reviewTableMap[project.id].totalPrsReviewed += project.totalPrsReviewed;

    reviewTableMap[project.id].avgQualityScore +=
      project.avgQualityScore * project.totalPrsReviewed;

    reviewTableMap[project.id].percentageOfPrsWithIssues +=
      (project.percentageOfPrsWithIssues / 100) * project.totalPrsReviewed;
  });

  const totalCodeReviewTableData = Object.values(reviewTableMap).map(
    (project) => ({
      ...project,
      avgQualityScore: parseFloat(
        (project.avgQualityScore / project.totalPrsReviewed).toFixed(2)
      ),
      percentageOfPrsWithIssues: parseFloat(
        (
          (project.percentageOfPrsWithIssues / project.totalPrsReviewed) *
          100
        ).toFixed(2)
      ),
    })
  );

  return {
    codeReviewMetrics: {
      averageQualityScore: parseFloat(totalAverageQualityScore.toFixed(2)),
      percentagePRsWithIssues: parseFloat(
        totalPercentagePRsWithIssues.toFixed(2)
      ),
      percentagePRsWithSecurityConcerns: parseFloat(
        totalPercentagePRsWithSecurityConcerns.toFixed(2)
      ),
      totalPRsAnalyzed: totalPRsAnalyzed,
    },
    categorizePRsByScoreData: totalCategorizedScores,
    countPRsWithIssuesData: totalCountPRsWithIssuesData,
    prsAverageScoreOverTimeData: totalPrsAverageScoreOverTime,
    codeReviewTable: totalCodeReviewTableData,
  };
}

async function generateCodeAnalyticsReports() {
  const commits = await queries.listAllPrs(analyticsStatus.PENDING);
  // Generate overall and employee wise code quality report
  const reports = await Promise.all([
    generateCodeQualityReport(commits),
    generateCodeQualityByEmployee(commits),
  ]);
  if (commits.length > 0) {
    await queries.updateAnalyticStatus(commits, analyticsStatus.COMPLETE);
  }
  return reports;
}

// Generate Code quality Report
async function generateCodeQualityReport(commits) {
  let s3Key = S3KeysPerReportType["code-quality"];
  const oldData = await getLatestJsonFromS3(s3Key);
  const hasOldData = oldData !== null;
  const hasCommits = commits?.length > 0;

  if (!hasOldData && !hasCommits) {
    // When no file and no commits found
    return { type: "code-quality", data: initialCodeAssessment };
  }
  if (!hasOldData && hasCommits) {
    const latestData = transformPRData(commits);
    return { type: "code-quality", data: latestData };
  }
  if (hasOldData && !hasCommits) {
    return { type: "code-quality", data: oldData.data };
  }

  const latestData = transformPRData(commits);
  const finalResults = appendAnalyticsData(oldData.data, latestData);
  return { type: "code-quality", data: finalResults };
}

async function calculateProjectRedFlags() {
  const projects = await queries.listActiveProjects();
  const data = await Promise.all(
    projects.map(async (project) => {
      const redFlagsList = await queries.listRedFlags(project?.id);

      //Count occurrences
      const statusCounts = redFlagsList.reduce((acc, flag) => {
        acc[flag.status] = (acc[flag.status] || 0) + 1;
        return acc;
      }, {});

      const statusOrder = ["PENDING", "ACTIVE", "CLOSED"];
      const statusArray = statusOrder.map(
        (status) => statusCounts[status] || 0
      );

      return {
        id: project.id,
        project_name: project.name,
        redflag: statusArray,
      };
    })
  );

  return { type: "project-redflag", data };
}

async function generateCodeQualityByEmployee(commits) {
  const s3Key = S3KeysPerReportType["code-quality-by-employee"];
  const oldData = await getLatestJsonFromS3(s3Key);
  const empGerritData = await getLatestEmployeeData(oldData?.data || []);
  const latestData =
    commits?.length > 0
      ? generateGerritAnalytics(commits, empGerritData)
      : empGerritData;
  return { type: "code-quality-by-employee", data: latestData };
}

async function generateJiraAnalytics() {
  const s3Key = S3KeysPerReportType["jira-analytics"];
  const oldData = await getLatestJsonFromS3(s3Key);
  const jiraEvents = await queries.listJiraEvents();

  if (!jiraEvents?.length) {
    return { type: "jira-analytics", data: oldData?.data ?? {} };
  }

  const analytics = calculateJiraEventAnalytics(jiraEvents);
  
  // Transform data for charts
  const issueTypesData = Object.entries(analytics.reduce((acc, emp) => {
    Object.entries(emp.issueTypeDistribution).forEach(([type, count]) => {
      acc[type] = (acc[type] || 0) + count;
    });
    return acc;
  }, {})).map(([type, count]) => ({ type, count }));

  const storiesPerEmployeeData = analytics.map(emp => ({
    employee: emp.email,
    stories: emp.totalStories
  }));

  const sprintVelocityData = analytics.reduce((acc, emp) => {
    emp.sprintVelocityData?.forEach(sprint => {
      const existingSprint = acc.find(s => s.sprint === sprint.sprint);
      if (existingSprint) {
        existingSprint.points += sprint.points;
      } else {
        acc.push({ ...sprint });
      }
    });
    return acc;
  }, []).sort((a, b) => a.sprint.localeCompare(b.sprint));

  const data = {
    totalStories: analytics.reduce((sum, emp) => sum + emp.totalStories, 0),
    totalBugs: analytics.reduce((sum, emp) => sum + emp.totalBugs, 0),
    averageSprintVelocity: analytics.reduce((sum, emp) => sum + emp.averageSprintVelocity, 0) / analytics.length,
    averageBugResolutionTime: analytics.reduce((sum, emp) => sum + emp.averageResolutionTime, 0) / analytics.length,
    issueTypesData,
    storiesPerEmployeeData,
    sprintVelocityData,
    employeeAnalytics: analytics
  };

  return { type: "jira-analytics", data };
}

// Save the overview data to S3 with retry logic
async function saveToS3(data, Key, maxRetries = 3) {
  const params = {
    Bucket: process.env.STORAGE_DATA_BUCKETNAME,
    Key,
    Body: JSON.stringify(data, null, 2),
    ContentType: "application/json",
  };

  let attempt = 0;
  while (attempt < maxRetries) {
    try {
      await s3.putObject(params).promise();
      console.log(`Successfully saved data to S3 at ${Key}`);
      return;
    } catch (error) {
      if (
        error.code === "NetworkingError" ||
        error.code === "UnknownEndpoint"
      ) {
        attempt++;
        const delay = Math.pow(2, attempt) * 100; // Exponential backoff
        console.error(
          `Retrying save to S3 (attempt ${attempt}) due to error:`,
          error.message
        );
        await new Promise((resolve) => setTimeout(resolve, delay));
      } else {
        console.error(`Error saving data to S3 at ${Key}:`, error);
        throw error; // Non-retryable error
      }
    }
  }
  throw new Error(`Max retries reached for saving data to S3 at ${Key}`);
}

const handleFetchAllFunctionsData = async () => {
  try {
    const reports = await Promise.all([
      generateCandidatesPerJobPostOverview(),
      generateCodeAnalyticsReports(),
      calculateProjectRedFlags(),
      generateJiraAnalytics()
    ]);

    const results = await Promise.all(
      reports.map((report) =>
        saveToS3(
          {
            generated_at: new Date().toISOString(),
            data: report.data,
          },
          `${S3KeysPerReportType[report.type]}/latest.json`
        )
      )
    );

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Successfully generated all reports",
        results,
      }),
    };
  } catch (error) {
    console.error("Error generating reports:", error);
    captureError(error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error generating reports",
        error: error.message,
      }),
    };
  }
};

exports.handler = wrapHandler(async (event) => {
  try {
    console.log(`Event: ${event?.type}`);

    let dataToUpdate = [];
    switch (event?.type) {
      case "code-quality":
        const report = await generateCodeAnalyticsReports();
        dataToUpdate = dataToUpdate.concat(report);
        break;

      case "candidate-per-jobpost":
        const overview = await generateCandidatesPerJobPostOverview();
        dataToUpdate.push(overview);
        break;

      case "project-redflag":
        const redflagData = await calculateProjectRedFlags();
        dataToUpdate.push(redflagData);
        break;

      case "jira-analytics":
        const jiraAnalytics = await generateJiraAnalytics();
        dataToUpdate.push(jiraAnalytics);
        break;

      default:
        // Scheduled function so create all reports
        dataToUpdate = await handleFetchAllFunctionsData();
        break;
    }

    const timestamp = new Date().toISOString();
    const dayTimeStamp = Math.floor(new Date().setHours(0, 0, 0, 0) / 1000);

    await Promise.all(
      dataToUpdate?.map(async (item) => {
        let type = item?.type;
        let s3Key = S3KeysPerReportType[type];

        const dataToSave = {
          generated_at: timestamp,
          data: item?.data,
        };

        console.log(`Saving ${type} data to S3...`, dataToSave);
        await saveToS3(dataToSave, `public/reports/${s3Key}/latest.json`);
        await saveToS3(
          dataToSave,
          `public/reports/${s3Key}/${dayTimeStamp}.json`
        );
      })
    );

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Candidate overview successfully saved to S3.",
      }),
    };
  } catch (error) {
    console.error("Error generating overview:", error);
    captureError(error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: error.message }),
    };
  }
});
