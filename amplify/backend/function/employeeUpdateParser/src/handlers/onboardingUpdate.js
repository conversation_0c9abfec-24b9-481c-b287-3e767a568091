const AWS = require("aws-sdk");
const ses = new AWS.SES();
const { captureError } = require("/opt/sentryWrapper");

const sendOnboardingApprovedMail = async (employee) => {
  try {
    const { email, first_name: firstName, last_name: lastName } = employee;
    const employeeName = `${firstName || ""} ${lastName || ""}`;
    const response = await ses
      .sendTemplatedEmail({
        Source: "<EMAIL>",
        Template: "HubGenericNotification",
        TemplateData: JSON.stringify({
          subject: "Onboarding Form Approved – Welcome Aboard!",
          body: `
          <p>Hi ${employeeName}</p>
          <p>Great news — your onboarding form has been successfully reviewed and <strong>approved</strong> by our HR team! ✅</p>
          <p>If you have any questions, feel free to reach out to the HR team.</p>`,
        }),
        Destination: { ToAddresses: [email] },
      })
      .promise();
    console.log(
      `Onboarding approve email sent to ${email}: ${response.MessageId || ""}`
    );
  } catch (error) {
    console.error("Error in sendOnboardingApprovedMail: ", error);
    captureError(error);
  }
};

const sendOnboardingRejectMail = async (employee) => {
  try {
    const {
      email,
      metadata: { rejectionReason },
      first_name: firstName,
      last_name: lastName,
    } = employee;
    const employeeName = `${firstName || ""} ${lastName || ""}`;
    const response = await ses
      .sendTemplatedEmail({
        Source: "<EMAIL>",
        Template: "HubGenericNotification",
        TemplateData: JSON.stringify({
          subject: "Update on Your Onboarding Status – Action Required",
          body: `
            <p>Hi <strong>${employeeName}</strong>,</p>
            <p>Thank you for submitting your onboarding form. After review, we noticed that we’re unable to proceed due to the following reason:</p>
            <blockquote style="background-color:#f8f8f8; padding:10px; border-left:4px solid #e74c3c;">
                <em>${rejectionReason || ""}</em>
            </blockquote>
            <p>We kindly ask you to review the information and make the necessary updates. Once corrected, please resubmit the form so we can move forward with the onboarding process.</p>
            <p>If you have any questions or need assistance, feel free to reach out to the HR team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            <p>We look forward to getting everything sorted and welcoming you aboard soon!</p>
`,
        }),
        Destination: { ToAddresses: [email] },
      })
      .promise();
    console.log(
      `Onboarding form reject email sent to ${email}: ${
        response.MessageId || ""
      }`
    );
  } catch (error) {
    console.error("Error in sendOnboardingRejectMail: ", error);
    captureError(error);
  }
};

const sendOnboardingInfoMailToHR = async (employee, hrEmails) => {
  try {
    const { email, first_name: firstName, last_name: lastName } = employee;
    const employeeName = `${firstName || ""} ${lastName || ""}`;
    const response = await ses
      .sendTemplatedEmail({
        Source: "<EMAIL>",
        Template: "HubGenericNotification",
        TemplateData: JSON.stringify({
          subject:
            process.env.ENV === "staging"
              ? "TESTING: New Onboarding Form Submitted – Action Required"
              : "New Onboarding Form Submitted – Action Required",
          body: `
          <p>Hi HR Team,</p>
        
          <p><strong>${employeeName}</strong> has just submitted their onboarding form for review.</p>
        
          <p>Please log in to the HR portal to review and take necessary action.</p>

          <p>Thank you for ensuring a smooth and timely onboarding process.</p>
        `,
        }),
        Destination: { ToAddresses: hrEmails },
      })
      .promise();
    console.log(
      `Onboarding form info email sent to ${email}: ${response.MessageId || ""}`
    );
  } catch (error) {
    console.error("Error in sendOnboardingInfoMailToHR: ", error);
    captureError(error);
  }
};

module.exports = {
  sendOnboardingApprovedMail,
  sendOnboardingRejectMail,
  sendOnboardingInfoMailToHR,
};
