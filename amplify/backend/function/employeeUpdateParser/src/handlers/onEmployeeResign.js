const {
  createEmployee<PERSON>eave,
  make<PERSON><PERSON>Coi<PERSON>,
  updateEmployeeCoinBalance,
} = require("../helpers/appSyncHelper.js");

/**
 * Handles the resignation process for an employee by resetting their sick leave balance
 * and coin balance to zero. It performs the following actions:
 * - Debits the employee's remaining sick leave balance to zero.
 * - Resets the employee's coin balance to zero.
 *
 * @param {Object} employee - The employee object containing resignation details.
 * @param {string} employee.email - The email of the resigned employee.
 * @param {number} employee.sick_leave_balance - The current sick leave balance of the employee.
 * @param {number} employee.reward_points - The current coin balance of the employee.
 *
 * @throws Will throw an error if the process fails at any step.
 */
const handleResignedEmp = async (employee) => {
  try {
    const {
      email,
      sick_leave_balance: SLBalance,
      reward_points: coinBalance,
    } = employee;
    // Remove or zero out all remaining sick_leaves for the employee.
    if (SLBalance > 0) {
      await createEmployeeLeave({
        type: "SICK",
        adjustment_type: "DEBIT",
        leave_length: "FULL_DAY",
        employeeLeavesId: email,
        count: SL<PERSON>alance,
        description: `DEBIT due to Employee Resignation`,
        leaveApproved_byId: "<EMAIL>",
        start_time: new Date(),
        end_time: new Date(),
      });
      console.log(`Updated sick leave balance for ${email}: ${SLBalance} → 0.`);
    }
    // Remove or zero out all Coins for the shop.
    if (coinBalance > 0) {
      const coinTransactionInput = {
        adjustment_type: "DEBIT",
        coins: coinBalance,
        transaction_date: new Date().toISOString(),
        employeeCoins_transactionsId: email,
        coinTransactionApproved_byId: "<EMAIL>",
        balance: 0,
        comment: `Due to Employee Resignation, Resetting coin balance to 0`,
      };
      await makeZeroCoins(coinTransactionInput);
      await updateEmployeeCoinBalance({ email, reward_points: 0 });
      console.log(
        `Resetting employee ${email} coin balance ${coinBalance} → 0.`
      );
    }
    console.log(
      `Resigned employee ${email} sick leaves and coin balance reset to 0.`
    );
  } catch (error) {
    throw new Error(
      `Error during handling resigned employees processes... ${error}`
    );
  }
};

module.exports = handleResignedEmp;
