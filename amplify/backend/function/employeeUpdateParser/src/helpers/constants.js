const EMP_ACCOUNT_STATUS = {
  ACTIVE: "ACTIVE", // Active User
  INACTIVE: "INACTIVE", // Not Active User
  RESIGNED: "RESIGNED", // Resigned but not inactive yet (Formalities pending)
  PENDING_PASSWORD: "PENDING_PASSWORD", // Logged in but password pending
  ONBOARDING_PENDING: "ONBOARDING_PENDING", // User is on fetch data screen to enter personal mail and fetch data
  ONBOARDING_FORM: "ONBOARDING_FORM", // User is on onboarding step form
  PENDING_APPROVAL: "PENDING_APPROVAL", // HR Approval
  HIDDEN: "HIDDEN", // Hidden profiles not visible to anyone
};

const CREDENTIALS = {
  USERPOOL_ID: process.env.USERPOOL_ID,
  COGNITO_HR_GRP_NAME: process.env.COGNITO_HR_GRP_NAME || "Hr",
};

module.exports = { ...EMP_ACCOUNT_STATUS, ...CREDENTIALS };
