{"permissions": {"api": {"yorkhrmsGraphQL": ["Query", "Mutation"]}, "storage": {"data": ["create", "read", "update", "delete"]}}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "yorkhrmsSentryLayerNode", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "staging"}], "secretNames": ["OPENAI_KEY"], "environmentVariableList": [{"cloudFormationParameterName": "sentry<PERSON>n", "environmentVariableName": "SENTRY_DSN"}]}