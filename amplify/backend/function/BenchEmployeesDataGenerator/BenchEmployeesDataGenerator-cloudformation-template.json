{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"13.0.1\",\"stackType\":\"function-Lambda\",\"metadata\":{\"whyContinueWithGen1\":\"\"}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "apiyorkhrmsGraphQLGraphQLAPIIdOutput": {"Type": "String", "Default": "apiyorkhrmsGraphQLGraphQLAPIIdOutput"}, "apiyorkhrmsGraphQLGraphQLAPIEndpointOutput": {"Type": "String", "Default": "apiyorkhrmsGraphQLGraphQLAPIEndpointOutput"}, "storagedataBucketName": {"Type": "String", "Default": "storagedataBucketName"}, "functionyorkhrmsSentryLayerNodeArn": {"Type": "String", "Default": "functionyorkhrmsSentryLayerNodeArn"}, "secretsPathAmplifyAppId": {"Type": "String"}, "sentryDsn": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "BenchEmployeesDataGenerator", {"Fn::Join": ["", ["BenchEmployeesDataGenerator", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "API_YORKHRMSGRAPHQL_GRAPHQLAPIIDOUTPUT": {"Ref": "apiyorkhrmsGraphQLGraphQLAPIIdOutput"}, "API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT": {"Ref": "apiyorkhrmsGraphQLGraphQLAPIEndpointOutput"}, "STORAGE_DATA_BUCKETNAME": {"Ref": "storagedataBucketName"}, "OPENAI_KEY": {"Fn::Join": ["", [{"Fn::Sub": ["/amplify/${appId}/${env}/AMPLIFY_${functionName}_", {"appId": {"Ref": "secretsPathAmplifyAppId"}, "env": {"Ref": "env"}, "functionName": "BenchEmployeesDataGenerator"}]}, "OPENAI_KEY"]]}, "SENTRY_DSN": {"Ref": "sentry<PERSON>n"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs22.x", "Layers": [{"Ref": "functionyorkhrmsSentryLayerNodeArn"}], "MemorySize": 1096, "Timeout": 900}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "yorkhrmsLambdaRole31e1002b", {"Fn::Join": ["", ["yorkhrmsLambdaRole31e1002b", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "AmplifyResourcesPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["appsync:GraphQL"], "Resource": [{"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apiyorkhrmsGraphQLGraphQLAPIIdOutput"}, "/types/Query/*"]]}, {"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apiyorkhrmsGraphQLGraphQLAPIIdOutput"}, "/types/Mutation/*"]]}]}, {"Effect": "Allow", "Action": "s3:ListBucket", "Resource": [{"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "storagedataBucketName"}]]}]}, {"Effect": "Allow", "Action": ["s3:PutObject", "s3:GetObject", "s3:DeleteObject"], "Resource": [{"Fn::Join": ["", ["arn:aws:s3:::", {"Ref": "storagedataBucketName"}, "/*"]]}]}]}}}, "LambdaTriggerPolicyEmployeeProjectAllocation": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy-EmployeeProjectAllocation", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:DescribeStream", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:ListStreams"], "Resource": {"Fn::ImportValue": {"Fn::Sub": "${apiyorkhrmsGraphQLGraphQLAPIIdOutput}:GetAtt:EmployeeProjectAllocationTable:StreamArn"}}}]}}}, "LambdaEventSourceMappingEmployeeProjectAllocation": {"Type": "AWS::Lambda::EventSourceMapping", "DependsOn": ["LambdaTriggerPolicyEmployeeProjectAllocation", "LambdaExecutionRole"], "Properties": {"BatchSize": 100, "Enabled": true, "EventSourceArn": {"Fn::ImportValue": {"Fn::Sub": "${apiyorkhrmsGraphQLGraphQLAPIIdOutput}:GetAtt:EmployeeProjectAllocationTable:StreamArn"}}, "FunctionName": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}, "StartingPosition": "LATEST"}}, "AmplifyFunctionSecretsPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-function-secrets-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ssm:GetParameter", "ssm:GetParameters"], "Resource": {"Fn::Join": ["", ["arn:aws:ssm:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":parameter", {"Fn::Sub": ["/amplify/${appId}/${env}/AMPLIFY_${functionName}_", {"appId": {"Ref": "secretsPathAmplifyAppId"}, "env": {"Ref": "env"}, "functionName": "BenchEmployeesDataGenerator"}]}, "*"]]}}]}}, "DependsOn": ["LambdaExecutionRole"]}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}