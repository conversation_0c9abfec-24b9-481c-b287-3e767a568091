/*
Use the following code to retrieve configured secrets from SSM:

const aws = require('aws-sdk');

const { Parameters } = await (new aws.SSM())
  .getParameters({
    Names: ["OPENAI_KEY"].map(secretName => process.env[secretName]),
    WithDecryption: true,
  })
  .promise();

Parameters will be of the form { Name: 'secretName', Value: 'secretValue', ... }[]
*/
/* Amplify Params - DO NOT EDIT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIIDOUTPUT
	ENV
	REGION
	STORAGE_DATA_BUCKETNAME
Amplify Params - DO NOT EDIT */

const {
  listEmployeesAllocation,
  listEmployees,
} = require("./utils/appQueries");
const { BENCH_EMP_PATH, WEEK_HRS } = require("./utils/constants");
const {
  generatedSuggestions,
  convertHtmlToPlainText,
} = require("./utils/generateHiringSuggestions");
const { PutFileToS3 } = require("./utils/s3Helper");
const { wrapHandler, captureError } = require("/opt/sentryWrapper");

/**
 * Calculates the experience in years and months from a given career start date.
 *
 * @param {string} careerStartDateStr - The start date of the career in string format.
 * @returns {string|null} The experience in the format "X years Y months", or null if no date is provided.
 */

function calculateExperience(careerStartDateStr) {
  if (!careerStartDateStr) return null;
  const careerStartDate = new Date(careerStartDateStr);
  const now = new Date();

  let years = now.getFullYear() - careerStartDate.getFullYear();
  let months = now.getMonth() - careerStartDate.getMonth();

  if (months < 0) {
    years--;
    months += 12;
  }

  return `${years} years ${months} months`;
}

/**
 * Generates an employee object with formatted details.
 *
 * @param {Object} emp - The employee data object containing personal and professional details.
 * @param {number} benchHrs - The number of bench hours for the employee.
 * @returns {Object} An object containing the employee's full name, skills, SME IDs, bench hours,
 *                   a plain text introduction, and calculated experience.
 */
const getEmployeeObj = (emp, benchHrs) => {
  const {
    first_name: firstName,
    last_name: lastName,
    skills,
    SME: sme,
    introduction,
    career_start_date: careerStartDate,
  } = emp;
  return {
    fullName: `${firstName || ""} ${lastName || ""}`,
    skills: skills?.items?.map((skill) => skill.skillID).join(", ") || "",
    SME: sme?.items?.map((sme) => sme.sMEID).join(", ") || "",
    benchHrs,
    introduction: convertHtmlToPlainText(introduction),
    experience: calculateExperience(careerStartDate),
  };
};

/**
 * Asynchronously retrieves and calculates bench hours for employees.
 *
 * Fetches employee allocation and employee data concurrently, then calculates
 * the bench hours for each employee by subtracting their allocation from the
 * standard weekly hours. Employees with positive bench hours are included in
 * the result.
 *
 * @returns {Promise<Object>} An object containing the creation and update
 * timestamps, and a map of employees with their bench hours and details.
 *
 * @throws {Error} If an error occurs during data retrieval or processing.
 */
const findAllBenchEmployee = async () => {
  try {
    const [empAllocation, employees] = await Promise.all([
      listEmployeesAllocation(),
      listEmployees(),
    ]);
    const allocationData = empAllocation.reduce((acc, item) => {
      const employeeId = item.employee.email;
      const allocation = item?.allocation || 0;

      // Initialize the employee entry if it doesn't exist
      if (!acc.has(employeeId)) {
        acc.set(employeeId, allocation);
      } else {
        acc.set(employeeId, acc.get(employeeId) + allocation);
      }
      return acc;
    }, new Map());

    const benchEmployees = employees?.reduce((acc, emp) => {
      const { email } = emp;
      const allocation = allocationData.get(email) || 0; // Default to 0 if not found
      const benchHrs = Math.max(0, WEEK_HRS - allocation);
      if (benchHrs > 0) {
        acc[email] = getEmployeeObj(emp, benchHrs);
      }
      return acc;
    }, {});

    const timestamp = new Date();
    return {
      createdAt: timestamp,
      updatedAt: timestamp,
      data: benchEmployees,
    };
  } catch (error) {
    console.error("Error in findAllBenchEmployee:", error);
    throw new Error(error);
  }
};

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

exports.handler = wrapHandler(async (event) => {
  try {
    console.log(`EVENT: ${JSON.stringify(event)}`);
    console.log("Starting bench employees data generation process...");

    const currentBenchEmployees = await findAllBenchEmployee();
    console.log(`Found ${Object.keys(currentBenchEmployees.data).length} bench employees`);

    // Run operations sequentially to avoid overwhelming AWS services
    console.log("Saving bench employees data to S3...");
    await PutFileToS3(BENCH_EMP_PATH, currentBenchEmployees);
    console.log("Successfully saved bench employees data to S3");

    console.log("Starting hiring suggestions generation...");
    await generatedSuggestions(currentBenchEmployees.data);
    console.log("Successfully generated hiring suggestions");

    return Promise.resolve(
      "Successfully created and saved bench employees data to s3 file and generated hiring suggestions."
    );
  } catch (error) {
    console.error("Error processing event:", error);
    captureError(error);

    // Check if it's a throttling error and provide specific guidance
    if (error.message && error.message.includes("ThrottlingException")) {
      console.error("Throttling error detected. Consider increasing delays or reducing batch sizes.");
    }

    return Promise.reject(error);
  }
});
