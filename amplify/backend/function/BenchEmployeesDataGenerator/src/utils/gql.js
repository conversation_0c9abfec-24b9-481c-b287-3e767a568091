const listEmployeeAllocation = `
query MyQuery {
  listEmployeeProjectAllocations {
    items {
      employee {
        email
      }
      allocation
      id
    }
  }
}
`;

const listEmployees = `
query MyQuery($filter: ModelEmployeeFilterInput) {
  listEmployees(filter: $filter) {
    items {
      email
      first_name
      last_name
      introduction
      career_start_date
      skills {
        items {
          skillID
        }
      }
      SME {
        items {
          sMEID
        }
      }
    }
  }
}
`;

const listHiringOpenPositions = `
query MyQuery($filter: ModelHiringPositionFilterInput) {
  listHiringPositions(filter: $filter) {
    items {
      id
      required_experience
      description
      skills {
        items {
          skillID
        }
      }
    }
  }
}
`;

const addSuggestionsToOpenPositions = `
mutation MyMutation($benchEmployeeSuggestion: AWSJSON!, $id: ID!) {
  updateHiringPosition(
    input: {id: $id, benchEmployeeSuggestion: $benchEmployeeSuggestion}
  ) {
    benchEmployeeSuggestion
  }
}
`;

module.exports = {
  listEmployeeAllocation,
  listEmployees,
  listHiringOpenPositions,
  addSuggestionsToOpenPositions,
};
