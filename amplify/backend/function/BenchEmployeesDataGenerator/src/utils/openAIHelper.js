const AWS = require("aws-sdk");
const OpenAI = require("openai");

// Cache for API key to avoid repeated SSM calls
let apiKeyCache = null;
let apiKeyCacheTimestamp = 0;
const CACHE_TTL = 10 * 60 * 1000; // 10 minutes cache

class OpenAIClient {
  constructor() {
    this.openai = null;
  }

  async init() {
    const apiKey = await this.fetchAPIKey();
    this.openai = new OpenAI({ apiKey });
  }

  async fetchAPIKey() {
    // Check cache first
    const now = Date.now();
    if (apiKeyCache && (now - apiKeyCacheTimestamp) < CACHE_TTL) {
      console.log("Using cached API key");
      return apiKeyCache;
    }

    // Fetch from SSM with retry logic
    const maxRetries = 3;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Fetching API key from SSM (attempt ${attempt}/${maxRetries})`);
        
        const { Parameters } = await new AWS.SSM()
          .getParameters({
            Names: ["OPENAI_KEY"].map((secretName) => process.env[secretName]),
            WithDecryption: true,
          })
          .promise();

        const openAIKey = Parameters[0]["Value"];
        
        // Cache the API key
        apiKeyCache = openAIKey;
        apiKeyCacheTimestamp = now;
        
        console.log("Successfully fetched and cached API key");
        return openAIKey;
      } catch (error) {
        lastError = error;
        console.error(`Error fetching API key (attempt ${attempt}/${maxRetries}):`, error);
        
        if (error.code === 'ThrottlingException' && attempt < maxRetries) {
          // Exponential backoff for throttling errors
          const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
          console.log(`Throttling detected, waiting ${delay}ms before retry`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else if (attempt < maxRetries) {
          // Simple delay for other errors
          const delay = 1000 * attempt; // 1s, 2s, 3s
          console.log(`Waiting ${delay}ms before retry`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    console.error("Failed to fetch API key after all retries");
    throw lastError;
  }

  async executePrompt(prompt, system) {
    if (!this.openai) {
      throw new Error("OpenAI client is not initialized. Call init() first.");
    }

    try {
      let options = {
        model: "gpt-4.1-nano",
        messages: [{ role: "user", content: prompt }],
      };
      if (system) {
        options.messages.push({ role: "system", content: system });
      }

      const completion = await this.openai.chat.completions.create(options);
      return completion;
    } catch (error) {
      console.error("Error executing prompt:", error);
      throw error;
    }
  }
}

module.exports = OpenAIClient;
