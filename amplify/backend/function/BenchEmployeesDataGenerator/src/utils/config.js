// Configuration for rate limiting and retry settings
module.exports = {
  // Rate limiting configuration
  RATE_LIMIT_DELAY: 100, // 100ms between requests
  MAX_RETRIES: 3,
  BASE_DELAY: 1000, // 1 second base delay for exponential backoff
  
  // Batch processing configuration
  BATCH_SIZE: 3, // Number of positions to process in each batch
  POSITION_DELAY: 1500, // 1.5 second delay between positions within a batch
  BATCH_DELAY: 3000, // 3 second delay between batches
  THROTTLING_EXTRA_DELAY: 5000, // 5 second extra delay when throttling is detected
  
  // OpenAI configuration
  OPENAI_MODEL: "gpt-4.1-nano",
  
  // Logging configuration
  ENABLE_DETAILED_LOGGING: true,
}; 