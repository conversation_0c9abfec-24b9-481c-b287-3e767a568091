/**
 * Shared Rate Limiter Utility
 * 
 * This utility provides rate limiting and retry functionality for AWS AppSync operations
 * to prevent ThrottlingException errors. Can be used across all Lambda functions.
 */

// Rate limiting configuration
const DEFAULT_CONFIG = {
  RATE_LIMIT_DELAY: 100, // 100ms between requests
  MAX_RETRIES: 3,
  BASE_DELAY: 1000, // 1 second base delay for exponential backoff
  ENABLE_LOGGING: true,
};

// Simple rate limiter
class RateLimiter {
  constructor(delay) {
    this.delay = delay;
    this.lastRequest = 0;
  }

  async wait() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequest;
    
    if (timeSinceLastRequest < this.delay) {
      const waitTime = this.delay - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    this.lastRequest = Date.now();
  }
}

// Exponential backoff retry function
async function retryWithBackoff(fn, config = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const rateLimiter = new RateLimiter(finalConfig.RATE_LIMIT_DELAY);
  
  for (let attempt = 0; attempt <= finalConfig.MAX_RETRIES; attempt++) {
    try {
      await rateLimiter.wait();
      return await fn();
    } catch (error) {
      const isThrottlingError = error.message && (
        error.message.includes('ThrottlingException') ||
        error.message.includes('Rate exceeded') ||
        error.message.includes('Too Many Requests') ||
        error.message.includes('429')
      );
      
      if (isThrottlingError && attempt < finalConfig.MAX_RETRIES) {
        const delay = finalConfig.BASE_DELAY * Math.pow(2, attempt);
        if (finalConfig.ENABLE_LOGGING) {
          console.log(`Rate limit hit, retrying in ${delay}ms (attempt ${attempt + 1}/${finalConfig.MAX_RETRIES + 1})`);
        }
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      
      throw error;
    }
  }
}

// Enhanced AppSync client wrapper
function createRateLimitedAppSyncClient(appsyncClient, config = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  return {
    query: async (params) => {
      return retryWithBackoff(async () => {
        return await appsyncClient.query(params);
      }, finalConfig);
    },
    
    mutate: async (params) => {
      return retryWithBackoff(async () => {
        return await appsyncClient.mutate(params);
      }, finalConfig);
    },
    
    subscribe: async (params) => {
      return retryWithBackoff(async () => {
        return await appsyncClient.subscribe(params);
      }, finalConfig);
    }
  };
}

// Batch processing utility
async function processBatch(items, processor, config = {}) {
  const finalConfig = {
    BATCH_SIZE: 3,
    BATCH_DELAY: 1000,
    ITEM_DELAY: 500,
    ...config
  };
  
  const batches = [];
  for (let i = 0; i < items.length; i += finalConfig.BATCH_SIZE) {
    batches.push(items.slice(i, i + finalConfig.BATCH_SIZE));
  }
  
  const results = [];
  
  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    
    // Process batch sequentially
    for (let i = 0; i < batch.length; i++) {
      const item = batch[i];
      try {
        const result = await processor(item, batchIndex * finalConfig.BATCH_SIZE + i);
        results.push(result);
        
        // Add delay between items
        if (i < batch.length - 1) {
          await new Promise(resolve => setTimeout(resolve, finalConfig.ITEM_DELAY));
        }
      } catch (error) {
        console.error(`Error processing item ${i} in batch ${batchIndex}:`, error);
        // Continue with next item
        continue;
      }
    }
    
    // Add delay between batches
    if (batchIndex < batches.length - 1) {
      await new Promise(resolve => setTimeout(resolve, finalConfig.BATCH_DELAY));
    }
  }
  
  return results;
}

module.exports = {
  RateLimiter,
  retryWithBackoff,
  createRateLimitedAppSyncClient,
  processBatch,
  DEFAULT_CONFIG,
}; 