const {
  listOpenHiringPositions,
  addSuggestionsToOpenPosition,
} = require("./appQueries");
const { getEmployeeSuggestionPrompt } = require("./prompt");
const OpenAIClient = require("./openAIHelper");
const { htmlToText } = require("html-to-text");
const config = require("./config");
const { processBatch } = require("./sharedRateLimiter");

const getOpenAIClient = async () => {
  const client = new OpenAIClient();
  await client.init();
  return client;
};

const processEmployeesForPosition = async (position, benchEmployees) => {
  const prompt = getEmployeeSuggestionPrompt(position, benchEmployees);
  const client = await getOpenAIClient();
  const response = await client.executePrompt(
    prompt,
    "You are a smart assistant for internal HR tools, responsible for matching bench employees to open positions based on job descriptions and employee data."
  );
  const generatedSuggestionResult = response.choices[0].message.content;
  return generatedSuggestionResult;
};

/**
 * Converts HTML content to plain text.
 *
 * This function removes HTML tags from the input content and returns
 * the resulting plain text. It specifically ignores the href attribute
 * in anchor tags.
 *
 * @param {string} htmlContent - The HTML content to be converted.
 * @returns {string} The plain text representation of the HTML content.
 */
const convertHtmlToPlainText = (htmlContent) => {
  const cleanText = htmlToText(htmlContent, {
    wordwrap: false,
    selectors: [{ selector: "a", options: { ignoreHref: true } }],
  });
  return cleanText;
};

/**
 * Generates hiring suggestions for bench employees by matching them to open positions.
 *
 * This function retrieves a list of open hiring positions and processes each position
 * to generate suggestions for bench employees. It converts HTML descriptions to plain text,
 * processes employees for each position, and saves the suggestions to the database.
 *
 * @param {Array} benchEmployees - An array of bench employees to be considered for open positions.
 * @throws Will throw an error if there is an issue retrieving open positions or saving suggestions.
 */
const generatedSuggestions = async (benchEmployees) => {
  try {
    const openPositions = await listOpenHiringPositions();
    if (openPositions?.length === 0) {
      console.log("No open hiring positions found.");
      return;
    }

    console.log(`Processing ${openPositions.length} open positions with rate limiting`);

    // Use shared batch processing utility
    const processPosition = async (position, index) => {
      console.log(`Processing position ${index + 1}/${openPositions.length}: ${position.id}`);
      
      const { id, description } = position;
      const cleanText = convertHtmlToPlainText(description);
      const suggestions = await processEmployeesForPosition(
        {
          ...position,
          description: cleanText,
        },
        benchEmployees
      );
      
      // Save the record in DB
      console.log("suggestions ", suggestions);
      await addSuggestionsToOpenPosition({
        id,
        benchEmployeeSuggestion: JSON.stringify(suggestions),
      });
      
      console.log(`Successfully processed position ${id}`);
      return { id, success: true };
    };

    const results = await processBatch(openPositions, processPosition, {
      BATCH_SIZE: config.BATCH_SIZE,
      BATCH_DELAY: config.BATCH_DELAY,
      ITEM_DELAY: config.POSITION_DELAY,
    });

    const successCount = results.filter(r => r && r.success).length;
    console.log(`Successfully generated hiring suggestions for ${successCount}/${openPositions.length} positions.`);
  } catch (error) {
    console.error("Error in generatedSuggestions: ", error);
    throw new Error(error);
  }
};

module.exports = {
  generatedSuggestions,
  convertHtmlToPlainText,
};
