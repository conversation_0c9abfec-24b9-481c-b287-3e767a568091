const getEmployeeSuggestionPrompt = (position, benchEmployees) => {
  const prompt = `
    You are an intelligent matching assistant that evaluates job-position suitability for bench employees. When someone's project allocation changes and they are free and not considered for any other projects, we want to evaluate whether they are a good fit for an open hiring position.

    Given:
    - A job position with required experience in year, description (JD), and required skills.
    - A list of bench employees, each with skills, experience, SME (Subject Matter Expertise), skills, introduction and experience.

    Your task:
    1. Compare the experience, skills, and introduction of each bench employee to match with the job position.
    2. Evaluate how well each employee matches the position using a percentage score.
    3. Consider adding any additional insights based on similarities between JD and the employee’s background.
    3. Write a concise evaluation summarizing why they received that score.

    ### Output format:
    Return a **strict JSON array** (⚠ no object wrapper), where each object includes:
    
    - "name": string — Employee name
    - "email": string — Employee email
    - "matchPercentage": number — Score from 0 to 100
    - "Skills": Take from the benchEmployees data
    - "SME": Take from the benchEmployees data
    - "evaluation": string — One-line reason for the score
    
    ### Example:
    [
      {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "matchPercentage": 80,
        "evaluation": "Strong backend skills with relevant experience, but limited DevOps exposure.",
        "skills":"",
        "SME":""
      }
    ]
    
    ### Important:
    - Output must be **only** a JSON array — do **not** wrap it inside any object (e.g., { "results": [...] }).
    - Do **not** include any comments, markdown, or explanation.
    - **Sort the array in descending order by matchPercentage**.
    - Do **not** prepend or append any other text.

    ### Job position:
    ${JSON.stringify(position, null, 2)}

    ### Bench employees:
    ${JSON.stringify(benchEmployees, null, 2)}
`;
  return prompt;
};

module.exports = {
  getEmployeeSuggestionPrompt,
};
