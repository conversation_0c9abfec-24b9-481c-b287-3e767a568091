const AWS = require("aws-sdk");
const AWSAppSyncClient = require("aws-appsync").default;
const { AUTH_TYPE } = require("aws-appsync");
const gql = require("graphql-tag");
const config = require("./config");
const { createRateLimitedAppSyncClient } = require("./sharedRateLimiter");
AWS.config.update({ region: process.env.REGION });
const appsyncUrl = process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT;
const queries = require("./gql.js");

// graphql client.  We define it outside of the lambda function in order for it to be reused during subsequent calls
const baseAppsyncClient = new AWSAppSyncClient({
  url: appsyncUrl,
  region: process.env.REGION,
  auth: {
    type: AUTH_TYPE.AWS_IAM,
    credentials: AWS.config.credentials,
  },
  disableOffline: true,
});

// Create rate-limited AppSync client
const appsyncClient = createRateLimitedAppSyncClient(baseAppsyncClient, {
  RATE_LIMIT_DELAY: config.RATE_LIMIT_DELAY,
  MAX_RETRIES: config.MAX_RETRIES,
  BASE_DELAY: config.BASE_DELAY,
  ENABLE_LOGGING: config.ENABLE_DETAILED_LOGGING,
});

async function executeQuery(query, variables) {
  try {
    const response = await appsyncClient.query({
      query: gql(query),
      variables,
      fetchPolicy: "network-only",
    });
    return response;
  } catch (err) {
    console.log("Error while trying to fetch data", err);
    throw err;
  }
}

async function fetchAllData(query, variables) {
  let items = [];
  let nextToken = null;

  try {
    do {
      const response = await executeQuery(query, {
        ...variables,
        limit: 999,
        nextToken,
      });
      if (response.errors) {
        console.error("GraphQL errors: ", response.errors);
        throw new Error("Error occurred while fetching data from GraphQL API.");
      }
      const data = response?.data;
      const queryKey = Object.keys(data).find((key) => data[key]?.items);

      if (queryKey) {
        items = items.concat(data[queryKey]?.items || []);
        nextToken = data[queryKey]?.nextToken || null;
      } else {
        nextToken = null;
      }
    } while (nextToken);

    return items;
  } catch (err) {
    console.error("Error in fetchAllData: ", err);
    throw new Error(
      "Failed to fetch all data. Please check the logs for more details."
    );
  }
}

async function listEmployeesAllocation() {
  try {
    return await fetchAllData(queries.listEmployeeAllocation);
  } catch (err) {
    console.error("Error in :listEmployeesAllocation ", err);
    throw new Error("Failed to listEmployeesAllocation");
  }
}

async function listEmployees() {
  try {
    return await fetchAllData(queries.listEmployees, {
      filter: {
        or: [{ account_status: { eq: "ACTIVE" } }, { active: { eq: true } }],
      },
    });
  } catch (err) {
    console.error("Error in :listEmployees ", err);
    throw new Error("Failed to listEmployees");
  }
}

async function listOpenHiringPositions() {
  try {
    return await fetchAllData(queries.listHiringOpenPositions, {
      filter: { open: { eq: true } },
    });
  } catch (err) {
    console.error("Error in :listOpenHiringPositions ", err);
    throw new Error("Failed to listOpenHiringPositions");
  }
}

async function executeMutation(query, variables) {
  try {
    const response = await appsyncClient.mutate({
      mutation: gql(query),
      variables,
    });
    return response;
  } catch (err) {
    console.log("Error while trying to mutate data", err);
    throw err;
  }
}

async function addSuggestionsToOpenPosition(payload) {
  try {
    return await executeMutation(
      queries.addSuggestionsToOpenPositions,
      payload
    );
  } catch (err) {
    console.error("Error in :addSuggestionsToOpenPosition ", err);
    throw new Error("Failed to addSuggestionsToOpenPosition");
  }
}

module.exports = {
  listEmployeesAllocation,
  listOpenHiringPositions,
  listEmployees,
  addSuggestionsToOpenPosition,
};
