{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"13.0.1\",\"stackType\":\"function-Lambda\",\"metadata\":{\"whyContinueWithGen1\":\"\"}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "apiyorkhrmsGraphQLGraphQLAPIIdOutput": {"Type": "String", "Default": "apiyorkhrmsGraphQLGraphQLAPIIdOutput"}, "apiyorkhrmsGraphQLGraphQLAPIEndpointOutput": {"Type": "String", "Default": "apiyorkhrmsGraphQLGraphQLAPIEndpointOutput"}, "functionyorkhrmsSentryLayerNodeArn": {"Type": "String", "Default": "functionyorkhrmsSentryLayerNodeArn"}, "sentryDsn": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "wfh<PERSON><PERSON><PERSON>", {"Fn::Join": ["", ["wfh<PERSON><PERSON><PERSON>", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "API_YORKHRMSGRAPHQL_GRAPHQLAPIIDOUTPUT": {"Ref": "apiyorkhrmsGraphQLGraphQLAPIIdOutput"}, "API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT": {"Ref": "apiyorkhrmsGraphQLGraphQLAPIEndpointOutput"}, "SENTRY_DSN": {"Ref": "sentry<PERSON>n"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs22.x", "Layers": [{"Ref": "functionyorkhrmsSentryLayerNodeArn"}], "Timeout": 25}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "yorkhrmsLambdaRole9a3b6c1a", {"Fn::Join": ["", ["yorkhrmsLambdaRole9a3b6c1a", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "AmplifyResourcesPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["appsync:GraphQL"], "Resource": [{"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apiyorkhrmsGraphQLGraphQLAPIIdOutput"}, "/types/Query/*"]]}, {"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apiyorkhrmsGraphQLGraphQLAPIIdOutput"}, "/types/Mutation/*"]]}]}]}}}, "LambdaTriggerPolicyWorkFromHomeRequest": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy-WorkFromHomeRequest", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:DescribeStream", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:ListStreams"], "Resource": {"Fn::ImportValue": {"Fn::Sub": "${apiyorkhrmsGraphQLGraphQLAPIIdOutput}:GetAtt:WorkFromHomeRequestTable:StreamArn"}}}]}}}, "LambdaEventSourceMappingWorkFromHomeRequest": {"Type": "AWS::Lambda::EventSourceMapping", "DependsOn": ["LambdaTriggerPolicyWorkFromHomeRequest", "LambdaExecutionRole"], "Properties": {"BatchSize": 100, "Enabled": true, "EventSourceArn": {"Fn::ImportValue": {"Fn::Sub": "${apiyorkhrmsGraphQLGraphQLAPIIdOutput}:GetAtt:WorkFromHomeRequestTable:StreamArn"}}, "FunctionName": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}, "StartingPosition": "LATEST"}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}