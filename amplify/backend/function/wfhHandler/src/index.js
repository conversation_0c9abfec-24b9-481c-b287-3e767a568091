/* Amplify Params - DO NOT EDIT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIIDOUTPUT
	ENV
	REGION
Amplify Params - DO NOT EDIT */

// const { updateEmployeeWFHBalance } = require("./helpers/appQueries");

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

const AWS = require("aws-sdk");
const { wrapHandler, captureError } = require("/opt/sentryWrapper");

const {
  getEmployee,
  updateEmployeeWFHBalance,
} = require("./helpers/appQueries");

exports.handler = wrapHandler(async (event) => {
  try {
    console.log(`EVENT: ${JSON.stringify(event)}`);
    for (const record of event.Records) {
      console.log("DynamoDB Record: %j", record.dynamodb);
      if (record.eventName === "MODIFY") {
        const newImage = AWS.DynamoDB.Converter.unmarshall(
          record.dynamodb.NewImage
        );
        const oldImage = AWS.DynamoDB.Converter.unmarshall(
          record.dynamodb.OldImage
        );

        if (!newImage || !oldImage) continue;

        const newStatus = newImage.status;
        const oldStatus = oldImage.status;

        if (oldStatus !== "APPROVED" && newStatus === "APPROVED") {
          const employeeId = newImage.employeeId;
          const wfhLength = newImage.wfhLength;
          const startDate = new Date(newImage.startDate);
          const endDate = new Date(newImage.endDate);
          const adjustmentType = newImage.adjustment_type;

          let delta = 0;
          if (startDate.toDateString() === endDate.toDateString()) {
            // Single day WFH, use wfhLength
            delta = wfhLength === "FULL_DAY" ? 1 : 0.5;
          } else {
            const oneDay = 24 * 60 * 60 * 1000;
            const totalDays = Math.round((endDate - startDate) / oneDay) + 1;
            delta = totalDays;
          }

          const employeeDetails = await getEmployee(employeeId);
          let wfh_balance = employeeDetails.wfh_balance ?? 0;
          if (adjustmentType === "DEBIT") {
            wfh_balance = wfh_balance - delta;
          } else {
            wfh_balance = wfh_balance + delta;
          }
          await updateEmployeeWFHBalance({
            input: { email: employeeId, wfh_balance },
          });
          console.log(`Processed WFH request: 
        Delta: ${delta}, AdjusmentType: ${adjustmentType}, WFH Balance: ${wfh_balance} for employee: ${employeeId} with WFH Length: ${wfhLength} and Start Date: ${startDate} and End Date: ${endDate}
        `);
        }
      }
    }
    return Promise.resolve("Successfully processed DynamoDB record");
  } catch (error) {
    captureError(error);
    return Promise.reject(error);
  }
});
