const updateEmployee = /* GraphQL */ `
  mutation UpdateEmployee(
    $input: UpdateEmployeeInput!
    $condition: ModelEmployeeConditionInput
  ) {
    updateEmployee(input: $input, condition: $condition) {
      email
      employee_id
      first_name
      last_name
      reward_points
      wfh_balance
    }
  }
`;

const getEmployee = /* GraphQL */ `
  query GetEmployee($email: String!) {
    getEmployee(email: $email) {
      email
      employee_id
      first_name
      last_name
      introduction
      wfh_balance

    }
  }
`;

module.exports = {
  updateEmployee,
  getEmployee
};
