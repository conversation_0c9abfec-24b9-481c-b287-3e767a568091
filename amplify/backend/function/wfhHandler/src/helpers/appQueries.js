const AWS = require("aws-sdk");
const AWSAppSyncClient = require("aws-appsync").default;
const { AUTH_TYPE } = require("aws-appsync");
const gql = require("graphql-tag");
AWS.config.update({ region: process.env.REGION });
const appsyncUrl = process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT;
const queries = require("./gql.js");

// graphql client.  We define it outside of the lambda function in order for it to be reused during subsequent calls
const appsyncClient = new AWSAppSyncClient({
  url: appsyncUrl,
  region: process.env.REGION,
  auth: {
    type: AUTH_TYPE.AWS_IAM,
    credentials: AWS.config.credentials,
  },
  disableOffline: true,
});


async function executeQuery(query, variables) {
  try {
    const response = await appsyncClient.query({
      query: gql(query),
      variables,
      fetchPolicy: "network-only",
    });
    return response;
  } catch (err) {
    console.log("Error while trying to fetch data", err);
    throw JSON.stringify(err);
  }
}

async function fetchAllData(query, variables) {
  let items = [];
  let nextToken = null;

  try {
    do {
      const response = await executeQuery(query, {
        ...variables,
        limit: 999,
        nextToken,
      });
      if (response.errors) {
        console.error("GraphQL errors: ", response.errors);
        throw new Error("Error occurred while fetching data from GraphQL API.");
      }
      const data = response?.data;
      const queryKey = Object.keys(data).find((key) => data[key]?.items);

      if (queryKey) {
        items = items.concat(data[queryKey]?.items || []);
        nextToken = data[queryKey]?.nextToken || null;
      } else {
        nextToken = null;
      }
    } while (nextToken);

    return items;
  } catch (err) {
    console.error("Error in fetchAllData: ", err);
    throw new Error(
      "Failed to fetch all data. Please check the logs for more details."
    );
  }
}

async function executeMutation(query, variables) {
  try {
    const response = await appsyncClient.mutate({
      mutation: gql(query),
      variables,
    });
    return response;
  } catch (err) {
    console.log("Error while trying to mutate data", err);
    throw new Error("Failed to mutate the data !");
  }
}


async function getEmployee(email) {
  const response = await executeQuery(queries.getEmployee, {
    email,
  });
  if (response.errors) {
    return response;
  }
  return response?.data?.getEmployee;
}

async function updateEmployeeWFHBalance(variables) {
  try {
    const response = await executeMutation(queries.updateEmployee, variables);
    
    return response;
  } catch (err) {
    console.error("Error in :updateEmployeeAccountStatus ", err);
    throw new Error(
      "Failed to update employee account status. Please check the logs for more details"
    );
  }
}

module.exports = {
  updateEmployeeWFHBalance,
  getEmployee
};
