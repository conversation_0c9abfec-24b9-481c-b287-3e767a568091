const { GraphQL } = require("./awsServiceClient");
const queries = require("../utils/gql");
const { default: gql } = require("graphql-tag");

class GraphQLOperations {
  constructor(appSyncClient) {
    this.appSyncClient = appSyncClient;
  }

  /**
   * Fetches all paginated data for a given query and variables.
   * Automatically handles AppSync-style `nextToken` pagination.
   */
  async fetchAllData(query, variables) {
    let items = [];
    let nextToken = null;

    try {
      do {
        const response = await this.executeQuery(query, {
          ...variables,
          limit: 999,
          nextToken,
        });
        if (response.errors) {
          console.error("GraphQL errors: ", response.errors);
          throw new Error(
            "Error occurred while fetching data from GraphQL API."
          );
        }
        const data = response?.data;
        const queryKey = Object.keys(data).find((key) => data[key]?.items);

        if (queryKey) {
          items = items.concat(data[queryKey]?.items || []);
          nextToken = data[queryKey]?.nextToken || null;
        } else {
          nextToken = null;
        }
      } while (nextToken);

      return items;
    } catch (err) {
      console.error("Error in fetchAllData: ", err);
      throw new Error(
        "Failed to fetch all data. Please check the logs for more details."
      );
    }
  }

  async executeQuery(query, variables) {
    try {
      const response = await this.appSyncClient.query({
        query: gql(query),
        variables,
        fetchPolicy: "network-only",
      });
      return response;
    } catch (err) {
      console.log("Error while trying to fetch data", err);
      throw JSON.stringify(err);
    }
  }

  async executeMutation(query, variables) {
    try {
      const response = await this.appSyncClient.mutate({
        mutation: gql(query),
        variables,
        fetchPolicy: "no-cache",
      });
      return response;
    } catch (err) {
      console.log("Error while trying to mutate data", err);
      throw JSON.stringify(err);
    }
  }
}

// --- All Query Operations ---
class QueryOperations extends GraphQLOperations {
  constructor(client) {
    super(client);
  }
  async fetchUnreadNotifications() {
    try {
      const notifications = await this.fetchAllData(
        queries.fetchUnreadNotifications,
        {
          filter: { isRead: { eq: false }, isEmailSent: { eq: false } },
        }
      );
      return notifications;
    } catch (error) {
      console.error("Error in fetchUnreadNotifications: ", error);
      throw new Error("Unable to fetch unread notifications from AppSync.");
    }
  }

  async fetchEmployeeDetails(isWeekend = false, date) {
    try {
      const query = queries.listEmployees(isWeekend);
      const variables = isWeekend
        ? { date } // AWSDate format: YYYY-MM-DD
        : {};

      const employeeResponse = await this.fetchAllData(query, variables);
      return employeeResponse;
    } catch (error) {
      console.error("Error in fetchEmployeeDetails: ", error);
      throw new Error("Unable to fetch employee details from AppSync.");
    }
  }

  /**
   * Fetches timesheet records within a given time range.
   *
   * @param {string} startTime - The start of the date range (ISO or AWSDate format).
   * @param {string} endTime - The end of the date range (ISO or AWSDate format).
   * @returns {Promise<Array>} A list of timesheet records.
   * @throws {Error} If fetching timesheets fails.
   */
  async fetchTimeSheetsInRange(startTime, endTime) {
    try {
      const timeSheets = await this.fetchAllData(queries.fetchTimeSheets, {
        startTimeFrom: startTime,
        startTimeTo: endTime,
      });
      return timeSheets;
    } catch (error) {
      console.error("Error fetching timesheets from AppSync: ", error);
      throw new Error("Failed to fetch timesheet data via AppSync.");
    }
  }

  async fetchRulesByFrequency(frequencies) {
    try {
      const options = frequencies?.map((fq) => ({ frequency: { eq: fq } }));
      const rules = await this.fetchAllData(queries.listExecutableRules, {
        filter: {
          or: options,
        },
      });
      return rules;
    } catch (error) {
      console.error("Error fetching fetchRulesByFrequency ", error);
      throw new Error(`Error fetching fetchRulesByFrequency: ${error}`);
    }
  }
}

// --- All Mutation Operations ---

class MutationOperations extends GraphQLOperations {
  constructor(client) {
    super(client);
  }

  /**
   * Updates a notification's `isEmailSent` field to true in the backend.
   *
   * @param {string} notificationId - The ID of the notification to update.
   * @returns {Promise<Object>} The response from the mutation request.
   * @throws {Error} If updating the notification fails.
   */
  async updateIsEmailSent(notificationId) {
    try {
      const variables = {
        input: {
          id: notificationId,
          isEmailSent: true,
        },
      };
      const response = await this.executeMutation(
        queries.updateEmailSentNotification,
        variables
      );
      return response;
    } catch (error) {
      console.error(
        `Error updating isEmailSent for id: ${notificationId}`,
        error
      );
      throw new Error("Failed to update notification status via AppSync.");
    }
  }

  async createExecutable(input) {
    try {
      const response = await this.executeMutation(
        queries.createExecutable,
        input
      );
      return response;
    } catch (error) {
      console.error(`Error creating executables...`, error);
      throw new Error(error);
    }
  }
}

// Instantiate query and mutation operations with singleton AppSync client
const queryOps = new QueryOperations(GraphQL.instance);
const mutationsOps = new MutationOperations(GraphQL.instance);

module.exports = {
  queryOps,
  mutationsOps,
};
