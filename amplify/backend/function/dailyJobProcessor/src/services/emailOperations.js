const { formatName } = require("../utils/general");
const { SES } = require("./awsServiceClient");

/**
 * @class EmailOperation
 * @description
 * The EmailOperation class provides methods to send templated emails and generate
 * email bodies for notifying about employees approaching their 6-month work anniversary.
 * It uses AWS SES for sending emails and formats employee details into an HTML structure.
 */

class EmailOperation {
  constructor(sesClient) {
    this.sesClient = sesClient;
  }

  /**
   * @method sendEmail
   * @param {Object} params - Parameters for sending a templated email.
   * @returns {Promise<Object>} - The response from the SES service.
   * @throws {Error} - Throws an error if email sending fails.

   */
  async sendEmail(params) {
    try {
      const response = await this.sesClient.sendTemplatedEmail({
        Source: "<EMAIL>",
        Template: "HubGenericNotification",
        ...params,
      });
      return response;
    } catch (error) {
      throw new Error(`Error during sending email: ${error}`);
    }
  }

  /**
   * @method generateEmailBody
   * @param {Array<Object>} employees - List of employee objects.
   * @param {string} [recipientType='HR'] - The type of recipient (HR, MANAGER, SQUAD_MANAGER).
   * @returns {string} - The generated HTML email body.
   */
  generateEmailBody(employees, recipientType = "HR") {
    const isMultiple = employees.length > 1;
    const memberLabel = isMultiple ? "Members" : "Member";
    const employeeLabel = isMultiple ? "Employees" : "Employee";

    const headingMap = {
      HR: `${employeeLabel} Approaching 6-Month Anniversary`,
      MANAGER: `Your Team ${memberLabel} Approaching 6-Month Work Anniversary`,
      SQUAD_MANAGER: `Your Squad ${memberLabel} Approaching 6-Month Work Anniversary`,
    };

    const introMap = {
      HR: `The following ${
        isMultiple ? "employees" : "employee"
      } will complete 6 months at York IE in 15 days:`,

      MANAGER: `The following ${
        isMultiple ? "team members" : "team member"
      } reporting to you will complete 6 months at York IE in 15 days:`,

      SQUAD_MANAGER: `The following ${
        isMultiple ? "squad members" : "squad member"
      } in your squad will complete 6 months at York IE in 15 days:`,
    };

    const heading = headingMap[recipientType] || headingMap.HR;
    const intro = introMap[recipientType] || introMap.HR;

    let body = `
      <div style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; padding: 20px; color: #333;">
        <h2 style="color: #1a5276; border-bottom: 1px solid #ddd; padding-bottom: 10px;">${heading}</h2>
        <p>${intro}</p>
    `;

    for (const emp of employees) {
      const {
        first_name: firstName,
        last_name: lastName,
        email,
        york_start_date: joinDate = "",
        guild,
        guildEmployeeId,
        title,
        reporting_to: reportingTo,
        squad,
      } = emp;

      const fullName = formatName(firstName, lastName);
      const department = guild?.name || guildEmployeeId || "";
      const designation = title?.name || "";
      const reportingManager = formatName(
        reportingTo?.first_name,
        reportingTo?.last_name
      );
      const squadManager = formatName(
        squad?.squad_manager?.first_name,
        squad?.squad_manager?.last_name
      );

      body += `
        <div style="margin-bottom: 30px; background-color: #f9f9f9; padding: 20px; border-left: 4px solid #3498db; border-radius: 4px;">
          <h3 style="margin-top: 0; color: #2c3e50;">${fullName}</h3>
          <div style="display: grid; grid-template-columns: 180px auto; gap: 5px;">
            <div style="font-weight: bold;">Email:</div>
            <div><a href="mailto:${email}" style="color: #3498db; text-decoration: none;">${email}</a></div>
  
            <div style="font-weight: bold;">Date of Joining:</div>
            <div>${joinDate}</div>
  
            <div style="font-weight: bold;">Department:</div>
            <div>${department}</div>
  
            <div style="font-weight: bold;">Designation:</div>
            <div>${designation}</div>
  
            ${
              recipientType === "HR"
                ? `
              <div style="font-weight: bold;">Reporting Manager:</div>
              <div>${reportingManager}</div>
  
              <div style="font-weight: bold;">Squad Manager:</div>
              <div>${squadManager}</div>
            `
                : ""
            }
          </div>
        </div>
      `;
    }

    body += `
        <p style="margin-top: 20px; font-style: italic; color: #7f8c8d;">
          Please take a moment to recognize these ${
            recipientType === "HR"
              ? "team members"
              : "team members on your team"
          } on their upcoming milestone.
        </p>
      </div>
    `;

    return body;
  }
}

// Instantiate EmailOperation with a singleton client instance
const emailClient = new EmailOperation(SES.instance);
module.exports = emailClient;
