const {
  COGNITO_HR_GRP_NAME,
  USERPOOL_ID,
  COGNITO_EXECUTIVE_GRP_NAME,
} = require("../utils/constants");
const { Cognito } = require("./awsServiceClient");

/**
 * Handle all cognito related operations
 * @class CognitoOperation
 */
class CognitoOperation {
  constructor(cognito) {
    this.client = cognito;
  }

  /**
   * Fetches all users in the HR group and returns their email addresses.
   * @method getHRGroupEmail
   */
  async getHRGroupEmail() {
    try {
      // Validate required parameters
      if (!USERPOOL_ID) {
        throw new Error("USERPOOL_ID is not configured");
      }
      
      if (!COGNITO_HR_GRP_NAME) {
        throw new Error("COGNITO_HR_GRP_NAME is not configured");
      }

      console.log(`Fetching HR group users from pool: ${USERPOOL_ID}, group: ${COGNITO_HR_GRP_NAME}`);

      const params = {
        UserPoolId: USERPOOL_ID,
        GroupName: COGNITO_HR_GRP_NAME,
      };

      const users = [];
      let response;
      do {
        response = await this.client.listUsersInGroup(params).promise();
        users.push(...response.Users);
        params.NextToken = response.NextToken;
      } while (response.NextToken);

      const emails = users
        .map(
          (user) => user.Attributes.find((attr) => attr.Name === "email")?.Value
        )
        .filter(Boolean);
      
      console.log(`Found ${emails.length} HR group users: ${emails.join(', ')}`);
      return emails;
    } catch (error) {
      console.error(`Error in getHRGroupEmail: ${error.message}`);
      console.error(`USERPOOL_ID: ${USERPOOL_ID}`);
      console.error(`COGNITO_HR_GRP_NAME: ${COGNITO_HR_GRP_NAME}`);
      throw new Error(`Failed to fetch HR group users: ${error}`);
    }
  }

  /**
   * Check the user are Executive or Hr.
   * @method isUserInExecutiveHrGroup
   */
  async isUserInExecutiveHrGroup(email) {
    try {
      const { Groups = [] } = await this.client
        .adminListGroupsForUser({
          UserPoolId: USERPOOL_ID,
          Username: email,
        })
        .promise();

      return Groups.some((group) =>
        [COGNITO_EXECUTIVE_GRP_NAME, COGNITO_HR_GRP_NAME].includes(
          group.GroupName
        )
      );
    } catch (error) {
      console.error(`Error checking user groups for ${email}:`, error);
      return false;
    }
  }
}

// Instantiate CognitoOperation with a singleton client instance
const cognitoClient = new CognitoOperation(Cognito.instance);
module.exports = cognitoClient;
