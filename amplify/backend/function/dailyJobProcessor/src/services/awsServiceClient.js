const AWS = require("aws-sdk");
const { AUTH_TYPE, default: AWSAppSyncClient } = require("aws-appsync");
const { REGION } = require("../utils/constants");
AWS.config.update({ region: REGION });
const appsyncUrl = process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT;

/*
 *  Singleton pattern to ensure only one instance is created and reused
 */

class SES {
  static #instance;

  static get instance() {
    if (!SES.#instance) {
      SES.#instance = new AWS.SES();
    }
    return SES.#instance;
  }
}

class Cognito {
  static #instance;

  static get instance() {
    if (!Cognito.#instance) {
      Cognito.#instance = new AWS.CognitoIdentityServiceProvider();
    }
    return Cognito.#instance;
  }
}

class LambdaInvoker {
  static #instance;

  static get instance() {
    if (!LambdaInvoker.#instance) {
      LambdaInvoker.#instance = new AWS.Lambda();
    }
    return LambdaInvoker.#instance;
  }
}

class GraphQL {
  static #appSyncClient;

  static get instance() {
    if (!GraphQL.#appSyncClient) {
      GraphQL.#appSyncClient = new AWSAppSyncClient({
        url: appsyncUrl,
        region: REGION,
        auth: {
          type: AUTH_TYPE.AWS_IAM,
          credentials: AWS.config.credentials,
        },
        disableOffline: true,
      });
    }

    return GraphQL.#appSyncClient;
  }
}

module.exports = {
  SES,
  LambdaInvoker,
  Cognito,
  GraphQL,
};
