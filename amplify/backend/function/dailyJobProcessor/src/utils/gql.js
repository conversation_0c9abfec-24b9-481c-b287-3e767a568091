const fetchUnreadNotifications = `
query MyQuery($filter: ModelNotificationFilterInput, $limit: Int , $nextToken: String) {
  listNotifications(filter: $filter, limit: $limit, nextToken: $nextToken) {
    items {
      id
      Message
      Type
      To {
        email
      }
    }
  }
}`;

const updateEmailSentNotification = `
    mutation UpdateNotification($input: UpdateNotificationInput!) {
        updateNotification(input: $input) {
        id
        isEmailSent
        }
    }
`;

const fetchTimeSheets = `
  query ListTimeSheets($limit: Int, $nextToken: String, $startTimeFrom: Int, $startTimeTo: Int) {
    listTimeSheets(
      filter: {
        start_time: { between: [$startTimeFrom, $startTimeTo] }
      }
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        start_time
        end_time
        description
        employeeID
        projectBucket
        approval_status
        employee {
          first_name
          last_name
          email
        }
        project {
          name
        }
      }
      nextToken
    }
  }
`;

/**
 * If it is weekend then fetch only date specific employees as we do not want process for the timesheet reminder for all employees
 * Not then process all employees
 */
function listEmployees(isWeekend) {
  return `
      query FetchEmployeeDetails($limit: Int, $nextToken: String${
        isWeekend ? ", $date: String" : ""
      }) {
        listEmployees(
          filter: {
            active: { eq: true }
            ${isWeekend ? "york_start_date: { eq: $date }" : ""}
          }
          limit: $limit
          nextToken: $nextToken
        ) {
          items {
            email
            first_name
            last_name
            york_start_date
            guild {
              name
            }
            title {
              name
            }
            reporting_to {
              first_name
              last_name
              email
            }
            squad {
              squad_manager {
                first_name
                last_name
                email
              }
            }
            guildEmployeeId
          }
        }
      }
    `;
}

const listExecutableRules = `
query MyQuery($filter: ModelExecutionRuleFilterInput,$limit:Int,$nextToken:String) {
  listExecutionRules(filter: $filter,limit: $limit,nextToken:$nextToken ) {
    items {
      id
      frequency
      title
      description
      completionType
      allowedDelay
    }
  }
}
`;

const createExecutable = `
  mutation MyMutation($executableRuleId: ID, $expiry: AWSTimestamp!, $status: String) {
    createExecutable(
      input: {executableRuleId: $executableRuleId, expiry: $expiry, status: $status}
    ) {
      id
      createdAt
    }
  }
`;

module.exports = {
  fetchUnreadNotifications,
  updateEmailSentNotification,
  listEmployees,
  fetchTimeSheets,
  listExecutableRules,
  createExecutable,
};
