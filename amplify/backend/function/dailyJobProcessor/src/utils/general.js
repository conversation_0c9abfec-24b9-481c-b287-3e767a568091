const { addDays } = require("date-fns/addDays");
const { format } = require("date-fns/format");
const { subMonths } = require("date-fns/subMonths");

const capitalize = (str) => {
  if (!str) return "";
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

const formatName = (first, last) =>
  [first, last].filter(Boolean).map(capitalize).join(" ");

const groupByManager = (map, key, employee) => {
  if (!key) return;
  if (!map.has(key)) map.set(key, []);
  map.get(key).push(employee);
};

const addEmailParams = (map, role, subject, sesParamsList, emailClient) => {
  for (const [email, employees] of map.entries()) {
    const emailBody = emailClient.generateEmailBody(employees, role);
    sesParamsList.push({
      Destination: { ToAddresses: [email] },
      TemplateData: JSON.stringify({
        subject,
        body: emailBody,
      }),
    });
  }
};

const getDateSixMonthBefore = () => {
  const today = new Date();
  const targetDate = addDays(today, 15);
  return format(subMonths(targetDate, 6), "yyyy-MM-dd");
};
module.exports = {
  capitalize,
  formatName,
  groupByManager,
  addEmailParams,
  getDateSixMonthBefore,
};
