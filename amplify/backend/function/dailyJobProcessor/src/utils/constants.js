const USERPOOL_ID = process.env.AUTH_YORKHRMSFA131FA1_USERPOOLID;
const TARGET_FUNCTION = process.env.FUNCTION_BUCKETUSAGESREMINDER_NAME;
const ENV = process.env.ENV;
const REGION = process.env.REGION;
const FALLBACK_HR_EMAIL = process.env.FALLBACK_HR_EMAIL;
const COGNITO_HR_GRP_NAME = process.env.COGNITO_HR_GRP_NAME || "Hr";
const COGNITO_EXECUTIVE_GRP_NAME = process.env.COGNITO_EXECUTIVE_GRP_NAME || "Executive";

// --- Executable rules frequency ---
const FREQUENCY = {
  WEEKLY: "WEEKLY",
  MONTHLY: "MONTHLY",
  QUARTERLY: "QUARTERLY",
  DAILY: "DAILY",
};

const EXECUTABLE_STATUS = {
  PENDING: "PENDING",
  COMPLETED: "COMPLETED",
};
module.exports = {
  USERPOOL_ID,
  TARGET_FUNCTION,
  ENV,
  FALLBACK_HR_EMAIL,
  COGNITO_HR_GRP_NAME,
  COGNITO_EXECUTIVE_GRP_NAME,
  REGION,
  ...FREQUENCY,
  ...EXECUTABLE_STATUS,
};
