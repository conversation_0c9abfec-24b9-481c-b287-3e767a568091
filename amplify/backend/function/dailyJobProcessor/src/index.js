const { getDay } = require("date-fns");
const cognitoClient = require("./services/cognitoOperations");
const { TARGET_FUNCTION, ENV } = require("./utils/constants");
const { queryOps } = require("./services/graphQLOperations");
const { getDateSixMonthBefore } = require("./utils/general");
const { LambdaInvoker } = require("./services/awsServiceClient");
const handleCreationOfExecutableRules = require("./helpers/handleExecutablesRules");
const handlePendingTimesheetNotifyer = require("./helpers/handlePendingTimesheetNotifyer");
const processUnreadNotifications = require("./helpers/processUnreadNotifications");
const handleSixMonthEmpAnniversary = require("./helpers/handleSixMonthEmpAnniversary");
const { wrapHandler, captureError } = require("/opt/sentryWrapper");

/**
 * Asynchronously invokes a specified AWS Lambda function if the current day is Monday or Thursday.
 * Utilizes the LambdaInvoker singleton to trigger the function with an asynchronous invocation type.
 * Logs the success or error of the invocation process.
 */
const invokeLambda = async () => {
  try {
    const today = new Date();
    const dayOfWeek = getDay(today);

    // Check if the current day is Monday (1) or Thursday (4)
    if (dayOfWeek === 1 || dayOfWeek === 4) {
      const lambdaClient = LambdaInvoker.instance;
      const response = await lambdaClient
        .invoke({
          FunctionName: TARGET_FUNCTION,
          InvocationType: "Event",
          Payload: JSON.stringify({}),
        })
        .promise();
      console.log("✅ Lambda function invoked successfully ", response);
    }
  } catch (error) {
    console.error("❌ Error during invoking lambda function...", error);
    captureError(error);
  }
};

exports.handler = wrapHandler(async (event) => {
  try {
    console.log(`EVENT: ${JSON.stringify(event)}`);

    // ! DO NOT REMOVE THIS CONDITION TO AVOID GETTING MAILS ON STAGE ENVIRONMENT
    if (ENV !== "prod") {
      return {
        statusCode: 200,
        body: JSON.stringify("Done"),
      };
    }
    await invokeLambda();
    const dayOfWeek = getDay(new Date());
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 1;
    const dateOfSixMonthAgo = getDateSixMonthBefore();

    // Fetch employees and HR emails with error handling
    const employees = await queryOps.fetchEmployeeDetails(isWeekend, dateOfSixMonthAgo);
    
    let hrEmails = [];
    try {
      hrEmails = await cognitoClient.getHRGroupEmail();
      console.log(`Successfully fetched ${hrEmails.length} HR emails`);
    } catch (error) {
      console.error("Failed to fetch HR emails, using fallback:", error);
      // Use fallback HR email if available
      if (process.env.FALLBACK_HR_EMAIL) {
        hrEmails = [process.env.FALLBACK_HR_EMAIL];
        console.log(`Using fallback HR email: ${process.env.FALLBACK_HR_EMAIL}`);
      } else {
        console.warn("No HR emails available and no fallback configured");
      }
    }

    await handleSixMonthEmpAnniversary(
      isWeekend,
      employees,
      hrEmails,
      dateOfSixMonthAgo
    );

    if (!isWeekend) {
      // Add business logic should not run on Weekend | Holiday
      await Promise.all([
        handlePendingTimesheetNotifyer(employees),
        processUnreadNotifications(),
      ]);
    }

    // Always create applicable executables (runs unconditionally)
    await handleCreationOfExecutableRules();
    console.log("Task is completed for ", new Date());
    return {
      statusCode: 200,
      body: JSON.stringify("Task Completed!"),
    };
  } catch (error) {
    console.log("Error during executing daily lambda processor", error);
    captureError(error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Something went wrong! Please try again later",
        error: error?.message || error,
      }),
    };
  }
});
