const { endOfDay } = require("date-fns/endOfDay");
const { startOfDay } = require("date-fns/startOfDay");
const { subDays } = require("date-fns/subDays");
const { queryOps } = require("../services/graphQLOperations");
const cognitoClient = require("../services/cognitoOperations");
const emailClient = require("../services/emailOperations");
const { captureError } = require("/opt/sentryWrapper");

/**
 * Asynchronously processes a list of employees to notify them about pending timesheets.
 *
 * This function retrieves the emails of employees who have not submitted their timesheets
 * for the previous day and sends them a reminder email. It excludes users who are part of
 * the executive HR group from receiving these notifications.
 *
 * @param {Array} employees - An array of employee objects, each containing an email property.
 *
 * @returns {Promise<void>} - A promise that resolves when the notification process is complete.
 *
 * @throws Will log an error message if there is an issue during the notification process.
 */
const handlePendingTimesheetNotifyer = async (employees) => {
  try {
    const emails = employees?.map((emp) => emp.email);
    const yesterday = subDays(new Date(), 1);
    const yesterdayStart = startOfDay(yesterday);
    const yesterdayEnd = endOfDay(yesterday);
    const yesterdayStartInt = Math.floor(yesterdayStart.getTime() / 1000);
    const yesterdayEndInt = Math.floor(yesterdayEnd.getTime() / 1000);
    const timesheetData = await queryOps.fetchTimeSheetsInRange(
      yesterdayStartInt,
      yesterdayEndInt
    );

    // Find emails of users who haven't created a TimeSheet entry yesterday
    let emailsWithoutTimesheet = emails.filter((email) => {
      return !timesheetData.some((item) => email === item.employeeID);
    });
    console.log(`EMAILS-WITHOUT-TIMESHEET: ${emailsWithoutTimesheet}`);

    // Send Reminder email
    for (const email of emailsWithoutTimesheet) {
      const isPowerUser = await cognitoClient.isUserInExecutiveHrGroup(email);
      // Do not send mail to power user
      if (isPowerUser) continue;
      const response = await emailClient.sendEmail({
        TemplateData: JSON.stringify({
          subject: "Reminder: Log Your Hours in Timesheet!",
          body: "<p>Hi,</p><p>It appears that you have not filled timesheet yesterday. Please ensure that you are logging your hours correctly so that system does not detect it as a leave.(Ignore if its leave)</p>",
        }),
        Destination: { ToAddresses: [email] },
      });
      console.log(
        `emailsWithoutTimesheetTimesheet reminder Email sent to ${email}: ${
          response.MessageId || ""
        }`
      );
    }
    console.log("✅ Processed Pending timesheet reminder successfully !");
  } catch (error) {
    captureError(error);
    console.error(
      `Error during notifying employees for the pending timesheet`,
      error
    );
  }
};

module.exports = handlePendingTimesheetNotifyer;
