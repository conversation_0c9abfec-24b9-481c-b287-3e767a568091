const emailClient = require("../services/emailOperations");
const { groupByManager, addEmailParams } = require("../utils/general");
const { captureError } = require("/opt/sentryWrapper");

const sendAnniversaryNotifications = async (employees, hrEmails) => {
  try {
    const sesParamsList = [];
    const hrEmailBody = emailClient.generateEmailBody(employees, "HR");
    sesParamsList.push({
      Destination: { ToAddresses: hrEmails },
      TemplateData: JSON.stringify({
        subject: "Upcoming 6-Month Employee Anniversaries",
        body: hrEmailBody,
      }),
    });

    // --- MANAGER EMAILS ---
    const managersMap = new Map();
    const squadManagersMap = new Map();
    for (const emp of employees) {
      groupByManager(managersMap, emp.reporting_to?.email, emp);
      groupByManager(squadManagersMap, emp.squad?.squad_manager?.email, emp);
    }
    addEmailParams(
      managersMap,
      "MANAGER",
      "Upcoming 6-Month Work Anniversary of Your Team Member(s)",
      sesParamsList,
      emailClient
    );

    addEmailParams(
      squadManagersMap,
      "SQUAD_MANAGER",
      "Upcoming 6-Month Work Anniversary of Your Squad Member(s)",
      sesParamsList,
      emailClient
    );

    await Promise.all(
      sesParamsList.map(async (param) => await emailClient.sendEmail(param))
    );
    console.log(
      `✅ Sent anniversary notifications to HR, ${managersMap.size} managers, and ${squadManagersMap.size} squad managers.`
    );
  } catch (error) {
    throw error;
  }
};

/**
 * Handles the notification process for employees reaching their six-month work anniversary.
 *
 * @param {...any} args - The arguments include:
 *   - isWeekend (boolean): Indicates if the current day is a weekend.
 *   - employees (Array): List of employee objects.
 *   - hrEmails (Array): List of HR email addresses.
 *   - dateOfSixMonthAgo (string): The date string representing six months ago.
 *
 * @returns {void}
 *
 * Logs a message if no HR emails are provided. Filters employees based on their start date
 * and sends notifications if applicable. Logs an error if the notification process fails.
 */
const handleSixMonthEmpAnniversary = async (...args) => {
  try {
    const [isWeekend, employees, hrEmails, dateOfSixMonthAgo] = args;
    if (hrEmails.length === 0) {
      console.log(
        "NO Hr Emails found to send the notifications of completion of six month anniversary."
      );
      return;
    }
    // Filter the employees whose date are squad_manager
    const employeesToNotify = isWeekend
      ? employees
      : employees.filter((emp) => emp?.york_start_date === dateOfSixMonthAgo);
    if (employeesToNotify.length > 0) {
      await sendAnniversaryNotifications(employeesToNotify, hrEmails);
    } else {
      console.log("No employees with upcoming 6-month anniversaries found.");
    }
  } catch (error) {
    captureError(error);
    console.error(
      "Error during notifying employee six month anniversary",
      error
    );
  }
};

module.exports = handleSixMonthEmpAnniversary;
