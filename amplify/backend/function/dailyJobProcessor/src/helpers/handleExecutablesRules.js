const {
  MONTHLY,
  WEEKLY,
  QUARTERLY,
  DAILY,
  PENDING,
} = require("../utils/constants");
const { getDay } = require("date-fns/getDay");
const { getMonth } = require("date-fns/getMonth");
const { queryOps, mutationsOps } = require("../services/graphQLOperations");
const { addDays } = require("date-fns/addDays");
const { captureError } = require("/opt/sentryWrapper");

const getFrequencies = () => {
  const today = new Date();
  const dayOfWeek = getDay(today);
  const dayOfMonth = today.getDate();
  const currentMonth = getMonth(today) + 1;

  const frequencies = [
    DAILY,
    ...(dayOfWeek === 1 ? [WEEKLY] : []), // Monday
    ...(dayOfMonth === 1 ? [MONTHLY] : []), // 1st of the month
    ...([1, 4, 7, 10].includes(currentMonth) ? [QUARTERLY] : []), // Jan, Apr, Jul, Oct consider as a QUARTERLY month
  ];
  return frequencies;
};

/**
 * Fetches executable rules based on the current frequencies.
 *
 * This function retrieves the current frequencies using the `getFrequencies` function,
 * then queries the rules that match these frequencies using `queryOps.fetchRulesByFrequency`.
 *
 * @returns {Promise<Array>} A promise that resolves to an array of executable rules.
 */
const fetchExecutableRules = async () => {
  const frequencies = getFrequencies();
  const executableRules = await queryOps.fetchRulesByFrequency(frequencies);
  return executableRules;
};

const createEntry = async (executableRules) => {
  if (!Array.isArray(executableRules)) return;

  await Promise.all(
    executableRules?.map(async (rule) => {
      try {
        const { id, allowedDelay } = rule;
        let expiryAt = new Date();
        if (allowedDelay) {
          // Consider allowedDelay are in days
          expiryAt = addDays(expiryAt, +allowedDelay);
        }
        expiryAt = Math.floor(expiryAt.getTime() / 1000);
        const input = {
          executableRuleId: id,
          status: PENDING,
          expiry: expiryAt,
        };
        await mutationsOps.createExecutable(input);
        console.log(`✅ Entry created for rule ID: ${id}`);
      } catch (error) {
        console.error(
          `❌ Failed to create entry for rule ID: ${rule?.id}`,
          error
        );
        captureError(error);
      }
    })
  );
};

/**
 * Handles the creation of executable rules based on their frequency.
 *
 * This function retrieves the current frequencies (DAILY, WEEKLY, MONTHLY, QUARTERLY)
 * and fetches executable rules from a GraphQL service using these frequencies.
 * It then creates entries for each rule, setting their status to "PENDING" and
 * calculating an expiry time based on any allowed delay specified in the rule.
 *
 * The function logs the total number of executable tasks created or logs an error
 * if the process fails.
 */
const handleCreationOfExecutableRules = async () => {
  try {
    const rules = await fetchExecutableRules();
    await createEntry(rules);
    console.log(
      `✅ Total ${rules.length} Executable Tasks are created successfully for date.`,
      new Date()
    );
  } catch (error) {
    console.error("❌ Error during creating executable rules", error);
    captureError(error);
  }
};

module.exports = handleCreationOfExecutableRules;
