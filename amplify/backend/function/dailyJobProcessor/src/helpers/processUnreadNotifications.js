const emailClient = require("../services/emailOperations");
const { queryOps, mutationsOps } = require("../services/graphQLOperations");
const { captureError } = require("/opt/sentryWrapper");

/**
 * Processes unread notifications by fetching them, grouping by email, and sending
 * a notification email to each user with their unread messages. Limits the number
 * of messages per email to 10 and formats them in HTML. Updates the notification
 * status to indicate that an email has been sent. Logs success or error messages
 * during the process.
 */
const processUnreadNotifications = async () => {
  try {
    const notifications = await queryOps.fetchUnreadNotifications();

    // Group notifications by email
    const notificationsByEmail = notifications?.reduce(
      (acc, { id, Message, To }) => {
        const email = To?.email || "Unknown";
        const message = Message || "No Message";

        if (!acc[email]) {
          acc[email] = [];
        }
        acc[email].push({
          message,
          notification_id: id,
        });
        return acc;
      },
      {}
    );

    const updateEmailFlag = [];
    // Send email to each user with unread notifications
    for (const [email, notificationDetails] of Object.entries(
      notificationsByEmail
    )) {
      // Sort messages and limit to 10
      const limitedMessages = notificationDetails
        .sort((a, b) => b.message.localeCompare(a.message))
        .slice(0, 10);

      // Format notifications with HTML
      let notificationsText = limitedMessages
        .map((detail) => `- ${detail.message}`)
        .join("<br/>");

      if (notificationDetails.length > 10) {
        notificationsText +=
          "<br/><br/>Don't miss out on important updates. Click to view your notifications: hub.york.ie";
      }

      // Send the email
      try {
        const response = await emailClient.sendEmail({
          TemplateData: JSON.stringify({
            subject: "You Have Unread Notifications in York IE HUB!",
            body: `<p>Hi,</p><p>You have unread notifications waiting for you in your account. Please take a moment to review them.</p><p>${notificationsText}</p>`,
          }),
          Destination: { ToAddresses: [email] },
        });
        console.log(`Email sent to ${email}: ${response.MessageId || ""}`);
      } catch (error) {
        console.error(
          `Error sending notification email to ${email} with notification text: ${notificationsText}`,
          error
        );
        captureError(error);
      }
      updateEmailFlag.push(limitedMessages);
    }
    // Update isEmailSent status for sent notifications
    await Promise.all(
      updateEmailFlag?.map(async ({ notification_id: id }) => {
        if (id) {
          await mutationsOps.updateIsEmailSent(id);
        }
      })
    );
    console.log(`✅ Unread notifications processed successfully !`);
  } catch (error) {
    captureError(error);
    console.error("Error during handling unread notifications ", error);
  }
};

module.exports = processUnreadNotifications;
