const GET_DEFAULT_PROMPT = (config) => `
You are an expert code reviewer. You will be given a code diff. Your job is to analyze the code changes and provide a realistic, unbiased, and critical assessment.
Be strict and do not give high scores unless the code is truly exceptional.
Consider the following when scoring:
- If the change is trivial, boilerplate, or only minor, do not give a score above 7.
- Only give a score of 9 or 10 if the code is exceptionally well-written, impactful, and follows all best practices.
- Penalize for missing comments, poor formatting, lack of modularity, high complexity, duplicated code, or security issues.
- If the code introduces bugs, security issues, or is hard to maintain, score below 6.
- If the code is average, functional but not outstanding, score between 6 and 7.
- If the code is below average, with clear issues, score between 3 and 5.
- If the code is very poor, broken, or dangerous, score 2 or below.
- Justify the score in detail, referencing specific issues or strengths.
- Do not be lenient. Most code should score between 5 and 8 unless it is truly outstanding or very poor.
- Consider the size and impact of the change: small changes should not get a perfect score unless they are flawless and impactful.
- Be realistic and critical, as if you are reviewing code for a high-stakes production system.
- IMPORTANT: You are reviewing a diff, so only consider the addition of code, not the deletion part of the diff. Ignore deletions when assessing quality.
- consider following points for rating the diff and it should include in the response - list-of-points-toggled-as-true-for-the-specific-project : ${JSON.stringify(
  config
)}
`;

// ! IF any fields change please review all the constants to adhere to the fields consistency
const CODE_REVIEW_CHECKLIST_DETAIL = {
  areCodeChangesOptimized: {
    type: "boolean",
    description:
      "If the code changes are optimized for performance and readability.",
    label: "Are Code Changes Optimized",
    slackField: true,
  },
  areCodeChangesRelative: {
    type: "boolean",
    description: "Whether the code changes are relevant and necessary.",
    label: "Are Code Changes Relative",
    slackField: true,
  },
  isCodeFormatted: {
    type: "boolean",
    description: "If the code adheres to standard formatting guidelines.",
    label: "Is Code Formatted",
    slackField: true,
  },
  isCodeWellWritten: {
    type: "boolean",
    description: "If the code is written clearly and understandably.",
    label: "Is Code Well Written",
    slackField: true,
  },
  areCommentsWritten: {
    type: "boolean",
    description:
      "Whether the code has adequate comments explaining the changes. If it's all removal of code, it's true. This is applicable when there are functions written.",
    label: "Are Comments Written",
    slackField: true,
  },
  score: {
    type: "number",
    description:
      "A score out of 10 reflecting the overall quality of the code changes considering all these parameters and the number of lines changed. Small changes should not score 10/10 unless they are exceptionally well-written and impactful.",
    label: "Score",
    suffix: "/10",
  },
  cyclomaticComplexityScore: {
    type: "number",
    description:
      "The cyclomatic complexity score of the code changes, indicating the number of linearly independent paths through the code. Higher scores indicate higher complexity.",
    label: "Cyclomatic Complexity Score",
  },
  missingElements: {
    type: "string",
    description:
      "Any missing elements such as error handling, input validation, or additional recommendations for improvement. Ignore unit tests related comments.",
    label: "Missing Elements",
  },
  loopholes: {
    type: "string",
    description:
      "Any potential edge case, race conditions, concurrency issue, security vulnerabilities, or logic errors that could lead to unexpected behavior.",
    label: "Loopholes",
  },
  isCommitMessageWellWritten: {
    type: "boolean",
    description:
      "Whether the commit message clearly explains the purpose of the changes.",
    label: "Is Commit Message Well Written",
    slackField: true,
  },
  isNamingConventionFollowed: {
    type: "boolean",
    description:
      "Whether code changes follow the naming convention as per the old code.",
    label: "Is Naming Convention Followed",
    slackField: true,
  },
  areThereAnySpellingMistakes: {
    type: "boolean",
    description:
      "Whether code changes have any spelling or grammatical errors.",
    label: "Are There Any Spelling Mistakes",
    reverse: true, // If true then consider this as a negative feedback
    slackField: true,
  },
  securityConcernsAny: {
    type: "boolean",
    description: "Whether the written code has any possible security loophole.",
    label: "Security Concerns Any",
    reverse: true, // If true then consider this as a negative feedback
    slackField: true,
  },
  isCodeDuplicated: {
    type: "boolean",
    description:
      "Whether the code contains any duplicated logic or code blocks.",
    label: "Is Code Duplicated",
    reverse: true, // If true then consider this as a negative feedback
    slackField: true,
  },

  areConstantsDefinedCentrally: {
    type: "boolean",
    description:
      "Whether constants are defined in a single place for better maintainability, so that changing in one place in the future will reflect everywhere.",
    label: "Are Constants Defined Centrally",
  },
  isCodeModular: {
    type: "boolean",
    description:
      "Whether the code is modular and follows the best practices of the language.",
    label: "Is Code Modular",
  },
  isLoggingDoneProperly: {
    type: "boolean",
    description: "Whether logging is done properly in the code.",
    label: "Is Logging Done Properly",
  },
  scoreReason: {
    type: "string",
    description: "The reason or justification for the given score.",
    label: "Score Reason",
    related: "score",
  },
  cyclomaticComplexityReason: {
    type: "string",
    description:
      "Explanation of the cyclomatic complexity score, including factors contributing to the score and potential implications for code maintainability.",
    label: "Cyclomatic Complexity Score Reason",
    related: "cyclomaticComplexityScore",
  },
  spellingMistakes: {
    type: "string",
    description: "Explain all the spelling mistakes.",
    label: "Spelling Mistakes",
    related: "areThereAnySpellingMistakes",
  },
  securityConcernsReason: {
    type: "string",
    description:
      "Based on flag securityConcernsAny, if it's yes, explain the reason.",
    label: "Security Concerns Reason",
    related: "securityConcernsAny",
  },
  isCodeDuplicatedReason: {
    type: "string",
    description:
      "if isCodeDuplicated is true, Give example of the code contains any duplicated logic or code blocks.",
    label: "Is Code Duplicated Reason",
    related: "isCodeDuplicated",
  },
  areConstantsDefinedCentrallyReason: {
    type: "string",
    description:
      "if areConstantsDefinedCentrally false, Give the example of constants are defined in a single place for better maintainability, so that changing in one place in the future will reflect everywhere.",
    label: "Are Constants Defined Centrally Reason",
    related: "areConstantsDefinedCentrally",
  },
  isCodeModularReason: {
    type: "string",
    description: "If isCodeModular is false, explain the reason.",
    label: "Is Code Modular Reason",
    related: "isCodeModular",
  },
  isLoggingDoneProperlyReason: {
    type: "string",
    description: "If isLoggingDoneProperly is false, explain the reason.",
    label: "Is Logging Done Properly Reason",
    related: "isLoggingDoneProperly",
  },
};

const REASON_FIELD = {
  isLoggingDoneProperly: "isLoggingDoneProperlyReason",
  isCodeModular: "isCodeModularReason",
  areConstantsDefinedCentrally: "areConstantsDefinedCentrallyReason",
  isCodeDuplicated: "isCodeDuplicatedReason",
  securityConcernsAny: "securityConcernsReason",
  areThereAnySpellingMistakes: "spellingMistakes",
  cyclomaticComplexityScore: "cyclomaticComplexityReason",
  score: "scoreReason",
};

const FEEDBACK_FIELDS = [
  "scoreReason",
  "missingElements",
  "loopholes",
  "securityConcernsReason",
  "spellingMistakes",
];

module.exports = {
  GET_DEFAULT_PROMPT,
  CODE_REVIEW_CHECKLIST_DETAIL,
  FEEDBACK_FIELDS,
  REASON_FIELD,
};
