// Configuration for jobPostAddUpdateProcessor optimization
module.exports = {
  // Batch processing configuration
  BATCH_SIZE: {
    RECORD_PROCESSING: 3,        // Number of DynamoDB records to process in parallel (reduced from 5)
    CANDIDATE_PROCESSING: 3,     // Number of candidates to process in parallel (reduced from 5)
    DATABASE_UPDATES: 3,         // Number of database updates to process in parallel (reduced from 5)
  },
  
  // Limits to prevent timeouts - optimized for minimal data fetching
  LIMITS: {
    MAX_CANDIDATES_TO_PROCESS: 1000,    // Maximum candidates to process per job post (reduced from 50)
    MAX_CANDIDATES_FETCH: 999,         // Maximum candidates to fetch from database (reduced from 100)
    MAX_SKILLS_FETCH: 500,            // Maximum skills to fetch from database (reduced from 1000)
    MAX_SKILLS_TO_PROCESS: 100,       // Maximum skills to process for matching (new)
    MAX_SUCCESSFUL_CANDIDATES: 100,    // Stop processing after finding this many suitable candidates (new)
  },
  
  // Delays to prevent overwhelming external services
  DELAYS: {
    BETWEEN_BATCHES: 150,        // Milliseconds between candidate batches (increased from 100)
    BETWEEN_DB_REQUESTS: 75,     // Milliseconds between database requests (increased from 50)
    BETWEEN_API_CALLS: 300,      // Milliseconds between external API calls (increased from 200)
  },
  
  // Cache configuration
  CACHE: {
    SKILLS_TTL: 10 * 60 * 1000,  // 10 minutes cache for skills (increased from 5 minutes)
  },
  
  // OpenAI configuration
  OPENAI: {
    MODEL: "gpt-4.1-nano",       // Model to use for AI processing
    MAX_RETRIES: 2,              // Maximum retries for OpenAI calls (reduced from 3)
  },
  
  // Performance monitoring
  MONITORING: {
    LOG_PROGRESS_INTERVAL: 5,    // Log progress every N candidates (reduced from 10)
    ENABLE_DETAILED_LOGGING: false, // Enable detailed performance logging
  }
}; 