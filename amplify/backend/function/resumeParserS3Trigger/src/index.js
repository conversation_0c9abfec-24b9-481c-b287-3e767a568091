/* Amplify Params - DO NOT EDIT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIIDOUTPUT
	ENV
	REGION
Amplify Params - DO NOT EDIT */
const { wrapHandler } = require("/opt/sentryWrapper");
const AWS = require("aws-sdk");
const OpenAI = require("openai");
const s3 = new AWS.S3();
const textract = new AWS.Textract();
const queries = require("./appQueries.js");
const openai = new OpenAI({
  apiKey: process.env["OPEN_AI_KEY"],
});
const ses = new AWS.SES();
const mammoth = require("mammoth"); // Library for extracting text from .docx files
const { captureError } = require("/opt/sentryWrapper");

// Function to upload parsed text to S3
const uploadParsedTextToS3 = async (bucket, key, content) => {
  const params = {
    Bucket: bucket,
    Key: key,
    Body: content,
    ContentType: "application/pdf", // Specify content type as PDF if you're saving it as a PDF
  };
  await s3.putObject(params).promise();
  console.log(`File uploaded successfully to ${key}`);
};

// Function to dynamically generate the parsed key
const generateParsedKey = (originalKey, parsedSuffix) => {
  const fileExtension = ".txt";

  // Extract file name and directory path
  const lastDotIndex = originalKey.lastIndexOf(".");
  const baseKey = originalKey.slice(0, lastDotIndex); // Remove the file extension

  // Append '_parsed' before the extension
  const parsedKey = `${baseKey}${parsedSuffix}${fileExtension}`;

  return parsedKey;
};

// Function to parse the resume text and return structured candidate data
const extractCandidateInformation = async (extractedText) => {
  const completion = await openai.chat.completions.create({
    model: "gpt-4.1-nano",
    messages: [
      {
        role: "system",
        content:
          "You are an AI specialized in extracting candidate information from resumes and returning structured data as JSON.",
      },
      {
        role: "user",
        content: `Extract candidate information from the following resume text: ${extractedText}`,
      },
    ],
    response_format: {
      type: "json_schema",
      json_schema: {
        name: "resume_schema",
        schema: {
          type: "object",
          properties: {
            first_name: {
              type: "string",
              description: "First name of the candidate",
            },
            last_name: {
              type: "string",
              description: "Last name of the candidate",
            },
            email: {
              type: "string",
              description: "Email address of the candidate",
            },
            mobile: {
              type: "string",
              description: "Mobile phone number of the candidate",
            },
            location: {
              type: "string",
              description:
                "Location of the candidate, parsed from the address given",
            },
            experience: { type: "number", description: "months of experience" },
            skills: {
              type: "array",
              items: { type: "string" },
              description:
                "Technical Skills of the candidate (coding languages/frameworks/libraries only)",
            },
            past_experience: {
              type: "object",
              description:
                "Past experience details in JSON of keys - designation, past_exeperience, start_date, end_date, employer, currently_working(true/false)",
            },
            education: {
              type: "object",
              description:
                "Education details in JSON of keys - degree, institution, edu_start_date, edu_end_date, currently_pursuing (true/false)",
            },
            notice_period: {
              type: "integer",
              description: "Notice period in days",
            },
            portfolio_link: {
              type: "string",
              description: "Portfolio link URL",
            },
            github_link: { type: "string", description: "GitHub profile link" },
            other_link: { type: "string", description: "Other relevant link" },
            linkedin_profile: {
              type: "string",
              description: "LinkedIn profile URL",
            },
          },
          additionalProperties: false,
        },
      },
    },
  });

  const structuredData = completion.choices[0].message.content;
  return structuredData;
};

const startDocumentTextDetection = async (bucket, key) => {
  const params = {
    DocumentLocation: {
      S3Object: {
        Bucket: bucket,
        Name: key,
      },
    },
  };
  console.log(params);
  try {
    console.log("starting startDocumentTextDetection");
    const response = await textract
      .startDocumentTextDetection(params)
      .promise();
    const jobId = response.JobId;
    console.log(`Started text detection job with JobId: ${jobId}`);
    return jobId;
  } catch (err) {
    console.error("Error starting text detection job:", err);
    throw err;
  }
};

const getDocumentTextDetection = async (jobId) => {
  let jobCompleted = false;
  let retries = 0;
  const maxRetries = 900;
  const delay = 1000;

  while (!jobCompleted && retries < maxRetries) {
    try {
      const params = { JobId: jobId };
      const response = await textract
        .getDocumentTextDetection(params)
        .promise();
      const jobStatus = response.JobStatus;
      console.log(`Job Status: ${jobStatus}`);

      if (jobStatus === "SUCCEEDED") {
        const extractedText = response.Blocks.filter(
          (block) => block.BlockType === "LINE"
        )
          .map((block) => block.Text)
          .join("\n");
        console.log("Extracted Text:", extractedText);
        return extractedText;
      } else if (jobStatus === "FAILED") {
        console.error("Document text detection job failed.");
        throw new Error("Document text detection job failed.");
      } else {
        console.log(
          `Job is still in progress. Retrying in ${delay / 1000} seconds...`
        );
        retries++;
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    } catch (err) {
      console.error("Error fetching job status:", err);
      throw err;
    }
  }
  throw new Error("Job did not complete within the maximum retry limit.");
};

const extractTextFromDocx = async (bucket, key) => {
  const params = {
    Bucket: bucket,
    Key: key,
  };
  const s3Object = await s3.getObject(params).promise();
  const extractedText = await mammoth.extractRawText({ buffer: s3Object.Body });
  return extractedText.value;
};

async function createCandidateWithSkills(candidateData, s3key) {
  // Validate required fields
  if (!candidateData.email || !candidateData.email.trim()) {
    throw new Error("Candidate email is required and cannot be empty");
  }
  
  if (!candidateData.first_name || !candidateData.first_name.trim()) {
    throw new Error("Candidate first name is required and cannot be empty");
  }
  
  if (!candidateData.last_name || !candidateData.last_name.trim()) {
    throw new Error("Candidate last name is required and cannot be empty");
  }

  const candidateInput = {
    first_name:
      candidateData.first_name.trim().charAt(0).toUpperCase() +
      candidateData.first_name.trim().slice(1).toLowerCase(),
    last_name:
      candidateData.last_name.trim().charAt(0).toUpperCase() +
      candidateData.last_name.trim().slice(1).toLowerCase(),
    email: candidateData.email.trim(),
    mobile: candidateData.mobile,
    location: candidateData.location,
    exeperience: candidateData.experience,
    notice_period: candidateData.notice_period,
    education: JSON.stringify(candidateData.education),
    past_exeperience: JSON.stringify(candidateData.past_experience),
    portfolio_link: candidateData.portfolio_link,
    github_link: candidateData.github_link,
    other_link: candidateData.other_link,
    linkedin_profile: candidateData.linkedin_profile,
    candidate_source: "System",
    s3_resume_key: s3key.replace(/^public\//, ""),
    status: "New",
    candidateReffered_byId: candidateData?.candidateReffered_byId,
    externalReferralsReferralsId: candidateData?.externalReferralsReferralsId,
    candidateExternal_referral_detailedId:
      candidateData?.externalReferralsReferralsId,
  };

  console.log(candidateInput);
  const createCandidateResponse = await queries?.createCandidate({
    ...candidateInput,
  });
  const candidateId = createCandidateResponse.id;
  console.log(candidateId);
  if (!candidateId) {
    throw new Error("Candidate creation failed");
  }

  for (const skill of candidateData.skills) {
    const skillInput = { candidateID: candidateId, skillID: skill };
    console.log(skillInput);
    const addSkillsResponse = await queries?.addSkills({ ...skillInput });
    console.log(addSkillsResponse);
  }

  console.log("Candidate created and skills added successfully!");
  return candidateId;
}

const uuidRegex =
  /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

exports.handler = wrapHandler(async (event) => {
  console.log(`EVENT: ${JSON.stringify(event)}`);
  const records = event.Records || [];

  for (const record of records) {
    const { eventName, s3 } = record;
    const objectKey = decodeURIComponent(s3.object.key.replace(/\+/g, " "));
    const bucket = s3.bucket.name;

    const segments = objectKey.split("/");
    let referral_email = segments.length === 4 ? segments[2] : null;
    
    // Validate and clean referral_email
    if (referral_email && typeof referral_email === 'string') {
      referral_email = referral_email.trim();
      if (referral_email === '') {
        referral_email = null;
      }
    }

    // TODO: manage external Referrals
    /**
     * If referral_email is UUID type then add external_referral_id while creating candidate
     * If referral_email available then add @york.ie (already done)
     */

    if (
      eventName === "ObjectCreated:Put" &&
      objectKey.startsWith("public/resumes") &&
      !objectKey.endsWith("_parsed.txt") &&
      !objectKey.endsWith("_airesponse.txt")
    ) {
      console.log(`Processing S3 object with key: ${objectKey}`);
      let extractedText;

      if (objectKey.endsWith(".doc") || objectKey.endsWith(".docx")) {
        console.log("Extracting text from DOC/DOCX file");
        extractedText = await extractTextFromDocx(bucket, objectKey);
      } else {
        console.log("Starting text detection job");
        const job_id = await startDocumentTextDetection(bucket, objectKey);
        console.log("Starting looping");
        extractedText = await getDocumentTextDetection(job_id);
      }

      console.log(extractedText);

      if (extractedText) {
        const extractedbuffer = Buffer.from(extractedText, "utf-8");
        const parsedKey = generateParsedKey(objectKey, "_parsed");
        await uploadParsedTextToS3(bucket, parsedKey, extractedbuffer);
        const parsedData = await extractCandidateInformation(extractedText);
        console.log(parsedData);
        const airesponsebuffer = Buffer.from(parsedData, "utf-8");
        const airesponseKey = generateParsedKey(objectKey, "_airesponse");
        await uploadParsedTextToS3(bucket, airesponseKey, airesponsebuffer);
        const candidateData = JSON.parse(parsedData);
        const existingCandidate = await queries?.CheckExistingCandidate(
          candidateData?.email
        );
        console.log(existingCandidate?.getcandidateByEmail?.items);

        if (existingCandidate?.getcandidateByEmail?.items?.length === 0) {
          if (referral_email && uuidRegex.test(referral_email)) {
            candidateData.externalReferralsReferralsId = referral_email;
          } else if (referral_email && referral_email.trim()) {
            candidateData.candidateReffered_byId = referral_email.trim() + "@york.ie";
          }

          const candidateId = await createCandidateWithSkills(
            candidateData,
            objectKey
          ).catch((err) => {
            console.error(err);
            captureError(err);
          });

          const emailBody = `
            <p>Dear ${candidateData.first_name} ${candidateData.last_name},</p>
            <p></p>
            <p>Thank you for your interest in joining the team at York IE! We appreciate the time and effort you’ve put into applying for this position.</p>
            <p></p>
            <p>To move forward with your application, we kindly ask you to complete the following form: https://hub.york.ie/candidateDetails/${candidateId}. This will help us gather additional information and streamline the next steps in our hiring process.</p>
            <p></p>
            <p>If you have any questions or encounter any issues while filling out the form, feel free to reach out to us.</p>
            <p></p>
            <p>Thank you again for applying, and we look forward to reviewing your submission!</p>
          `;
          const subject = `Thanks for Applying at York IE, ${candidateData.first_name} - Next Steps`;
          // Validate candidate email before sending
          if (!candidateData.email || !candidateData.email.trim()) {
            console.error("No valid candidate email found, skipping candidate email");
            return;
          }

          const params = {
            Source: "York IE HR <<EMAIL>>",
            Destination: {
              ToAddresses:
                process.env.ENV === "staging"
                  ? ["<EMAIL>"]
                  : [candidateData.email.trim()],
            },
            Template: "HubGenericNotification",
            TemplateData: JSON.stringify({
              subject: subject,
              body: emailBody,
            }),
          };

          try {
            await ses.sendTemplatedEmail(params).promise();
            console.log(`Email sent`);
          } catch (error) {
            console.error("Error sending email:", error);
            captureError(error);
          }
        } else {
          console.log(
            `Candidate is already in system so Skipping : ${objectKey}`
          );
        }

        if (referral_email) {
          const existingCandidateDetails =
            existingCandidate?.getcandidateByEmail?.items?.[0];

          let isExternalReferrer = false;
          // todo: find name, email
          if (referral_email && uuidRegex.test(referral_email)) {
            // fetch external referrer
            isExternalReferrer = true;
            var referralDetails = await queries.getExternalReferralDetails(
              referral_email
            );
            console.log(referralDetails);
          }

          const emailBodyForCandidateAvailable = `
            <p>Dear ${
              isExternalReferrer ? referralDetails?.name : referral_email
            }</p>
            <p></p>

            <p>Thank you so much for taking the time to refer ${
              existingCandidateDetails?.first_name
            } ${
            existingCandidateDetails?.last_name
          } to us. We truly value your effort in helping us identify talented professionals for our team.</p>
            <p></p>

            <p>We wanted to let you know that ${
              existingCandidateDetails?.first_name
            } ${
            existingCandidateDetails?.last_name
          }'s resume is already in our system, and we are either actively processing their application or have already reviewed it as part of our recruitment efforts. Anyway you can view your referrals anytime by clicking this link
            <p></p>

            <p>
            <a href="https://hub.york.ie/referrals/${referral_email}" target="_blank" style="
                display: inline-block;
                padding: 8px 16px;
                font-size: 14px;
                color: #ffffff;
                background-color: #00b48b;
                text-decoration: none;
                border-radius: 4px;
                text-align: center;
            ">View All Referrals</a>
        </p>
          <p></p>

            We sincerely appreciate your willingness to contribute to our talent acquisition process. Your support and collaboration mean a great deal to us, and we encourage you to continue sharing referrals in the future.</p>

            <p>If you have any questions or would like to share additional insights about the candidate, please don't hesitate to reach out.</p>
          `;

          const emailBodyForNewCandidate = `
            <p>Dear ${
              isExternalReferrer ? referralDetails?.name : referral_email
            }</p>
            <p></p>

            <p>Thank you so much for taking the time to refer ${
              candidateData?.first_name
            } ${
            candidateData?.last_name
          } to us. We truly value your effort in helping us identify talented professionals for our team.</p>
            <p></p>
            
            <p>We are excited to review ${candidateData?.first_name} ${
            candidateData?.last_name
          }'s profile and explore their potential fit within our organization. Our recruitment team will carefully assess the application and keep you updated on the progress.</p>
            <p></p>
            
            <p>Your support in helping us discover talented individuals is invaluable, and we are grateful for your contribution. you can view your referrals anytime by clicking this link</p>
            <p></p>

            <p>
            <a href="https://hub.york.ie/referrals/${referral_email}" target="_blank" style="
                display: inline-block;
                padding: 8px 16px;
                font-size: 14px;
                color: #ffffff;
                background-color: #00b48b;
                text-decoration: none;
                border-radius: 4px;
                text-align: center;
            ">View All Referrals</a>
        </p>
          <p></p>
            <p>If you have any questions or would like to share additional insights about the candidate, please don't hesitate to reach out.</p>
          `;

          const subject = "Thank You for Your Referral!";
          // Validate referral email before sending
          let referralEmailAddress = null;
          if (!isExternalReferrer && referral_email && referral_email.trim()) {
            referralEmailAddress = referral_email.trim() + "@york.ie";
          } else if (isExternalReferrer && referralDetails?.email) {
            referralEmailAddress = referralDetails.email;
          }

          if (!referralEmailAddress) {
            console.warn("No valid referral email address found, skipping referral email");
            return;
          }

          const params = {
            Source: "York IE HR <<EMAIL>>",
            Destination: {
              ToAddresses:
                process.env.ENV === "staging"
                  ? ["<EMAIL>"]
                  : [referralEmailAddress],
            },
            Template: "HubGenericNotification",
            TemplateData: JSON.stringify({
              subject: subject,
              body:
                existingCandidate?.getcandidateByEmail?.items?.length === 0
                  ? emailBodyForNewCandidate
                  : emailBodyForCandidateAvailable,
            }),
          };

          try {
            await ses.sendTemplatedEmail(params).promise();
            console.log(`Email sent`);
          } catch (error) {
            console.error("Error sending email:", error);
          }
        }
      }
    } else {
      console.log(`Skipping record with key: ${objectKey}`);
    }
  }

  return {
    statusCode: 200,
    body: JSON.stringify("Event processing complete"),
  };
});
