class GQL {
  createExecutableHistory = `
        mutation MyMutation($executableHistoryRuleId: ID!, $metaData: AWSJSON, $status: EXECUTABLE_STATUS ) {
          createExecutableHistory(
            input: {executableHistoryRuleId: $executableHistoryRuleId, status: $status, metaData: $metaData}
          ) {
            id
            status
            metaData
          }
        }
    `;

  createNotification = `
        mutation MyMutation($Message: String!, $ToAccount: String , $Type: String, $isEmailSent: Boolean, $timeSensitive: Boolean) {
          createNotification(
            input: {Message: $Message, ToAccount: $ToAccount, Type: $Type, isEmailSent: $isEmailSent, timeSensitive: $timeSensitive}
          ) {
            id
          }
        }
    `;

  getExecutableRule = `
    query MyQuery($id: ID!) {
        getExecutionRule(id: $id) {
          id
          completionType
          title
          description
          authorityGroup {
            id
            leaderEmail
            leader {
              first_name
              last_name
            }
          }
        }
      }
    `;
}

const gql = new GQL();
module.exports = gql;
