/* Amplify Params - DO NOT EDIT
	ENV
	REGION
	API_YORKHRMSGRAPHQL_GRAPHQLAPIIDOUTPUT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT
	SENTRY_DSN
Amplify Params - DO NOT EDIT */

const { mutationsOps, queryOps } = require("./services/graphQLOperations");
const notifyClient = require("./services/NotifyOperations");
const { COMPLETED, MANUAL } = require("./utils/constants");
const AWS = require("aws-sdk");
const { wrapHandler, captureError } = require("/opt/sentryWrapper");

const handleRuleViolation = async (executable, executableRule, triggeredBy) => {
  const {
    authorityGroup: {
      leaderEmail,
      leader: { first_name: leaderFirstName, last_name: leaderLastName },
    },
    title: ruleTitle,
    description,
  } = executableRule;

  const notificationData = {
    Message: `Rule ${ruleTitle} has been violated.`,
    ToAccount: leaderEmail,
    Type: "Rule Violation",
    isEmailSent: true,
    timeSensitive: true,
  };
  const leaderName = `${leaderFirstName} ${leaderLastName}`;
  const emailData = {
    Destination: {
      ToAddresses: [leaderEmail],
    },
    TemplateData: JSON.stringify({
      subject: `⚠️ Rule Violation Alert`,
      body: `
      <p>Hi <strong>${leaderName}</strong>,</p>
      <p>The rule <strong style="color:#1d4ed8;">${ruleTitle}</strong> has been <strong style="color:#dc2626;">violated</strong> by the responsible group.</p>
      <p><strong>Description:</strong></p>
      <blockquote style="background-color:#f8f8f8; padding:10px; border-left:4px solid #e11d48;">
      <em>${description || "No detailed description provided."}</em>
      </blockquote>
      <p>Thank you for your attention to this matter.</p>
      <p style="font-size: 12px; color: #888888;">This is an automated message from the Rule Engine system. Please do not reply to this email.</p>
      `,
    }),
  };
  await notifyClient.notifyLeader(notificationData, emailData, leaderEmail);

  const { status, executableRuleId } = executable;
  const input = {
    executableHistoryRuleId: executableRuleId,
    status: status,
    metaData: JSON.stringify({
      triggeredBy,
      timestamp: Math.floor(new Date().getTime() / 1000),
    }),
  };
  await mutationsOps.createExecutableHistory(input);
};

/**
 * Processes an executable based on its status and completion type.
 *
 * @param {Object} executable - The executable object containing status and rule ID.
 * @param {string} [triggeredBy="UNKNOWN"] - The entity that triggered the process.
 *
 * If the completion type is MANUAL and the status is COMPLETED, it records the
 * executable history. Otherwise, it handles rule violations.
 *
 * TODO: Implement handler for AUTOMATIC completion type.
 */
const processExecutable = async (executable, triggeredBy = "UNKNOWN") => {
  const { status, executableRuleId } = executable;
  const executableRule = await queryOps.getExecutableRuleById(executableRuleId);
  const { completionType } = executableRule;
  if (completionType === MANUAL) {
    if (status === COMPLETED) {
      const input = {
        executableHistoryRuleId: executableRuleId,
        status: status,
        metaData: JSON.stringify({
          triggeredBy,
          timestamp: Math.floor(new Date().getTime() / 1000),
        }),
      };
      await mutationsOps.createExecutableHistory(input);
      return;
    }

    // Handle a violation
    await handleRuleViolation(executable, executableRule, triggeredBy);
  }
  // TODO: Handler for AUTOMATIC completion type
};

const processDBRecord = async (event) => {
  const { Records: dbRecords, triggeredBy } = event;
  for await (const record of dbRecords) {
    try {
      const eventType = record.eventName;
      const oldImage = AWS.DynamoDB.Converter.unmarshall(
        record.dynamodb.OldImage
      );

      switch (eventType) {
        case "REMOVE":
          await processExecutable(oldImage, triggeredBy);
          break;
        default:
          console.info("Handle the Event Type for ", eventType);
      }
      console.log("✅ Executables processed successfully.");
    } catch (error) {
      captureError(error);
      console.error(
        `Failed to process the record : ${error}`,
        JSON.stringify(record)
      );
    }
  }
};

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
/**
 * AWS Lambda handler for processing DynamoDB stream events related to rule execution.
 *
 * This function processes each record from the DynamoDB stream, specifically handling
 * "REMOVE" events to process executable rules. It checks the completion type of the
 * executable rule and handles manual completion by either recording the completion
 * or handling rule violations. The function logs the event and any errors encountered
 * during processing.
 *
 * @param {Object} event - The event object containing DynamoDB stream records.
 * @returns {Promise<string>} A promise that resolves with a success message or rejects with an error message.
 */
exports.handler = wrapHandler(async (event) => {
  try {
    console.log(`EVENT: ${JSON.stringify(event)}`);
    await processDBRecord(event);
    return Promise.resolve("Successfully processed DynamoDB record");
  } catch (error) {
    console.log("Error during executing inspector lambda...", error);
    captureError(error);
    return Promise.reject("Failed to process the inspector lambda function");
  }
});
