const AWS = require("aws-sdk");
const { AUTH_TYPE, default: AWSAppSyncClient } = require("aws-appsync");
const { REGION } = require("../utils/constants");
AWS.config.update({ region: REGION });
const appsyncUrl = process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT;

/*
 *  Singleton pattern to ensure only one instance is created and reused
 */
class GraphQL {
  static #appSyncClient;

  static get instance() {
    if (!GraphQL.#appSyncClient) {
      console.log("appsyncUrl ", appsyncUrl);
      GraphQL.#appSyncClient = new AWSAppSyncClient({
        url: appsyncUrl,
        region: REGION,
        auth: {
          type: AUTH_TYPE.AWS_IAM,
          credentials: AWS.config.credentials,
        },
        disableOffline: true,
      });
    }

    return GraphQL.#appSyncClient;
  }
}

class SES {
  static #instance;

  static get instance() {
    if (!SES.#instance) {
      SES.#instance = new AWS.SES();
    }
    return SES.#instance;
  }
}

module.exports = {
  GraphQL,
  SES,
};
