const { GraphQL } = require("./awsServiceClient");
const queries = require("../utils/gql");
const { default: gql } = require("graphql-tag");

class GraphQLOperations {
  constructor(appSyncClient) {
    this.appSyncClient = appSyncClient;
  }

  /**
   * Fetches all paginated data for a given query and variables.
   * Automatically handles AppSync-style `nextToken` pagination.
   */
  async fetchAllData(query, variables) {
    let items = [];
    let nextToken = null;

    try {
      do {
        const response = await this.executeQuery(query, {
          ...variables,
          limit: 999,
          nextToken,
        });
        if (response.errors) {
          console.error("GraphQL errors: ", response.errors);
          throw new Error(
            "Error occurred while fetching data from GraphQL API."
          );
        }
        const data = response?.data;
        const queryKey = Object.keys(data).find((key) => data[key]?.items);

        if (queryKey) {
          items = items.concat(data[queryKey]?.items || []);
          nextToken = data[queryKey]?.nextToken || null;
        } else {
          nextToken = null;
        }
      } while (nextToken);

      return items;
    } catch (err) {
      console.error("Error in fetchAllData: ", err);
      throw new Error(
        "Failed to fetch all data. Please check the logs for more details."
      );
    }
  }

  async executeQuery(query, variables) {
    try {
      const response = await this.appSyncClient.query({
        query: gql(query),
        variables,
        fetchPolicy: "network-only",
      });
      return response;
    } catch (err) {
      console.log("Error while trying to fetch data", err);
      throw JSON.stringify(err);
    }
  }

  async executeMutation(query, variables) {
    try {
      const response = await this.appSyncClient.mutate({
        mutation: gql(query),
        variables,
        fetchPolicy: "no-cache",
      });
      return response;
    } catch (err) {
      console.log("Error while trying to mutate data", err);
      throw JSON.stringify(err);
    }
  }
}

// --- All Query Operations ---
class QueryOperations extends GraphQLOperations {
  constructor(client) {
    super(client);
  }

  async getExecutableRuleById(id) {
    try {
      const response = await this.executeQuery(queries.getExecutableRule, {
        id,
      });
      return response?.data?.getExecutionRule;
    } catch (error) {
      console.error(`Error during getting executable...`, error);
      throw new Error(error);
    }
  }
}

// --- All Mutation Operations ---

class MutationOperations extends GraphQLOperations {
  constructor(client) {
    super(client);
  }

  async createExecutableHistory(input) {
    try {
      const response = await this.executeMutation(
        queries.createExecutableHistory,
        input
      );
      return response;
    } catch (error) {
      console.error(`Error creating executables history...`, error);
      throw new Error(error);
    }
  }

  async createNotification(input) {
    try {
      const response = await this.executeMutation(
        queries.createNotification,
        input
      );
      return response;
    } catch (error) {
      console.error(`Error creating notification`, error);
      throw new Error(error);
    }
  }
}

// Instantiate query and mutation operations with singleton AppSync client
const queryOps = new QueryOperations(GraphQL.instance);
const mutationsOps = new MutationOperations(GraphQL.instance);

module.exports = {
  queryOps,
  mutationsOps,
};
