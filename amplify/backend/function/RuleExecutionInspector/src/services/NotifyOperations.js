const { SES } = require("./awsServiceClient");
const { mutationsOps } = require("./graphQLOperations");

class NotifyOperations {
  constructor() {
    this.sesClient = SES.instance;
  }

  /**
   * @method sendEmail
   * @param {Object} params - Parameters for sending a templated email.
   * @returns {Promise<Object>} - The response from the SES service.
   * @throws {Error} - Throws an error if email sending fails.

   */
  async sendEmail(params) {
    try {
      const response = await this.sesClient.sendTemplatedEmail({
        Source: "<EMAIL>",
        Template: "HubGenericNotification",
        ...params,
      });
      return response;
    } catch (error) {
      throw new Error(`Error during sending email: ${error}`);
    }
  }

  async sendNotification(inputData) {
    try {
      const response = await mutationsOps.createNotification(inputData);
      return response;
    } catch (error) {
      throw new Error(`Error during creating notifications ${error}`);
    }
  }

  async notifyLeader(notificationData, emailData, leaderEmail) {
    try {
      await Promise.all([
        this.sendNotification(notificationData),
        this.sendEmail(emailData),
      ]);
      console.log(
        "✅ Notification and email sent Successfully to leader ",
        leaderEmail
      );
    } catch (error) {
      console.log(
        `Error during notifying the leader on rule violation: ${error}`
      );
    }
  }
}
// Instantiate NotifyOperation with a singleton client instance
const notifyClient = new NotifyOperations();
module.exports = notifyClient;
