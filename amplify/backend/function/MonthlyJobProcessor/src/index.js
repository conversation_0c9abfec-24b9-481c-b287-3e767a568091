const { queryOps, mutationsOps } = require("./services/graphQLOperations.js");
const dayjs = require("dayjs");
const utc = require("dayjs/plugin/utc");
const timezone = require("dayjs/plugin/timezone");
const { captureError } = require("/opt/sentryWrapper");
const {
  CREDIT_WFH,
  IST_TIMEZONE,
  DATE_TIME_FORMAT,
} = require("./utils/constants");
const { wrapHandler } = require("/opt/sentryWrapper");

// Enable the plugins
dayjs.extend(utc);
dayjs.extend(timezone);

/* Amplify Params - DO NOT EDIT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIIDOUTPUT
	ENV
	REGION
    Amplify Params - DO NOT EDIT */

/**
 * Credits privilege leave to an employee based on the current month.
 * If the current month is October, credits 2 days; otherwise, credits 1 day.
 *
 * @param {string} email - The email of the employee to credit leave for.
 * @param {string} description - A description for the leave credit transaction.
 * @returns {Promise<void>} - A promise that resolves when the leave is credited.
 * @throws Will log an error message if the leave crediting fails.
 */
const creditPrivilegeLeave = async (email, description) => {
  try {
    const date = new Date();
    // months are 0 based indexing
    // If Oct(9+1) the credit 2 else 1
    const plCredit = date.getMonth() + 1 === 10 ? 2 : 1;
    await mutationsOps.createEmployeeLeave({
      type: "PRIVILEGE",
      adjustment_type: "CREDIT",
      leave_length: "FULL_DAY",
      employeeLeavesId: email,
      count: plCredit,
      description: description,
    });

    console.log(
      `Privilege leave ${plCredit} credited successfully for employee ${email}.`
    );
  } catch (error) {
    console.error(`Privilege leave creation failed for ${email}`, error);
    captureError(error);
    
  }
};

/**
 * Generates start and end dates in IST timezone based on the current date and a specified amount of days.
 *
 * @param {number} amount - The number of days to calculate the end date from the current date.
 * @returns {Object} An object containing the start and end dates formatted as strings.
 *      - startDate: The current date and time in "YYYY-MM-DDTHH:mm:ssZ" format.
 *      - endDate: The calculated end date and time in the specified format.
 */
const getISTDates = (amount) => {
  const now = dayjs().tz(IST_TIMEZONE);
  const startDate = now.format("YYYY-MM-DDTHH:mm:ssZ");
  const endDate = now
    .add(amount > 1 ? amount - 1 : 0, "day")
    .format(DATE_TIME_FORMAT);

  return { startDate, endDate };
};

/**
 * Credits Work From Home (WFH) balance for an employee by creating and approving a WFH request.
 *
 * @param {string} email - The email of the employee for whom the WFH balance is credited.
 * @param {number} amount - The amount of WFH days to credit. Determines if it's a full day or half day.
 * @param {string} reason - The reason for crediting the WFH balance.
 * @param {string} [reviewedBy="System"] - The reviewer of the WFH request, defaults to "System".
 *
 * @returns {Promise<void>} - A promise that resolves when the WFH request is created and approved.
 *
 * @throws Will log an error message if the WFH request creation or approval fails.
 */
const creditWFHBalance = async (
  email,
  amount,
  reason,
  reviewedBy = "System"
) => {
  try {
    const { startDate, endDate } = getISTDates(amount);
    const input = {
      startDate: startDate, // e.g., 2025-04-04T14:07:59+05:30
      endDate: endDate,
      reason: reason,
      wfhLength: amount > 0.5 ? "FULL_DAY" : "FIRST_HALF",
      requestedAt: dayjs().tz(IST_TIMEZONE), // current IST time
      employeeId: email,
      adjustment_type: "CREDIT",
      reviewedBy: reviewedBy,
    };
    const result = await mutationsOps.createWFHRequest({ input });
    console.log(`WFH created for ${email}: ${result?.id}`);
    const update = await mutationsOps.approveWFHRequest({
      id: result.id,
      status: "APPROVED",
    });
    console.log(`WFH approved for ${email}: ${update?.id}`);
  } catch (error) {
    console.error(`Error during processing WFH for ${email}: ${error}`);
    captureError(error);
  }
};

/**
 * Sends a quarterly profile review reminder notification to the specified email.
 *
 * This function constructs a notification object with details such as the recipient's email,
 * notification type, message content, and metadata like time sensitivity and creation date.
 * It then uses the organization's mutation operations to create and send the notification.
 *
 * @param {string} email - The email address of the recipient.
 * @returns {Promise<void>} - A promise that resolves when the notification is successfully sent.
 * @throws Will log an error message if the notification creation fails.
 */
const sendProfileReminderNotification = async (email) => {
  try {
    const input = {
      ToAccount: email,
      Type: "Quarterly Profile Review Reminder",
      Message:
        "Please take a few minutes to review and update your profile and skills to reflect any recent changes. Keeping your information up to date ensures better team alignment and helps us support your growth more effectively.",
      timeSensitive: true,
      isRead: false,
      createdAt: new Date(),
    };
    await mutationsOps.createProfileReminderNotification(input);
    console.log(`Profile update reminder notification sent to ${email}...`);
  } catch (error) {
    console.log("Error during creating profile notification ", error);
  }
};

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

exports.handler = wrapHandler(async (event, context) => {
  try {
    console.log("EVENT: ", JSON.stringify(event));

    const allEmployees = await queryOps.listEmployees();
    const currentDate = new Date();
    const time = `${currentDate.toLocaleString("default", {
      month: "long",
    })} ${currentDate.getFullYear()}`;

    // Jan, Apr, July and Oct => Quarterly month
    const isQuarterlyReminderMonth = [1, 4, 7, 10].includes(
      currentDate.getMonth() + 1
    );

    const processEmployees = allEmployees?.map(async (emp) => {
      const { email, account_status, active, hidden_profile } = emp;
      await creditPrivilegeLeave(email, `Credit for ${time}`);
      await creditWFHBalance(
        email,
        CREDIT_WFH,
        `Monthly WFH credit for ${time}`
      );
      if (
        isQuarterlyReminderMonth &&
        (active || account_status === "ACTIVE") &&
        !hidden_profile
      ) {
        await sendProfileReminderNotification(email);
      }
    });

    await Promise.allSettled(processEmployees);
    console.log(`✅ Monthly privilege leave credit operation completed.`);
    console.log(`✅ Monthly WFH credit operation completed.`);
    return {
      statusCode: 200,
      body: JSON.stringify("Monthly leave credited for employees."),
    };
  } catch (error) {
    console.error("Error during crediting monthly leave", error);
    captureError(error);
    return {
      statusCode: 500,
      body: JSON.stringify(error),
    };
  }
});
