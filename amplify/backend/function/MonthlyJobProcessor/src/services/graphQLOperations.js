const { GraphQL } = require("./awsServiceClient");
const { default: gql } = require("graphql-tag");
const queries = require("./gql");

class GraphQLOperations {
  constructor(appSyncClient) {
    this.appSyncClient = appSyncClient;
  }

  /**
   * Fetches all paginated data for a given query and variables.
   * Automatically handles AppSync-style `nextToken` pagination.
   */
  async fetchAllData(query, variables) {
    let items = [];
    let nextToken = null;

    try {
      do {
        const response = await this.executeQuery(query, {
          ...variables,
          limit: 999,
          nextToken,
        });
        if (response.errors) {
          console.error("GraphQL errors: ", response.errors);
          throw new Error(
            "Error occurred while fetching data from GraphQL API."
          );
        }
        const data = response?.data;
        const queryKey = Object.keys(data).find((key) => data[key]?.items);

        if (queryKey) {
          items = items.concat(data[queryKey]?.items || []);
          nextToken = data[queryKey]?.nextToken || null;
        } else {
          nextToken = null;
        }
      } while (nextToken);

      return items;
    } catch (err) {
      console.error("Error in fetchAllData: ", err);
      throw new Error(
        "Failed to fetch all data. Please check the logs for more details."
      );
    }
  }

  async executeQuery(query, variables) {
    try {
      const response = await this.appSyncClient.query({
        query: gql(query),
        variables,
        fetchPolicy: "network-only",
      });
      return response;
    } catch (err) {
      console.log("Error while trying to fetch data", err);
      throw JSON.stringify(err);
    }
  }

  async executeMutation(query, variables) {
    try {
      const response = await this.appSyncClient.mutate({
        mutation: gql(query),
        variables,
        fetchPolicy: "no-cache",
      });
      return response;
    } catch (err) {
      console.log("Error while trying to mutate data", err);
      throw JSON.stringify(err);
    }
  }
}

// --- All Query Operations ---
class QueryOperations extends GraphQLOperations {
  constructor(client) {
    super(client);
  }

  async listEmployees() {
    try {
      // Get those employees whose account status is active (as per new way)
      //  account_status = null or active = true (as per old way)
      return await this.fetchAllData(queries.listEmployees, {
        filter: {
          or: [{ account_status: { eq: "ACTIVE" } }, { active: { eq: true } }],
        },
      });
    } catch (err) {
      console.error("Error in :listEmployees ", err);
      throw new Error("Failed to listEmployees");
    }
  }
}

// --- All Mutation Operations ---

class MutationOperations extends GraphQLOperations {
  constructor(client) {
    super(client);
  }
  async createEmployeeLeave(input) {
    try {
      await this.executeMutation(queries.createLeave, input);
    } catch (err) {
      console.error("Error in :createEmployeeLeave ", err);
      throw new Error("Failed to createEmployeeLeave");
    }
  }

  async createWFHRequest(input) {
    try {
      const response = await this.executeMutation(
        queries.createWFHRequest,
        input
      );
      return response?.data?.createWorkFromHomeRequest;
    } catch (err) {
      console.error("Error in :createWFHRequest ", err);
      throw new Error("Failed to createWFHRequest");
    }
  }

  async approveWFHRequest(input) {
    try {
      const response = await this.executeMutation(
        queries.approveWFHRequest,
        input
      );
      return response?.data?.updateWorkFromHomeRequest;
    } catch (err) {
      console.error("Error in :approveWFHRequest ", err);
      throw new Error("Failed to approveWFHRequest");
    }
  }

  async createProfileReminderNotification(input) {
    try {
      return await this.executeMutation(queries.createNotification, input);
    } catch (err) {
      console.error("Error in :createProfileReminderNotification ", err);
      throw new Error("Failed to createProfileReminderNotification");
    }
  }
}

// Instantiate query and mutation operations with singleton AppSync client
const queryOps = new QueryOperations(GraphQL.instance);
const mutationsOps = new MutationOperations(GraphQL.instance);

module.exports = {
  queryOps,
  mutationsOps,
};
