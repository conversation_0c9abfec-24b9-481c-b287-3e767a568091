/**
 * The RawQuery class contains GraphQL query and mutation strings for managing employee data,
 * leave requests, work-from-home requests, and notifications.
 *
 * Properties:
 * - listEmployees: A query to retrieve a list of employees with optional filters, limits, and pagination.
 * - createLeave: A mutation to create a leave request with specified details such as type, adjustment type, and count.
 * - createWFHRequest: A mutation to create a work-from-home request with the necessary input data.
 * - approveWFHRequest: A mutation to update the status of a work-from-home request by its ID.
 * - createNotification: A mutation to create a notification with details like message, recipient, type, and time sensitivity.
 */

class RawQuery {
  listEmployees = `
            query MyQuery($filter: ModelEmployeeFilterInput,$limit:Int,$nextToken:String) {
            listEmployees(
                filter:  $filter
                limit: $limit
                nextToken: $nextToken
            ) {
                items {
                active
                account_status
                email
                }
            }
            }
        `;

  createLeave = `
        mutation MyMutation($DoNotProcess: Boolean , $adjustment_type: LEAVE_ADJUSTMENT_TYPE!, $count: Float!, $description: String , $leave_length: LEAVE_LENGTH! , $type: LEAVE_TYPE!, $employeeLeavesId: String) {
        createLeave(
            input: {type: $type, adjustment_type: $adjustment_type, count: $count, leave_length: $leave_length, description: $description, DoNotProcess: $DoNotProcess, employeeLeavesId: $employeeLeavesId}
        ) {
            id
        }
        }
    `;

  createWFHRequest = `
        mutation MyMutation($input: CreateWorkFromHomeRequestInput! ) {
        createWorkFromHomeRequest(input: $input) {
            employeeId
            id
        }
        }
`;

  approveWFHRequest = `
        mutation MyMutation($id: ID!, $status: KUDOS_STATUS) {
        updateWorkFromHomeRequest(input: {id: $id, status: $status}) {
            id
        }
        }
`;

  createNotification = `
        mutation MyMutation($Message: String! , $ToAccount: String, $Type: String, $createdAt: String, $isRead: Boolean, $timeSensitive: Boolean) {
        createNotification(
            input: {Message: $Message, ToAccount: $ToAccount, Type: $Type, timeSensitive: $timeSensitive, isRead: $isRead, createdAt: $createdAt}
        ) {
            id
        }
        }
    `;
}

const gql = new RawQuery();
module.exports = gql;
