const AWS = require("aws-sdk");
const gql = require("graphql-tag");
AWS.config.update({ region: process.env.REGION });
const queries = require("./gql.js");
const { appsyncClient } = require("./gqlClient.js");

// --- Internal GraphQL Execution Utility ---

async function executeGraphQL({ query, variables = {}, type = "query" }) {
  try {
    const [operation, gqlKey] =
      type === "mutation" ? ["mutate", "mutation"] : ["query", "query"];
    const response = await appsyncClient[operation]({
      [gqlKey]: gql(query),
      variables,
      ...(type === "query" && { fetchPolicy: "network-only" }),
    });

    return response;
  } catch (err) {
    console.error(`Error during GraphQL ${type}:`, err);
    throw new Error(
      `Failed to execute GraphQL ${type}. See logs for more details.`
    );
  }
}

// --- Business Logic Functions ---

async function createEmployee(payload) {
  try {
    const response = await executeGraphQL({
      query: queries.createEmployee,
      variables: payload,
      type: "mutation",
    });
    return response;
  } catch (err) {
    console.error("Error in createEmployee", err);
    throw new Error("Failed to create employee");
  }
}

async function isEmployeeFoundInDB(payload) {
  try {
    const res = await executeGraphQL({
      query: queries.isEmployeeFound,
      variables: payload,
      type: "query",
    });
    return !!res?.data?.getEmployee?.email;
  } catch (err) {
    console.error("Error in isEmployeeFound", err);
    throw new Error("Failed to check employee existence");
  }
}

// --- Exported API ---
module.exports = {
  createEmployee,
  isEmployeeFoundInDB,
};
