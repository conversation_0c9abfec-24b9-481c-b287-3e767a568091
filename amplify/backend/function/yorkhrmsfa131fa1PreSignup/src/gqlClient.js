const AWSAppSyncClient = require("aws-appsync").default;
const { AUTH_TYPE } = require("aws-appsync");
const AWS = require("aws-sdk");
AWS.config.update({ region: process.env.REGION });
const appsyncUrl = process.env.API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT;

const appsyncClient = new AWSAppSyncClient({
  url: appsyncUrl,
  region: process.env.REGION,
  auth: {
    type: AUTH_TYPE.AWS_IAM,
    credentials: AWS.config.credentials,
  },
  disableOffline: true,
});

module.exports = {
  appsyncClient,
};
