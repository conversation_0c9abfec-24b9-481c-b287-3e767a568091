{"permissions": {"api": {"yorkhrmsGraphQL": ["Query", "Mutation"]}, "auth": {"yorkhrmsfa131fa1": ["create", "read", "update", "delete"]}, "storage": {"data": ["create", "read", "update", "delete"]}}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "yorkhrmsSentryLayerNode", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "staging"}], "secretNames": ["OPEN_AI_KEY", "JIRA_API_TOKEN"], "environmentVariableList": [{"cloudFormationParameterName": "sentry<PERSON>n", "environmentVariableName": "SENTRY_DSN"}]}