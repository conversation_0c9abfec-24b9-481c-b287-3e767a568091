const {
  getEmployee,
  getCandidate,
  getKudosById,
} = require("../helpers/appQueries.js");
const OpenAIClient = require("../helpers/openAIHelper.js");
const { GetFileFromS3 } = require("../helpers/s3Helper.js");
const { getKudosPrompt } = require("../helpers/prompt.js");
const { captureError } = require("/opt/sentryWrapper");
const { GENERAL_ERROR } = require("../helpers/constants.js");

const generateEmployeeIntro = async (req, res) => {
  try {
    const email = req.query.email?.trim();
    const employeeDetails = await getEmployee(email);
    // Clean any HTML tags from the employee details
    const cleanEmployeeDetails = { ...employeeDetails };
    if (cleanEmployeeDetails.introduction) {
      // Strip HTML tags from introduction field
      cleanEmployeeDetails.introduction =
        cleanEmployeeDetails.introduction.replace(/<\/?[^>]+(>|$)/g, "");
    }

    const prompt = `Summarize this detailed profile into an overview focusing on the individual's core technical skills, areas of expertise, introduction from employee details if any
    and any unique highlights from their career from the details provided below. Ensure the format is consistent and concise,
    with no additional information outside the requested structure. Begin the bio with this format:
    [First Name] – [Years of experience] experience, [Key areas of expertise (e.g., technologies, tools, industries, methodologies)].
    
    
    employee details: ${JSON.stringify(cleanEmployeeDetails)}
    `;

    const client = new OpenAIClient();
    await client.init();

    const response = await client.executePrompt(
      prompt,
      "Act as professional copy writer who can write intro for employees based on their skills, experience and introduction text if present."
    );
    const aireply = response.choices[0].message.content;
    console.log("Parsed Response:", aireply);

    res.json({ data: aireply });
    return;
  } catch (error) {
    captureError(error);
    console.error("Error during generating employee intro", error);
    return res.status(500).json({
      success: false,
      message: GENERAL_ERROR,
      error: error,
    });
  }
};
const generateCandidateIntro = async (req, res) => {
  try {
    const candidateID = req.query.id?.trim();
    const candidateDetails = await getCandidate(candidateID);

    console.log(candidateDetails);
    const ResumeTextFileName = candidateDetails.s3_resume_key.replace(
      /\.[^/.]+$/,
      "_parsed.txt"
    );
    console.log(ResumeTextFileName);
    const data = await GetFileFromS3("public/" + ResumeTextFileName);

    const prompt = `Summarize this detailed profile into an overview using this format:
      [First Name] – [Years of experience] experience, [Key areas of expertise (e.g., technologies, tools, industries, methodologies)].
      The summary should focus on the individual's core technical skills, areas of expertise, 
      and any unique highlights from their career. Ensure the format is consistent and concise, 
      with no additional information outside the requested structure.
      
      ${data}
      `;

    const client = new OpenAIClient();
    await client.init();

    const response = await client.executePrompt(
      prompt,
      "Act as professional copy writer who can write intro for employees based on their skills, experience."
    );
    const aireply = response.choices[0].message.content;
    console.log("Parsed Response:", aireply);

    res.json({ data: aireply });
    return;
  } catch (error) {
    console.error("Error during generating candidate intro", error);
    captureError(error);
    return res.status(500).json({
      success: false,
      message: GENERAL_ERROR,
      error: error,
    });
  }
};
// const generateKudosPost = async (req, res) => {
//   try {
//     const kudoID = req.query.id?.trim();
//     res.json({ kudoID });
//     return;
//   } catch (error) {
//     captureError(error);
//     console.error("Error during generating kudos post", error);
//     return res.status(500).json({
//       success: false,
//       message: GENERAL_ERROR,
//       error: error,
//     });
//   }
// };

const generateKudosSocialContent = async (req, res) => {
  try {
    const { kudosId, userEmail } = req.query;
    if (!kudosId || !userEmail) {
      return res.status(400).json({
        success: false,
        message: "Pass a valid request query data.",
      });
    }

    const kudos = await getKudosById({ id: kudosId, eq: userEmail });
    if (kudos?.recipient?.items?.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Pass a valid User Email.",
      });
    }

    const prompt = getKudosPrompt(kudos);
    const client = new OpenAIClient();
    await client.init();
    const response = await client.executePrompt(
      prompt,
      "You are an expert LinkedIn content creator known for crafting impactful, human-centered posts that celebrate workplace recognition in a way that feels both heartfelt and professional. Your goal is to transform a brief kudos message into a concise, engaging LinkedIn post (under 200 words). No grammatical errors at all."
    );
    const generatedSocialContent = response.choices[0].message.content;
    console.log("Kudos Generated AI Content...", generatedSocialContent);

    res.json({
      success: true,
      message: "Kudos content generated successfully",
      data: {
        generatedSocialContent,
      },
    });
  } catch (error) {
    console.log("Error ", JSON.stringify(error));
    captureError(error);
    res.status(500).json({
      success: false,
      message: "Error generating kudos content",
    });
  }
};

module.exports = {
  generateEmployeeIntro,
  generateCandidateIntro,
  // generateKudosPost,
  generateKudosSocialContent,
};
