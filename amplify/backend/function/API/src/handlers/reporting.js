const {
  CACHED_FILE,
  TREE_LOCATION,
  ORGANIZATION_TREE_META,
  GENERAL_ERROR,
} = require("../helpers/constants.js");
const { GetFileFromS3, getS3FileHeadInfo } = require("../helpers/s3Helper.js");
const fs = require("fs");
const { captureError } = require("/opt/sentryWrapper");

function getHierarchyData(tree, targetEmail, managerChain = []) {
  for (const employee of tree) {
    if (employee.email === targetEmail) {
      return {
        reporting_to: managerChain,
        reportees: employee.reportees ?? [],
      };
    }

    if (Array.isArray(employee.reportees) && employee.reportees.length > 0) {
      const nextChain = [
        { ...employee, reportees: undefined },
        ...managerChain,
      ];
      const result = getHierarchyData(
        employee.reportees,
        targetEmail,
        nextChain
      );
      if (result) return result;
    }
  }

  return null;
}

/**
 * Retrieves the organization tree file, utilizing a cached version if available and up-to-date.
 * Checks the cache for an existing file and its metadata. If the cache is valid, returns the cached file.
 * Otherwise, fetches the file from S3, updates the cache, and returns the data.
 *
 * @returns {Promise<Object>} The organization tree data.
 * @throws Will throw an error if there is an issue accessing the S3 file or reading/writing the cache.
 */
const getOrgTreeFile = async () => {
  try {
    const headInfo = await getS3FileHeadInfo(TREE_LOCATION);
    if (fs.existsSync(CACHED_FILE) && fs.existsSync(ORGANIZATION_TREE_META)) {
      const meta = JSON.parse(fs.readFileSync(ORGANIZATION_TREE_META, "utf-8"));
      if (meta.ETag === headInfo.ETag) {
        return JSON.parse(fs.readFileSync(CACHED_FILE, "utf-8"));
      }
    }

    // Fetch from S3 if cache read fails or file doesn't exist
    console.log("Returning organization tree file from S3.");
    const data = await GetFileFromS3(TREE_LOCATION);
    fs.writeFileSync(CACHED_FILE, JSON.stringify(data));
    fs.writeFileSync(
      ORGANIZATION_TREE_META,
      JSON.stringify({ ETag: headInfo.ETag })
    );
    return data;
  } catch (error) {
    throw error;
  }
};

const getReportingTree = async (req, res) => {
  try {
    const email = req.query.email?.trim();
    if (!email || email === "undefined" || email === "null") {
      return res.status(400).send({
        body: JSON.stringify({ message: "Email is required." }),
      });
    }
    const data = await getOrgTreeFile();
    const result = getHierarchyData(data, email);
    res.json(result);
    return;
  } catch (error) {
    captureError(error);
    return res.status(500).json({
      message: GENERAL_ERROR,
      error: error,
    });
  }
};

module.exports = {
  getReportingTree,
};
