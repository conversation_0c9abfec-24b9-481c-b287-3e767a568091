const fetch = require("node-fetch");
const aws = require("aws-sdk");
const { JIRA_URL, KALRAV_EMAIL } = require("../helpers/constants.js");
const { captureError } = require("/opt/sentryWrapper");

/**
 * Retrieves and decrypts the JIRA API token from AWS SSM Parameter Store.
 *
 * This function fetches the JIRA API token stored in the AWS SSM Parameter Store,
 * decrypts it, and returns it in an object. The token is identified by the
 * environment variable 'JIRA_API_TOKEN'.
 *
 * @returns {Promise<Object>} A promise that resolves to an object containing the
 * JIRA API token with the key 'JIRA_API_TOKEN'.
 */
async function getSecrets() {
  const { Parameters } = await new aws.SSM()
    .getParameters({
      Names: ["JIRA_API_TOKEN"].map((secretName) => process.env[secretName]),
      WithDecryption: true,
    })
    .promise();

  return Parameters?.reduce((acc, { Name, Value }) => {
    if (Name === process.env.JIRA_API_TOKEN) {
      acc.JIRA_API_TOKEN = Value;
    }
    return acc;
  }, {});
}

const getHeader = (secrets) => {
  return {
    Authorization: `Basic ${Buffer.from(
      `${KALRAV_EMAIL}:${secrets.JIRA_API_TOKEN}`
    ).toString("base64")}`,
    Accept: "application/json",
  };
};

/**
 * Fetches Jira projects using the Jira API and sends the result as a JSON response.
 *
 * This function retrieves the necessary secrets for authentication, makes a GET request
 * to the Jira API to fetch all projects, and returns the data in JSON format. If an error
 * occurs during the process, it logs the error and sends a 500 status response with an error message.
 *
 * @param {Object} req - The request object.
 * @param {Object} res - The response object.
 * @returns {Promise<Object>} JSON response containing the success status and project data or an error message.
 */
const getJiraProjects = async (req, res) => {
  try {
    const secrets = await getSecrets();
    const response = await fetch(`${JIRA_URL}/rest/api/3/project`, {
      method: "GET",
      headers: getHeader(secrets),
    });
    const data = await response.text();
    return res.json({
      message: "Projects fetched successfully.",
      success: true,
      data: JSON.parse(data),
    });
  } catch (error) {
    console.error("Error fetching Jira projects:", error);
    captureError(error);
    return res.status(500).json({
      message: "Something went wrong ! Please try again later.",
      success: false,
    });
  }
};

module.exports = {
  getJiraProjects,
};
