const AWS = require("aws-sdk");
const {
  updateEmployeeAccountStatus,
  getEmployee,
} = require("../helpers/appQueries.js");
const cognito = new AWS.CognitoIdentityServiceProvider({ region: process.env.REGION });
const { captureError } = require("/opt/sentryWrapper");
    

/**
 * Validates the strength of a given password based on specific criteria.
 * Checks if the password is at least 8 characters long, contains at least
 * one uppercase letter, one lowercase letter, one number, and one special
 * character. Returns a string listing the unmet criteria if the password
 * is weak, or null if the password meets all requirements.
 *
 * @param {string} password - The password to be validated.
 * @returns {string|null} - A message listing the unmet criteria or null if the password is strong.
 */
function validatePasswordStrength(password) {
  const errors = [];
  if (password.length < 8) {
    errors.push("at least 8 characters long");
  }
  if (!/[A-Z]/.test(password)) {
    errors.push("at least one uppercase letter (A-Z)");
  }
  if (!/[a-z]/.test(password)) {
    errors.push("at least one lowercase letter (a-z)");
  }
  if (!/\d/.test(password)) {
    errors.push("at least one number (0-9)");
  }
  if (!/[@$!%*?&#^+=()_\-~]/.test(password)) {
    errors.push("at least one special character (@$!%*?&#^+=()_-~)");
  }

  if (errors.length > 0) {
    return `Password must be ${errors.join(", ")}.`;
  }

  return null;
}

/**
 * Sets the login password for a user in the Cognito User Pool and updates the employee account status.
 *
 * @async
 * @function setLoginPassword
 * @param {Object} req - The request object containing user details.
 * @param {Object} res - The response object for sending the result.
 * @returns {Object} JSON response indicating success or failure.
 *
 * @throws Will return a 400 status if required fields are missing or if the password is weak.
 * @throws Will return a 500 status if an error occurs during the process.
 *
 * The function validates the password strength, sets the password using AWS Cognito,
 * and updates the employee account status to "ONBOARDING_PENDING".
 */
const setLoginPassword = async (req, res) => {
  const { username, password, email } = req.body;
  try {
    // Validate input
    if (!username || !password || !email) {
      return res.status(400).json({
        success: false,
        message: "Username, Password and Email are required",
      });
    }

    // Validate password strength
    const result = validatePasswordStrength(password);
    if (result !== null) {
      return res.status(400).json({
        success: false,
        message: result,
      });
    }

    // Set user password in Cognito
    const params = {
      Username: username,
      Password: password,
      UserPoolId: process.env.AUTH_YORKHRMSFA131FA1_USERPOOLID,
      Permanent: true,
    };
    await cognito.adminSetUserPassword(params).promise();
    console.log(`Password successfully set for user ${username}`);

    // Update the employee account status to ONBOARDING_PENDING
    const emp = await getEmployee({ email });

    // If the employee is active and this API might be used in the future,
    // restrict updating the status to prevent interfering with the form fill-up flow.
    if (emp?.account_status !== "ACTIVE") {
      await updateEmployeeAccountStatus({
        email,
        account_status: "ONBOARDING_PENDING",
      });
    }
    return res.status(200).json({
      success: true,
      message: "Password set successfully.",
    });
  } catch (error) {
    console.error("Error setting password:", error);
    captureError(error);
    return res.status(500).json({
      success: false,
      message:
        "Something went wrong while setting the password. Please try again later.",
      error: error?.message || error,
    });
  }
};

module.exports = {
  setLoginPassword,
};
