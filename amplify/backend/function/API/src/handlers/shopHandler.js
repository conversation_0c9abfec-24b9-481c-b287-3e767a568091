const {
  getEmployeeRewardPoints,
  getProduct,
  createOrder,
  createCoinTransaction,
  updateEmployee,
} = require("../helpers/appQueries");
const { captureError } = require("/opt/sentryWrapper");

const processOrder = async (req, res) => {
  try {
    console.log("INPUT: ", JSON.stringify(req?.body));
    const { email, productId } = req?.body || {};
    if (!email || !productId) {
      return res.status(400).json({
        message: "Invalid request body.",
      });
    }

    // 🔄 Fetch employee balance & product info in parallel
    const [rewardPoints, productPoints] = await Promise.all([
      getEmployeeRewardPoints(email),
      getProduct(productId),
    ]);

    console.log("Available Balance", rewardPoints);
    console.log("Product Price", productPoints?.price);

    // ❌ Check affordability
    if (rewardPoints < productPoints?.price) {
      return res.status(400).json({
        message: "You do not have enough reward YCs to purchase this product.",
      });
    }
    const remainingBalance = rewardPoints - productPoints?.price;

    const orderInput = {
      orderEmployeeId: email,
      orderProductId: productId,
      status: "CREATED",
    };
    await createOrder(orderInput);

    // 💸 Log coin transaction
    const coinTransactionInput = {
      adjustment_type: "DEBIT",
      coins: productPoints?.price,
      transaction_date: new Date().toISOString(),
      employeeCoins_transactionsId: email,
      coinTransactionApproved_byId: email,
      balance: remainingBalance,
      comment: `Purchased ${productPoints?.title}`,
    };
    await createCoinTransaction(coinTransactionInput);

    // 👤 Update employee balance
    const employeeInput = {
      email,
      reward_points: remainingBalance,
    };
    console.log("After Order Employee Data ", JSON.stringify(employeeInput));
    await updateEmployee(employeeInput);
    return res.json({
      message: "Success!!",
      rewardPoints: remainingBalance,
    });
  } catch (error) {
    console.error("Error in /shop/order:", error);
    captureError(error);
    return res.status(500).json({
      message: "Something went wrong ! Please try again later.",
      error: error?.message || error,
    });
  }
};

module.exports = { processOrder };
