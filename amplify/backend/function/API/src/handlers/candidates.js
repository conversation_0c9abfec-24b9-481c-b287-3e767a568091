const { getCandidateByEmail } = require("../helpers/appQueries");
const { GENERAL_ERROR } = require("../helpers/constants");
const { captureError } = require("/opt/sentryWrapper");

/**
 * Asynchronously retrieves candidate details based on the provided email query parameter.
 * 
 * Validates the email parameter, ensuring it is not empty, "undefined", or "null".
 * If the email is invalid, responds with a 400 status and an error message.
 * 
 * Attempts to fetch candidate details using the `getCandidateByEmail` function.
 * If no candidate is found, responds with a 404 status and an error message.
 * 
 * On successful retrieval, responds with a JSON object containing the candidate details.
 * 
 * In case of any errors during the process, logs the error and responds with a 500 status
 * and an appropriate error message.
 * 
 * @param {Object} req - The request object, containing query parameters.
 * @param {Object} res - The response object, used to send back the desired HTTP response.
 */
const getCandidateDetails = async (req, res) => {
  try {
    const email = req.query?.email?.trim()?.toLowerCase();
    if (!email || email === "undefined" || email === "null") {
      return res.status(400).send({
        success: false,
        message: "Email is required.",
      });
    }
    const candidate = await getCandidateByEmail({ email });
    if (candidate?.length === 0) {
      return res.status(200).send({
        success: true,
        message: "Candidate not found.",
        data:{}
      });
    }
    return res.json({
      success: true,
      message: "Candidate details fetched successfully",
      data: candidate[0],
    });
  } catch (error) {
    console.error("Error fetching candidate details:", error);
    captureError(error);
    res.status(500).json({
      success: false,
      message: GENERAL_ERROR,
      error: error?.message || error,
    });
  }
};

module.exports = {
  getCandidateDetails,
};
