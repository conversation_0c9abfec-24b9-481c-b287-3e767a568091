const {
  getEmployeeProfile,
  listAllMBOGoalsV2s,
} = require("../helpers/appQueries");
const OpenAIClient = require("../helpers/openAIHelper.js");

const systemPrompt = `
You are an expert HR professional specializing in Management by Objectives (MBO) goal setting.
Your task is to analyze an employee's profile and select the most relevant MBO goals for the upcoming quarter from the provided list of available goals.

CRITICAL REQUIREMENTS:
1. Select 5-10 goals by their ID from the provided list.
2. Total weightage of selected goals MUST equal EXACTLY 120 points - no more, no less.
3. 75% of total weightage (90 points) should be based on their SME (Subject Matter Expertise) and skills.
4. 25% of total weightage (30 points) should be from other categories: process improvement, people management, communication, training, leadership, etc.
5. Must include at least one goal from each category (SME/Skills AND Other categories).
6. Each goal should have a weightage between 10-25 points.
7. Goals should be specific, measurable, achievable, relevant, and time-bound (SMART).
8. The sum of all weightages in your response MUST equal exactly 120.

VALIDATION: Before responding, verify that the sum of all weightages equals exactly 120 points.
`;

const jsonSchema = {
  type: "object",
  properties: {
    selected_goal_ids: {
      type: "array",
      description: "An array of selected MBOGoalV2 IDs",
      items: {
        type: "string",
        description: "ID of the selected goal",
      },
      minItems: 5,
      maxItems: 7,
    },
    weightage: {
      type: "object",
      description: "Weightage for each selected goal ID",
      additionalProperties: {
        type: "integer",
        minimum: 10,
        maximum: 30,
      },
    },
    success_criteria: {
      type: "object",
      description: "Success criteria for each selected goal ID",
      additionalProperties: {
        type: "string",
        description: "Success criteria for each selected goal ID",
      },
    },
  },
  required: ["selected_goal_ids", "weightage", "success_criteria"],
  additionalProperties: false,
};

async function generateMBOGoals(req, res) {
  const email = req.body.email?.trim();
  const managerAsk = req.body.managerAsk?.trim();
  try {
    console.log(`\n=== Generating MBO Goals for ${email} ===\n`);

    // Get employee profile
    const employee = await getEmployeeProfile(email);
    console.log("Employee Profile:");
    console.log("Email:", employee.email);
    console.log("Introduction:", employee.introduction);
    console.log(
      "Skills:",
      employee.skills.items.map((item) => item.skill.name)
    );
    console.log(
      "SME:",
      employee.SME.items.map((item) => item.sME.name)
    );
    console.log("Career Start Date:", employee.career_start_date);

    // Get available MBO goals for reference
    const availableGoals = await listAllMBOGoalsV2s();
    console.log(`\nAvailable MBO Goal Templates: ${availableGoals.length}`);

    // Prepare employee profile for AI
    const employeeProfile = {
      email: employee.email,
      introduction: employee.introduction,
      skills: employee.skills.items.map((item) => item.skill.name),
      sme: employee.SME.items.map((item) => item.sME.name),
      career_start_date: employee.career_start_date,
      experience_years: employee.career_start_date
        ? Math.floor(
            (new Date() - new Date(employee.career_start_date)) /
              (1000 * 60 * 60 * 24 * 365)
          )
        : 0,
    };

    const client = new OpenAIClient();
    await client.init();

    let selectedGoalIds = [];
    let goalWeightages = {};
    let successCriteria = {};
    let totalWeightage = 0;
    let maxAttempts = 2; // Maximum 2 attempts: first default, second if needed
    let attempt = 0;

    while (totalWeightage < 120 && attempt < maxAttempts) {
      attempt++;
      console.log(`\n=== Attempt ${attempt}: Current weightage = ${totalWeightage} ===`);

      // Calculate remaining weightage needed
      const remainingWeightage = 120 - totalWeightage;
      
      // Create dynamic prompt based on current state
      const dynamicPrompt = `${systemPrompt}

CURRENT STATE:
- Already selected goals: ${selectedGoalIds.length} goals
- Current total weightage: ${totalWeightage}
- Remaining weightage needed: ${remainingWeightage}
- Already selected goal IDs: ${JSON.stringify(selectedGoalIds)}

INSTRUCTIONS:
- Select additional goals to reach exactly ${remainingWeightage} more weightage
- Do NOT select any goals that are already in the list: ${JSON.stringify(selectedGoalIds)}
- Each goal should have weightage between 10-30 points
- The sum of new goal weightages must equal exactly ${remainingWeightage}`;

      const response = await client.executePrompt(dynamicPrompt, "", {
        model: "gpt-4.1-nano",
        messages: [
          { role: "system", content: dynamicPrompt },
          {
            role: "user",
            content: `Employee profile: ${JSON.stringify(
              employeeProfile,
              null,
              2
            )}`,
          },
          {
            role: "user",
            content: `Available goal templates: ${JSON.stringify(
              availableGoals.slice(0, 20),
              null,
              2
            )}`,
          },
          { role: "user", content: `Manager ask: ${managerAsk}` },
        ],
        response_format: {
          type: "json_schema",
          json_schema: {
            name: "selected_goal_ids_schema",
            schema: jsonSchema,
          },
        },
      });

      console.log(`AI Response (Attempt ${attempt}):`, response);

      const content = response.choices[0].message.content;
      console.log(`Parsed Content (Attempt ${attempt}):`, content);
      const { selected_goal_ids: newGoalIds, weightage: newWeightages, success_criteria: newSuccessCriteria } = JSON.parse(content);
      
      // Check for duplicates
      const duplicateIds = newGoalIds.filter(id => selectedGoalIds.includes(id));
      if (duplicateIds.length > 0) {
        console.log(`Warning: Duplicate goal IDs found: ${duplicateIds}. Skipping this attempt.`);
        continue;
      }

      // Add new goals, weightages, and success criteria
      selectedGoalIds = [...selectedGoalIds, ...newGoalIds];
      goalWeightages = { ...goalWeightages, ...newWeightages };
      successCriteria = { ...successCriteria, ...newSuccessCriteria };
      
      // Recalculate total weightage
      totalWeightage = Object.values(goalWeightages).reduce((sum, w) => sum + w, 0);
      console.log(`Updated total weightage: ${totalWeightage}`);
      
      // If total exceeds 120 after first attempt, reduce weightage proportionally from each goal
      if (totalWeightage > 120 && attempt >= 1) {
        console.log(`\n=== Adjusting weightage: Total ${totalWeightage} > 120 ===`);
        const excess = totalWeightage - 120;
        const goalsToReduce = Object.keys(goalWeightages).length;
        const reductionPerGoal = Math.ceil(excess / goalsToReduce);
        
        console.log(`Excess: ${excess}, Goals: ${goalsToReduce}, Reduction per goal: ${reductionPerGoal}`);
        
        // Reduce weightage by reductionPerGoal from each goal, but not below 10
        Object.keys(goalWeightages).forEach(goalId => {
          const currentWeightage = goalWeightages[goalId];
          const newWeightage = Math.max(10, currentWeightage - reductionPerGoal);
          goalWeightages[goalId] = newWeightage;
          console.log(`Goal ${goalId}: ${currentWeightage} -> ${newWeightage}`);
        });
        
        // Recalculate final total
        totalWeightage = Object.values(goalWeightages).reduce((sum, w) => sum + w, 0);
        console.log(`Final adjusted total weightage: ${totalWeightage}`);
        
        // Break the loop after adjustment since we've reached close to 120
        break;
      }
    }

    if (totalWeightage !== 120) {
      console.log(`Warning: Could not reach exactly 120 weightage. Final total: ${totalWeightage}`);
    }

    // Map IDs to full goal objects
    const selectedGoals = availableGoals.filter((goal) =>
      selectedGoalIds.includes(goal.id)
    );

    console.log("\n=== Selected MBO Goals ===");
    let goals = selectedGoals.map((goal) => {
      const customWeightage = goalWeightages[goal.id] || goal.experienceRange;
      // Get customSuccessCriteria from AI response
      const customSuccessCriteria = successCriteria[goal.id] || "Not specified";

      console.log(
        `ID: ${goal.id}\nTitle: ${goal.goal}\nCategory: ${goal.category}\nWeightage: ${customWeightage}\nPurpose: ${goal.purpose}\nNote: ${goal.note}\nSuccess Criteria: ${customSuccessCriteria}\n`
      );
      return {
        category: goal.category,
        goal: goal.goal,
        weightage: customWeightage,
        customSuccessCriteria,
        purpose: goal.purpose,
        updatedAt: new Date().toISOString(),
      };
    });
    console.log(goals);
    res.json({ goals });
  } catch (error) {
    console.error("Error generating MBO goals:", error);
    throw error;
  }
}

// Export function and run if called directly
module.exports = { generateMBOGoals };