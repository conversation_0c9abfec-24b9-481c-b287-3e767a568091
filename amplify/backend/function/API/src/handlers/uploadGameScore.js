const { GAME_SCORE } = require("../helpers/constants");
const { PutFileToS3, GetFileFromS3 } = require("../helpers/s3Helper.js");
const { getEmployee } = require("../helpers/appQueries");
const { captureError } = require("/opt/sentryWrapper");

/**
 * Updates or creates the game score record for an employee.
 *
 * @param {Object} employee - The employee details including first name, last name, and email.
 * @param {Object|null} employeeScores - The current scores data for employees or null if no data exists.
 * @param {Object} input - The new score data including score, start time, and end time.
 * @returns {Object} The updated or newly created scores data with timestamps.
 */
const modifyEmployeeScore = (employee, employeeScores, input) => {
  const { first_name: firstName, last_name: lastName, email } = employee;
  const { score: newScore, startTime, endTime } = input;

  if (employeeScores === null) {
    // Create File
    return {
      createdAt: new Date(),
      updatedAt: new Date(),
      data: {
        [email]: {
          firstName,
          lastName,
          maxScore: +newScore,
          gameStartTime: startTime,
          gameEndTime: endTime,
        },
      },
    };
  }
  const currentScore = employeeScores.data[email]?.maxScore ?? -Infinity;

  // Only update if newScore is higher or employee not present yet
  if (newScore > currentScore) {
    return {
      ...employeeScores,
      updatedAt: new Date(),
      data: {
        ...employeeScores.data,
        [email]: {
          firstName,
          lastName,
          maxScore: +newScore,
          gameStartTime: startTime,
          gameEndTime: endTime,
        },
      },
    };
  }
  return employeeScores;
};

/**
 * Handles the upload of a game score by validating the request body,
 * retrieving the employee data, updating the game scores, and storing
 * the updated scores in S3. Responds with appropriate HTTP status codes
 * and messages based on the success or failure of the operation.
 *
 * @param {Object} req - The request object containing the game score data.
 * @param {Object} res - The response object used to send back the HTTP response.
 */
const uploadGameScore = async (req, res) => {
  try {
    const {
      score,
      email,
      startTime, // UTC Time
      endTime, // UTC Time
    } = req.body;
    const isInvalidScore = typeof score !== "number" || score < 0;
    const isMissingFields = !email || !startTime || !endTime;

    if (isInvalidScore || isMissingFields) {
      return res.status(400).json({
        message: "Pass a valid request body.",
        success: false,
      });
    }
    const employee = await getEmployee(email);
    if (!employee) {
      return res.status(404).json({
        message: "Employee not found.",
        success: false,
      });
    }
    let empScores = await GetFileFromS3(GAME_SCORE);
    const updatedGameScores = modifyEmployeeScore(
      employee,
      empScores,
      req.body
    );
    await PutFileToS3(GAME_SCORE, updatedGameScores);
    return res.status(200).json({
      message: "Game score uploaded successfully.",
      success: true,
    });
  } catch (error) {
    console.error("Error uploading game score:", error);
    captureError(error);
    return res.status(500).json({
      message:
        "Something went wrong while uploading the game score. Please try again later.",
      success: false,
      error: error?.message || error,
    });
  }
};

module.exports = { uploadGameScore };
