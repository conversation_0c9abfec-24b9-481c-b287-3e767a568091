const { PutFileToS3, GetFileFromS3 } = require("../helpers/s3Helper.js");
const queries = require("../helpers/appQueries.js");

const ANALYTICS_FILE_LOCATION = "public/reports/kudos/metrics/latest.json";
const { captureError } = require("/opt/sentryWrapper");

// Function to get approved kudos using GraphQL
async function getApprovedKudos() {
  try {
    const variables = {
      status: "APPROVED",
    };

    const allItems = await queries.getKudosByStatus(variables);

    return allItems;
  } catch (error) {
    console.error("Error fetching approved kudos:", error);
    throw new Error("Failed to fetch approved kudos");
  }
}

// Function to calculate total kudos given
function calculateTotalKudos(kudos) {
  return kudos.length;
}

// Function to calculate top kudos givers
function calculateTopGivers(kudos) {
  const givers = {};

  kudos.forEach((kudo) => {
    const email = kudo.senderId !== "" ? kudo.senderId : "Well Wisher";
    const first_name = kudo.senderDetails?.first_name ?? "";
    const last_name = kudo.senderDetails?.last_name ?? "";

    if (!givers[email]) {
      givers[email] = { email, first_name, last_name, count: 1 };
    } else {
      givers[email].count++;
    }
  });

  // Convert to array and sort
  const sortedGivers = Object.values(givers).sort((a, b) => b.count - a.count);

  // Return top 5 givers
  return sortedGivers.slice(0, 5);
}

// Function to calculate top kudos receivers
function calculateTopReceivers(kudos) {
  // First, extract all recipient entries
  const recipientEntries = [];
  kudos.forEach((kudo) => {
    if (kudo.recipient && kudo.recipient.items) {
      kudo.recipient.items.forEach((item) => {
        recipientEntries.push({
          email: item.employeeID,
          first_name: item.employee.first_name,
          last_name: item.employee.last_name,
        });
      });
    }
  });

  // Count occurrences of each recipient
  const receivers = {};
  recipientEntries.forEach(({ email, first_name, last_name }) => {
    if (!receivers[email]) {
      receivers[email] = { email, first_name, last_name, count: 1 };
    } else {
      receivers[email].count++;
    }
  });

  // Convert to array and sort
  const sortedReceivers = Object.values(receivers).sort(
    (a, b) => b.count - a.count
  );

  // Return top 5 receivers
  return sortedReceivers.slice(0, 5);
}

// Function to calculate kudos distribution by category
function calculateCategoryDistribution(kudos) {
  const categories = {};

  kudos.forEach((kudo) => {
    const category = kudo.category.toLowerCase();
    if (!categories[category]) {
      categories[category] = 1;
    } else {
      categories[category]++;
    }
  });

  // Convert to array format
  const sortedCategories = Object.entries(categories)
    .map(([category, count]) => ({
      category,
      count,
    }))
    .sort((a, b) => b.count - a.count);

  // Return top 5 categories
  return sortedCategories.slice(0, 5);
}

// Function to calculate time-based trends
function calculateTimeTrends(kudos) {
  const monthlyTrends = {};

  kudos.forEach((kudo) => {
    // Extract date parts
    const updatedDate = new Date(kudo.updatedAt);
    const month = updatedDate.toISOString().slice(0, 7); // YYYY-MM

    // Update monthly trends
    if (!monthlyTrends[month]) {
      monthlyTrends[month] = 1;
    } else {
      monthlyTrends[month]++;
    }
  });

  const monthlyArray = Object.entries(monthlyTrends)
    .map(([month, count]) => ({ month, count }))
    .sort((a, b) => a.month.localeCompare(b.month));

  return {
    monthly: monthlyArray,
  };
}

// Main handler function for updating kudos analytics
const updateKudosAnalytics = async (req, res) => {
  try {
    // Fetch approved kudos using GraphQL
    console.log("Fetching approved kudos...");
    const approvedKudos = await getApprovedKudos();
    console.log(`Retrieved ${approvedKudos.length} approved kudos`);

    // Calculate analytics
    console.log("Calculating analytics...");
    const totalKudos = calculateTotalKudos(approvedKudos);
    const topGivers = calculateTopGivers(approvedKudos);
    const topReceivers = calculateTopReceivers(approvedKudos);
    const categoryDistribution = calculateCategoryDistribution(approvedKudos);
    const timeTrends = calculateTimeTrends(approvedKudos);

    // Create analytics object
    const analytics = {
      lastUpdated: new Date().toISOString(),
      totalKudos,
      topGivers,
      topReceivers,
      categoryDistribution,
      timeTrends,
    };

    // Try to get existing analytics file
    let existingAnalytics = null;
    try {
      console.log("Checking for existing analytics file...");
      existingAnalytics = await GetFileFromS3(ANALYTICS_FILE_LOCATION);
      console.log("Existing analytics file found");
    } catch (error) {
      console.log("No existing analytics file found, creating new one");
    }

    // Determine if we need to update
    let shouldUpdate = true;
    if (existingAnalytics) {
      // If the only difference is the lastUpdated timestamp, don't update
      const existingCopy = {
        ...existingAnalytics,
        lastUpdated: analytics.lastUpdated,
      };
      shouldUpdate = JSON.stringify(existingCopy) !== JSON.stringify(analytics);
    }

    if (shouldUpdate) {
      // Save analytics to S3
      console.log("Saving analytics to S3...");
      await PutFileToS3(ANALYTICS_FILE_LOCATION, analytics);

      const dayTimeStamp = Math.floor(new Date().setHours(0, 0, 0, 0) / 1000);
      await PutFileToS3(
        `public/reports/kudos/metrics/${dayTimeStamp}.json`,
        analytics
      );
      console.log("Analytics saved to S3 successfully");

      res.json({
        success: true,
        message: "Kudos analytics updated successfully",
        totalKudos,
        timestamp: analytics.lastUpdated,
      });
    } else {
      console.log("No changes detected, analytics not updated");
      res.json({
        success: true,
        message: "No changes detected, analytics not updated",
        totalKudos,
        timestamp: existingAnalytics.lastUpdated,
      });
    }
  } catch (error) {
    console.error("Error updating kudos analytics:", error);
    captureError(error);
    res.status(500).json({
      success: false,
      message: "Error updating kudos analytics",
      error: error.message,
    });
  }
};

module.exports = {
  updateKudosAnalytics,
};
