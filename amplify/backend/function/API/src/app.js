/*
Use the following code to retrieve configured secrets from SSM:

const aws = require('aws-sdk');

const { Parameters } = await (new aws.SSM())
  .getParameters({
    Names: ["OPEN_AI_KEY","JIRA_API_TOKEN"].map(secretName => process.env[secretName]),
    WithDecryption: true,
  })
  .promise();

Parameters will be of the form { Name: 'secretName', Value: 'secretValue', ... }[]
*/
/*
Copyright 2017 - 2017 Amazon.com, Inc. or its affiliates. All Rights Reserved.
Licensed under the Apache License, Version 2.0 (the "License"). You may not use this file except in compliance with the License. A copy of the License is located at
    http://aws.amazon.com/apache2.0/
or in the "license" file accompanying this file. This file is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and limitations under the License.
*/

/* Amplify Params - DO NOT EDIT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIIDOUTPUT
	AUTH_YORKHRMSFA131FA1_USERPOOLID
	ENV
	REGION
	STORAGE_DATA_BUCKETNAME
Amplify Params - DO NOT EDIT */

const express = require("express");
const bodyParser = require("body-parser");
const awsServerlessExpressMiddleware = require("aws-serverless-express/middleware");
const { getReportingTree } = require("./handlers/reporting.js");
const { updateKudosAnalytics } = require("./handlers/kudosAnalytics.js");
const {
  generateEmployeeIntro,
  generateCandidateIntro,
  // generateKudosPost,
  generateKudosSocialContent,
} = require("./handlers/aiApis.js");
const { getCandidateDetails } = require("./handlers/candidates.js");
const { setLoginPassword } = require("./handlers/setLoginPassword.js");
const { getJiraProjects } = require("./handlers/jiraApis.js");
const { uploadGameScore } = require("./handlers/uploadGameScore.js");
const { processOrder } = require("./handlers/shopHandler.js");
const { generateMBOGoals } = require("./handlers/generateMBOGoals.js");

// declare a new express app
const app = express();
app.use(bodyParser.json());
app.use(awsServerlessExpressMiddleware.eventContext());

// Enable CORS for all methods
app.use(function (req, res, next) {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Headers", "*");
  next();
});

app.get("/api/reporting", getReportingTree);
app.post("/api/analytics/kudos", updateKudosAnalytics);
app.get("/api/generate-employee-intro", generateEmployeeIntro);
app.get("/api/generate-candidate-intro", generateCandidateIntro);
// app.get("/api/generate-kudos-post", generateKudosPost);
app.get("/api/generate-kudo-content", generateKudosSocialContent);
app.get("/api/candidate-details", getCandidateDetails);
app.put("/api/set-password", setLoginPassword);
app.get("/api/get-jira-projects", getJiraProjects);
app.post("/api/upload-game-score", uploadGameScore);
app.post("/api/shop-order", processOrder);

app.post("/api/generate-mbo-goals", generateMBOGoals);
app.listen(3000, function () {
  console.log("App started");
});

// Export the app object. When executing the application local this does nothing. However,
// to port it to AWS Lambda we will create a wrapper around that will load the app from
// this file
module.exports = app;
