const getKudosPrompt = (kudo) => {
  const userData = kudo?.recipient?.items[0]?.employee;
  const employeeName = `${userData?.first_name} ${userData?.last_name}`;
  const kudosGiver =
    `${kudo?.senderDetails?.first_name} ${kudo?.senderDetails?.last_name}` ||
    "Anonymous";
  const kudosMessage = kudo?.message || "No message provided";
  const kudosCategory = kudo?.category || "General Appreciation";
  const prompt = `
    Generate a professional and engaging LinkedIn post under 200 words. The post should:
  
    1. Express genuine gratitude for receiving kudos from a teammate.
    2. Highlight the employee’s contribution based on the kudo message.
    3. Clearly conveys the positive impact of their work 
    4. Subtly appreciate York IE for fostering a recognition-rich culture (do not mention any internal systems).
    5. Feels personal, warm, and worthy of real engagement from peers and colleagues.
  
    Input:
    - Employee Name: ${employeeName}
    - Kudos Given By: ${kudosGiver}
    - Kudos Message: "${kudosMessage}"
    - Kudos Category: ${kudosCategory}
  `;
  return prompt;
};

module.exports = {
  getKudosPrompt,
};
