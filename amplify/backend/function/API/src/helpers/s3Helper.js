const AWS = require("aws-sdk");
const { S3_BUCKET_NAME } = require("./constants");
const s3 = new AWS.S3();

// Save the overview data to S3
async function GetFileFromS3(Key) {
  try {
    const params = {
      Bucket: process.env.STORAGE_DATA_BUCKETNAME,
      Key,
    };

    const data = await s3.getObject(params).promise();
    const result = data.Body.toString("utf-8");
    return JSON.parse(result);
  } catch (error) {
    // If file not found
    if (error.code === "NoSuchKey") {
      return null;
    }
    console.log("Error retrieving file from S3:", error);
    throw new Error(error);
  }
}

// Upload data to S3
async function PutFileToS3(Key, data) {
  // Convert data to string if it's an object
  let Body;

  if (typeof data === "object") {
    try {
      // Stringify the object with pretty formatting for readability
      Body = JSON.stringify(data, null, 2);
    } catch (error) {
      console.log("Error stringifying object:", error);
      throw error;
    }
  } else {
    // If data is already a string, use it directly
    Body = data;
  }

  // Set up the parameters for the S3 upload
  const params = {
    Bucket: process.env.STORAGE_DATA_BUCKETNAME,
    Key,
    Body,
    ContentType: "application/json", // Assuming we're primarily storing JSON
  };

  // Perform the upload operation
  try {
    await s3.putObject(params).promise();
    return true; // Return success
  } catch (error) {
    console.log("Error uploading to S3:", error);
    throw error; // Propagate the error to the caller
  }
}

const getS3FileHeadInfo = async (Key) => {
  try {
    return await s3
      .headObject({
        Bucket: S3_BUCKET_NAME,
        Key,
      })
      .promise();
  } catch (error) {
    console.log("Error getting file head info:", error);
    throw error;
  }
};
module.exports = {
  GetFileFromS3,
  PutFileToS3,
  getS3FileHeadInfo,
};
