const getKudosByStatus = `
    query GetKudosByStatus($status: KUDOS_STATUS!, $limit: Int, $nextToken: String) {
      getKudosByStatus(status: $status, limit: $limit, nextToken: $nextToken) {
        items {
          id
          senderId
          message
          category
          updatedAt
          createdAt
          senderDetails {
            first_name
            last_name
          }
          recipient {
            items {
              employeeID
              employee {
                first_name
                last_name
              }
            }
          }
          status
        }
        nextToken
      }
    }
`;

const getEmployee = /* GraphQL */ `
  query GetEmployee($email: String!) {
    getEmployee(email: $email) {
      email
      employee_id
      first_name
      last_name
      introduction
      title {
        id
        name
      }
      skills {
        items {
          id
          employeeID
          skillID
          createdAt
          updatedAt
          email
          __typename
        }
        nextToken
        __typename
      }
      career_start_date
      york_start_date
      gender
      __typename
    }
  }
`;
const getCandidate = /* GraphQL */ `
  query GetCandidate($id: ID!) {
    getCandidate(id: $id) {
      s3_resume_key
      first_name
      last_name
      email
    }
  }
`;

const getKudosById = `
query MyQuery($id: ID!, $eq: String) {
  getKudos(id: $id) {
    category
    message
    status
    recipient(filter: {employeeID: {eq: $eq}}) {
      items {
        employee {
          email
          first_name
          last_name
        }
      }
    }
    senderId
    senderDetails {
      first_name
      last_name
    }
  }
}
`;

const getCandidateByEmail = `
query MyQuery($email: String = "<EMAIL>", $limit: Int = 1) {
  getcandidateByEmail(email: $email, limit: $limit) {
    items {
      id
      email
      first_name
      last_name
      mobile
      skills {
        items {
          id
          skillID
        }
      }
      reffered_by {
        email
        first_name
        last_name
        active
      }
      status
      location
      external_referral_detailed {
        email
      }
      externalReferralsReferralsId
    }
  }
}
`;

const updateEmployeeAccountStatus = `
mutation MyMutation($account_status: ACCOUNT_STATUS, $email: String!) {
  updateEmployee(input: {account_status: $account_status, email: $email}) {
    account_status
    email
  }
}
`;

const getEmployeeRewardsPoint = /* GraphQL */ `
  query getEmployee($email: String!) {
    getEmployee(email: $email) {
      reward_points
    }
  }
`;

const getProduct = /* GraphQL */ `
  query GetProduct($id: ID!) {
    getProduct(id: $id) {
      id
      title
      price
      assets
      createdAt
      updatedAt
      __typename
    }
  }
`;

const updateEmployee = /* GraphQL */ `
  mutation UpdateEmployee(
    $input: UpdateEmployeeInput!
    $condition: ModelEmployeeConditionInput
  ) {
    updateEmployee(input: $input, condition: $condition) {
      email
      employee_id
      first_name
      last_name
      reward_points
      createdAt
      updatedAt
      __typename
    }
  }
`;

const createOrder = /* GraphQL */ `
  mutation CreateOrder(
    $input: CreateOrderInput!
    $condition: ModelOrderConditionInput
  ) {
    createOrder(input: $input, condition: $condition) {
      id
      status
      createdAt
      updatedAt
      orderProductId
      orderEmployeeId
      __typename
    }
  }
`;

const createCoinTransactionForShop = /* GraphQL */ `
  mutation CreateCoinTransaction(
    $input: CreateCoinTransactionInput!
    $condition: ModelCoinTransactionConditionInput
  ) {
    createCoinTransaction(input: $input, condition: $condition) {
      id
      coins
      adjustment_type
      employee {
        email
        first_name
        last_name
        reward_points
      }
      transaction_date
      balance
      createdAt
      updatedAt
      employeeCoins_transactionsId
      coinTransactionApproved_byId
      __typename
    }
  }
`;

// GraphQL queries
const getEmployeeByEmail = /* GraphQL */ `
  query GetEmployee($email: String!) {
    getEmployee(email: $email) {
      email
      introduction
      skills {
        items {
          skill {
            name
          }
        }
      }
      SME {
        items {
          sME {
            name
          }
        }
      }
      career_start_date
    }
  }
`;

const listMBOGoalsV2s = /* GraphQL */ `
  query ListMBOGoalsV2s(
    $filter: ModelMBOGoalsV2FilterInput
    $limit: Int
    $nextToken: String
  ) {
    listMBOGoalsV2s(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        role
        experienceRange
        category
        goal
        purpose
        note
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

module.exports = {
  getKudosByStatus,
  getEmployee,
  getCandidate,
  getKudosById,
  getCandidateByEmail,
  updateEmployeeAccountStatus,
  getEmployeeRewardsPoint,
  getProduct,
  updateEmployee,
  createOrder,
  createCoinTransactionForShop,
  getEmployeeByEmail,
  listMBOGoalsV2s,
};
