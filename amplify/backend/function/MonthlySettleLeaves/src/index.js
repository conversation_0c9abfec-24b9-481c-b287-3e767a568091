/* Amplify Params - DO NOT EDIT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIENDPOINTOUTPUT
	API_YORKHRMSGRAPHQL_GRAPHQLAPIIDOUTPUT
	AUTH_YORKHRMSFA131FA1_USERPOOLID
	ENV
	REGION
	STORAGE_DATA_BUCKETNAME
  SENTRY_DSN
Amplify Params - DO NOT EDIT */

const { queryOps, mutationsOps } = require("./services/graphQLOperations");
const XLSX = require("node-xlsx");
const {
  FILE_HEADER,
  UNIT_TEAM_LEADER_GRP_NAME,
  EXECUTIVE_GRP_NAME,
  HR_GRP_NAME,
} = require("./utils/constants");
const cognitoClient = require("./services/cognitoOperations");
const { S3 } = require("./services/awsServiceClient");
const emailClient = require("./services/emailOperations");
const { wrapHandler, captureError } = require("/opt/sentryWrapper");

const getMetaData = () => {
  const date = new Date();
  const year = date.getFullYear();

  // Get the month (0-11), add 1 to make it 1-12, and pad with a leading '0' if needed
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const folderName = `leave-reports/${year}_${month}`;
  const fileName = `negative_leave_rpt_${year}_${month}.xlsx`;
  const s3Key = `${folderName}/${fileName}`;
  return { folderName, fileName, s3Key };
};

// Upload data to S3
async function putFileToS3(Key, data) {
  try {
    // Set up the parameters for the S3 upload
    const params = {
      Bucket: process.env.STORAGE_DATA_BUCKETNAME,
      Key,
      Body: data,
      ContentType:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    };

    await S3.instance.putObject(params).promise();
    console.log(`✅ File uploaded to S3 at ${Key}`);
    return true; // Return success
  } catch (error) {
    console.log("Error uploading csv file to S3:", error);
    throw error;
  }
}

// 📄 Generates XLSX buffer from employee list
const generateCsvFile = async (employees) => {
  const rows = [FILE_HEADER];

  employees?.forEach((emp) => {
    const {
      email,
      employee_id,
      privilege_leave_balance,
      employeeSquadId,
      squad: { squadSquad_managerId },
    } = emp;
    rows.push([
      employee_id,
      email,
      Math.abs(privilege_leave_balance),
      employeeSquadId,
      squadSquad_managerId,
    ]);
  });

  const { fileName } = getMetaData();
  return XLSX.build([{ name: fileName, data: rows }]);
};

// 📦 Handles file creation + S3 upload
const handleFileCreationUploadOperation = async (employees) => {
  const fileBuffer = await generateCsvFile(employees);
  const { s3Key } = getMetaData();
  await putFileToS3(s3Key, fileBuffer);
  return fileBuffer;
};

const NotifyUser = async (fileBuffer = null, message) => {
  try {
    const grpNames = [
      HR_GRP_NAME,
      UNIT_TEAM_LEADER_GRP_NAME,
      EXECUTIVE_GRP_NAME,
    ];
    const emails = (
      await Promise.all(
        grpNames.map((grp) => cognitoClient.listGroupEmail(grp))
      )
    ).flat();

    if (!emails || emails.length === 0) {
      console.warn("⚠️ No recipient emails found.");
      return;
    }
    const { fileName } = getMetaData();
    await emailClient.sendEmailToUsers(emails, fileBuffer, fileName);
    await Promise.all(
      emails.map((email) => {
        // Create the Notification
        try {
          mutationsOps.createNotification({
            input: {
              ToAccount: email,
              Type: "Leave Settlement",
              Message: message,
              createdAt: new Date().toISOString(),
            },
          });
        } catch (err) {
          console.error(`❌ Failed to create notification for ${email}:`, err);
        }
      })
    );
    console.log(
      `✅ Notification created for user Hr's, Executive's and unit team leaders.`
    );
  } catch (error) {
    console.error("Error during notifying the user.", error);
    captureError(error);
  }
};

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = wrapHandler(async (event) => {
  try {
    // Query all employees with negative privilege leave balance
    const employees = await queryOps.listEmployeesWithNegativePl();

    // Update Higher Management for not having employees with negative PL Balance
    if (!employees || employees?.length === 0) {
      console.info(
        "ℹ️ No employees found with negative privilege leave balance."
      );
      await NotifyUser(
        null,
        "No Employees found with the negative privilege leave balance."
      );
      return {
        statusCode: 200,
        body: JSON.stringify({ message: "Processing completed successfully" }),
      };
    }

    console.info(
      `🔍 Found ${employees.length} employee(s) with negative PL balance.`
    );
    // Create leave entries for employees with negative balances
    await Promise.all(
      employees?.map(async (emp) => {
        const { email, privilege_leave_balance } = emp;
        await mutationsOps.createLeave({
          adjustment_type: "CREDIT",
          type: "PRIVILEGE",
          description: "Settlement of Paid Leave",
          count: Math.abs(privilege_leave_balance),
          leave_length: "FULL_DAY",
          employeeLeavesId: email,
        });
      })
    );
    console.log(
      `✅ Leave entry created for employees whose balance was negative.`
    );
    // Generate csv file and upload it to s3 bucket
    const fileBuffer = await handleFileCreationUploadOperation(employees);
    await NotifyUser(
      fileBuffer,
      "The privilege leave settlement report has been generated and is now available."
    );
    console.log("🎉 Leave settlement processing completed successfully.");

    return {
      statusCode: 200,
      body: JSON.stringify({ message: "Processing completed successfully" }),
    };
  } catch (error) {
    console.error("❌ Error during settling the leave...", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Something went wrong! Please try again later",
        error: error?.message || error,
      }),
    };
  }
});
