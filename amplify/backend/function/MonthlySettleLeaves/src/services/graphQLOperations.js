const { GraphQL } = require("./awsServiceClient");
const queries = require("./gql");
const { default: gql } = require("graphql-tag");

class GraphQLOperations {
  constructor(appSyncClient) {
    this.appSyncClient = appSyncClient;
  }

  /**
   * Fetches all paginated data for a given query and variables.
   * Automatically handles AppSync-style `nextToken` pagination.
   */
  async fetchAllData(query, variables) {
    let items = [];
    let nextToken = null;

    try {
      do {
        const response = await this.executeQuery(query, {
          ...variables,
          limit: 999,
          nextToken,
        });
        if (response.errors) {
          console.error("GraphQL errors: ", response.errors);
          throw new Error(
            "Error occurred while fetching data from GraphQL API."
          );
        }
        const data = response?.data;
        const queryKey = Object.keys(data).find((key) => data[key]?.items);

        if (queryKey) {
          items = items.concat(data[queryKey]?.items || []);
          nextToken = data[queryKey]?.nextToken || null;
        } else {
          nextToken = null;
        }
      } while (nextToken);

      return items;
    } catch (err) {
      console.error("Error in fetchAllData: ", err);
      throw new Error(
        "Failed to fetch all data. Please check the logs for more details."
      );
    }
  }

  async executeQuery(query, variables) {
    try {
      const response = await this.appSyncClient.query({
        query: gql(query),
        variables,
        fetchPolicy: "network-only",
      });
      return response;
    } catch (err) {
      console.log("Error while trying to fetch data", err);
      throw JSON.stringify(err);
    }
  }

  async executeMutation(query, variables) {
    try {
      const response = await this.appSyncClient.mutate({
        mutation: gql(query),
        variables,
        fetchPolicy: "no-cache",
      });
      return response;
    } catch (err) {
      console.log("Error while trying to mutate data", err);
      throw JSON.stringify(err);
    }
  }
}

// --- All Query Operations ---
class QueryOperations extends GraphQLOperations {
  constructor(client) {
    super(client);
  }
  async listEmployeesWithNegativePl() {
    try {
      return await this.fetchAllData(queries.listEmployees, {
        filter: {
          privilege_leave_balance: { lt: 0 },
        },
      });
    } catch (err) {
      throw new Error(`Failed to listEmployees: ${err}`);
    }
  }
}

// --- All Mutation Operations ---

class MutationOperations extends GraphQLOperations {
  constructor(client) {
    super(client);
  }

  async createLeave(input) {
    try {
      return await this.executeMutation(queries.createLeave, input);
    } catch (error) {
      console.error("Error in :createLeave ", error);
      throw new Error("Failed to createLeave");
    }
  }

  async createNotification(input) {
    try {
      return await this.executeMutation(queries.createNotification, input);
    } catch (error) {
      throw new Error(`Failed to createNotification: ${error}`);
    }
  }
}

// Instantiate query and mutation operations with singleton AppSync client
const queryOps = new QueryOperations(GraphQL.instance);
const mutationsOps = new MutationOperations(GraphQL.instance);

module.exports = {
  queryOps,
  mutationsOps,
};
