const AWS = require("aws-sdk");
const { AUTH_TYPE, default: AWSAppSyncClient } = require("aws-appsync");
const { REGION, APP_SYNC_URL } = require("../utils/constants");
AWS.config.update({ region: REGION });

/*
 *  Singleton pattern to ensure only one instance is created and reused
 */

class SES {
  static #instance;

  static get instance() {
    if (!SES.#instance) {
      SES.#instance = new AWS.SES();
    }
    return SES.#instance;
  }
}

class Cognito {
  static #instance;

  static get instance() {
    if (!Cognito.#instance) {
      Cognito.#instance = new AWS.CognitoIdentityServiceProvider();
    }
    return Cognito.#instance;
  }
}

class GraphQL {
  static #appSyncClient;

  static get instance() {
    if (!GraphQL.#appSyncClient) {
      GraphQL.#appSyncClient = new AWSAppSyncClient({
        url: APP_SYNC_URL,
        region: REGION,
        auth: {
          type: AUTH_TYPE.AWS_IAM,
          credentials: AWS.config.credentials,
        },
        disableOffline: true,
      });
    }

    return GraphQL.#appSyncClient;
  }
}

class S3 {
  static #instance;
  static get instance() {
    if (!S3.#instance) {
      S3.#instance = new AWS.S3();
    }
    return S3.#instance;
  }
}
module.exports = {
  SES,
  Cognito,
  GraphQL,
  S3,
};
