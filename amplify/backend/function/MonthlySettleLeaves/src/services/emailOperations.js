const { SES } = require("./awsServiceClient");
const { captureError } = require("/opt/sentryWrapper");

/**
 * @class EmailOperation
 * @description
 * The EmailOperation class provides methods to send customized templated emails with or without attachments
 */

class EmailOperation {
  constructor(sesClient) {
    this.sesClient = sesClient;
  }

  async sendEmail(params) {
    try {
      const response = await this.sesClient.sendRawEmail(params).promise();
      console.log("✅ Email sent:", response.MessageId);
      return response;
    } catch (error) {
      throw error;
    }
  }

  async getCustomizedTemplate(html) {
    try {
      const { Template } = await SES.instance
        .getTemplate({ TemplateName: "HubGenericNotification" })
        .promise();

      return Template.HtmlPart.replace("{{body}}", html);
    } catch (error) {
      throw new Error(`Error fetching SES template: ${error.message}`);
    }
  }

  async buildEmailWithReportAttachment(...args) {
    const [email, fileName, xlsxBase64] = args;
    const boundary = "NextPartBoundary";
    const monthName = new Date().toLocaleString("default", { month: "long" });
    const body = `
      <p>Hello,</p>
      <p>We hope this message finds you well.</p>
      <p>Please find the attached <strong>Negative Leave Balance Report</strong>, which includes employees whose privilege leave balance has dropped below zero.</p>
      <p>We kindly request you to review the report and take the necessary action if required.</p>
  `;
    const html = await this.getCustomizedTemplate(body);
    const rawEmail = [
      `From: "The Hub" <<EMAIL>>`,
      `To: ${email}`,
      `Subject: Negative Leave Balance Report – ${monthName}`,
      `MIME-Version: 1.0`,
      `Content-Type: multipart/mixed; boundary="${boundary}"`,
      ``,
      `--${boundary}`,
      `Content-Type: text/html; charset=utf-8`,
      `Content-Transfer-Encoding: 7bit`,
      ``,
      html.trim(),
      ``,
      `--${boundary}`,
      `Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; name="${fileName}"`,
      `Content-Disposition: attachment; filename="${fileName}"`,
      "Content-Transfer-Encoding: base64",
      ``,
      xlsxBase64,
      ``,
      `--${boundary}--`,
    ].join("\n");

    const params = {
      RawMessage: {
        Data: Buffer.from(rawEmail),
      },
    };

    return params;
  }

  async buildEmailForNoRecordsFound(email) {
    const monthName = new Date().toLocaleString("default", { month: "long" });
    const boundary = `NextPartBoundary_${Date.now()}`;
    const subject = `No Negative Leave Balances Found – ${monthName}`;
    const body = `
      <p>Hello,</p>
      <p>We wanted to inform you that there are <strong>no employees with negative privilege leave balances</strong> report for the month of <strong>${monthName}</strong>.</p>
    `;
    const html = await this.getCustomizedTemplate(body);
    const rawMessage = [
      `From: "The Hub" <<EMAIL>>`,
      `To: ${email}`,
      `Subject: ${subject}`,
      `MIME-Version: 1.0`,
      `Content-Type: multipart/alternative; boundary="${boundary}"`,
      ``,
      `--${boundary}`,
      `Content-Type: text/html; charset="UTF-8"`,
      `Content-Transfer-Encoding: 7bit`,
      ``,
      html.trim(),
      ``,
      `--${boundary}--`,
    ].join("\n");

    const params = {
      RawMessage: {
        Data: Buffer.from(rawMessage),
      },
    };
    return params;
  }

  async sendEmailToUsers(...args) {
    const [emails, fileBuffer, fileName] = args;
    await Promise.all(
      emails.map(async (email) => {
        try {
          const params = fileBuffer
            ? await this.buildEmailWithReportAttachment(
                email,
                fileName,
                fileBuffer.toString("base64")
              )
            : await this.buildEmailForNoRecordsFound(email);
          await this.sendEmail(params);
        } catch (err) {
          console.error(`❌ Error sending email to ${email}:`, err?.message);
          captureError(`Error sending email to ${email}: ${err}`);
        }
      })
    );
    console.log(
      "✅ Email send successfully to HR's Executive and Unit Team Leader."
    );
  }
}

// Instantiate EmailOperation with a singleton client instance
const emailClient = new EmailOperation(SES.instance);
module.exports = emailClient;
