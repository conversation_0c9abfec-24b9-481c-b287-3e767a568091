/**
 * The RawQuery class contains GraphQL query and mutation strings
 * */

class RawQuery {
  listEmployees = `
              query MyQuery($filter: ModelEmployeeFilterInput,$limit:Int,$nextToken:String) {
                  listEmployees(
                      filter:  $filter
                      limit: $limit
                      nextToken: $nextToken
                  ) {
                      items {
                      email
                      employee_id
                      privilege_leave_balance
                      employeeSquadId
                      squad {
                          squadSquad_managerId
                      }
                      }
                  }
              }
          `;

  createLeave = `
          mutation MyMutation($type: LEAVE_TYPE!, $leave_length: LEAVE_LENGTH!, $employeeLeavesId: String, $description: String, $count: Float!, $adjustment_type: LEAVE_ADJUSTMENT_TYPE!) {
          createLeave(
              input: {type: $type, adjustment_type: $adjustment_type, count: $count, leave_length: $leave_length, description: $description, employeeLeavesId: $employeeLeavesId}
          ) {
              id
          }
          }
       `;

  createNotification = `
          mutation MyMutation( $input: CreateNotificationInput!) {
          createNotification(input: $input) {
              id
          }
          }
        `;
}

const gql = new RawQuery();
module.exports = gql;
