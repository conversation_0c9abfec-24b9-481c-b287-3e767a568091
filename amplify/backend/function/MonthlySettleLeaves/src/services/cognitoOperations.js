const { USERPOOL_ID } = require("../utils/constants");
const { Cognito } = require("./awsServiceClient");

/**
 * Handle all cognito related operations
 * @class CognitoOperation
 */
class CognitoOperation {
  constructor(cognito) {
    this.client = cognito;
  }

  async listGroupEmail(grpName) {
    try {
      const params = {
        UserPoolId: USERPOOL_ID,
        GroupName: grpName,
      };

      const users = [];
      let response;
      do {
        response = await this.client.listUsersInGroup(params).promise();
        users.push(...response.Users);
        params.NextToken = response.NextToken;
      } while (response.NextToken);

      const emails = users
        .map(
          (user) => user.Attributes.find((attr) => attr.Name === "email")?.Value
        )
        .filter(Boolean);
      return emails;
    } catch (error) {
      throw new Error(`Failed to fetch  group ${grpName} users: ${error}`);
    }
  }
}

// Instantiate CognitoOperation with a singleton client instance
const cognitoClient = new CognitoOperation(Cognito.instance);
module.exports = cognitoClient;
