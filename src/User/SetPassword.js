import { LockOutlined } from "@ant-design/icons";
import { Button, Form, Input, message } from "antd";
import { Auth } from "aws-amplify";
import Background from "Commons/Background";
import Navbar from "Pages/Careers/Navbar";
import { useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { SetEmployeeDetails } from "store/slices/loginSlice";
import { getEmployeeDetails } from "utils/api-actions/user-operation";
import { putRestAPIData } from "utils/RestApiHelper";
import * as yup from "yup";

// Validation schema for password requirements
const passwordSchema = yup.object().shape({
  password: yup
    .string()
    .required("Password is required")
    .min(8, "Password must be at least 8 characters")
    .matches(/[a-z]/, "Include at least one lowercase letter")
    .matches(/[A-Z]/, "Include at least one uppercase letter")
    .matches(/\d/, "Include at least one number"),
  confirmPassword: yup
    .string()
    .required("Confirm your password")
    .oneOf([yup.ref("password")], "Passwords must match"),
});

/**
 * Component for setting a new password after initial authentication
 */
const SetPassword = () => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [isSubmitting, setIsSubmitting] = useState(false); // Loading state

  // Yup sync validation for AntD Form
  const yupSync = useMemo(
    () => ({
      async validator({ field }, value) {
        await passwordSchema.validateSyncAt(field, {
          ...form.getFieldsValue(),
          [field]: value,
        });
      },
    }),
    [form]
  );

  /**
   * Handles form submission to set new password
   * @param {object} values - Form values containing password and confirmPassword
   */
  const onFinish = async (values) => {
    setIsSubmitting(true); // Start loading
    try {
      const currentUser = await Auth.currentAuthenticatedUser();

      // Call API to set new password
      await putRestAPIData("/api/set-password", {
        body: {
          username: currentUser?.username,
          password: values?.password,
          email: currentUser?.attributes?.email,
        },
      });

      // Refresh user session with new credentials
      const cognitoUser = await Auth.currentAuthenticatedUser({
        bypassCache: true,
      });
      const currentSession = await Auth.currentSession();

      await cognitoUser.refreshSession(
        currentSession.refreshToken,
        async () => {
          if (currentUser?.attributes?.email) {
            const data = await getEmployeeDetails(
              currentUser?.attributes?.email
            );
            if (data) {
              dispatch(SetEmployeeDetails(data));
            }
          }
        }
      );

      message.success("Password has been set successfully!");
      setIsSubmitting(false); // Stop loading regardless of success/error
    } catch (error) {
      console.error("Password set error:", error);
      message.error(
        error.message || "Failed to set password. Please try again."
      );
      setIsSubmitting(false); // Stop loading regardless of success/error
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar onlyLogo />
      <Background />

      <main className="container mx-auto px-4 py-8 flex-grow flex flex-col items-center">
        <div className="bg-white p-8 rounded shadow-md w-full max-w-md">
          <h2 className="text-2xl font-bold mb-6 text-center text-primary">
            Set Your Password
          </h2>

          <Form
            form={form}
            name="set-password"
            layout="vertical"
            onFinish={onFinish}
          >
            <Form.Item
              name="password"
              label="New Password"
              rules={[yupSync]}
              hasFeedback
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Enter new password"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label="Confirm Password"
              rules={[yupSync]}
              dependencies={["password"]}
              hasFeedback
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Re-enter password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                block
                loading={isSubmitting} // Show loading indicator on button
              >
                {isSubmitting ? "Setting Password..." : "Set Password"}
              </Button>
            </Form.Item>
          </Form>
        </div>
      </main>
    </div>
  );
};

export default SetPassword;
