import React from "react";
import { useSelector } from "react-redux";
import { BrowserRouter, Navigate, Route, Routes } from "react-router-dom";
import {
  getCurrentToken,
  getCurrentUserData,
  getCurrentUserReportees,
  getEmployeeDeta<PERSON>,
  isAdmin,
  isExecutive,
  isHr,
  isITAd<PERSON>,
  isR<PERSON>ruiter,
  isUnitTeamLeader,
} from "store/slices/loginSlice";
import { engineeringManagers } from "utils/constants";

// 404 Not Found Page
import NotFound from "Pages/NotFound/NotFound";

// Layout Components
import ExecutiveItems from "MainLayout/ExecutiveItems";
import HiringLayout from "MainLayout/HiringLayout";
import MainLayout from "MainLayout/MainLayout";
import ShopLayout from "MainLayout/ShopLayout";

// Authentication Components
import SetPassword from "User/SetPassword";
import Login from "../User/Login";

// Public Routes
import DropResume from "Pages/Careers/DropResume";
import JobApplicationForm from "Pages/Careers/JobApplicationForm";
import JobDetailsPage from "Pages/Careers/JobDetailsPage";
import JobListings from "Pages/Careers/JobListings";
import ListExternalReferrals from "Pages/Public/ListExternalReferrals";
import ReferralProgram from "Pages/Public/ReferralProgram";
import SecureSharing from "Pages/Public/SecureSharing";
import Thankyou from "../Commons/Thankyou";
import CandidateDetails from "../Pages/Public/CandidateDetails";

// Main Application Pages
import HubTypography from "Commons/HubTypography";
import ActionItems from "Pages/ActionItems/ActionItems";
import Feedback from "Pages/Feedback/Components/Feedback";
import FloorPlan from "Pages/FloorPlan";
import Kudos from "Pages/Kudos/Kudos";
import LeaveAndWFH from "Pages/Leave/LeaveAndWFH";
import Detail from "Pages/MBOV2/Components/Detail";
import Meetings from "Pages/MeetingV2/Meetings";
import ProfileWrapper from "Pages/Profile/EmployeeProfile/ProfileWrapper";
import TimeSheet from "Pages/Timesheet/TimeSheet";
import ListZoomRecordings from "Pages/ZoomRecordings/Components/ListZoomRecordings";
import Dashboard from "../Pages/Dashboard/Dashboard";
import EmployeeDirectory from "../Pages/EmployeeDirectory/EmployeeDirectory";
import MBO from "../Pages/MBOV2/Components/MBO";
import Profile from "../Pages/Profile/Profile";

// Organization Pages
import Guilds from "Pages/Organization/Guilds/Guilds";
import ProductUnit from "Pages/Organization/ProductUnit/Pages/ProductUnit";
import Squad from "../Pages/Organization/Squad/Squad";

// Project Management
import Project from "../Pages/Project/Project";
import ProjectView from "../Pages/Project/ProjectView";
import ProjectDetails from "Pages/ProjectV2/Pages/ProjectDetails";
import ProjectList from "Pages/ProjectV2/Pages/ProjectList";

// Finance & Accounting
import Accounting from "Pages/Accounting/Accounting";
import Finance from "Pages/Finance/Finance";
import QuotationDetail from "Pages/Quot/QuotationDetail";
import Quot from "../Pages/Quot/quot";

// HR & Admin
import Inventory from "Pages/Inventory/Inventory";
import PendingChecklist from "Pages/PendingChecklist/PendingChecklist";
import Redflag from "Pages/Redflags/Redflag";

// Hiring Process
import Assessment from "Pages/Hiring/Pages/Assessment/Assessment";
import AssessmentFeedbacks from "Pages/Hiring/Pages/Assessment/AssessmentFeedbacks";
import CandidateUnSubscribe from "Pages/Hiring/Pages/Assessment/CandidateUnSubscribe";
import CandidateListV2 from "Pages/Hiring/Pages/Candidates/CandidateListV2";
import CandidateProfile from "Pages/Hiring/Pages/Candidates/CandidateProfile";
import CreateCandidate from "Pages/Hiring/Pages/Candidates/CreateCandidate";
import { UpdateCandidate } from "Pages/Hiring/Pages/Candidates/UpdateCandidate";
import CandidatePreferredTimeSelector from "Pages/Hiring/Pages/Interviews/CandidatePreferredTimeSelector";
import EmployeeInterviews from "Pages/Hiring/Pages/Interviews/EmployeeInterviews";
import GoogleCalendarRediretion from "Pages/Hiring/Pages/Interviews/GoogleCalendarRediretion";
import InterviewDetails from "Pages/Hiring/Pages/Interviews/InterviewDetails";
import InterviewFeedbackPage from "Pages/Hiring/Pages/Interviews/InterviewFeedbackPage";
import InterviewListV2 from "Pages/Hiring/Pages/Interviews/InterviewListV2";
import InterviewerFeedbackForm from "Pages/Hiring/Pages/Interviews/InterviewerFeedbackForm";
import PreferredTimeSelector from "Pages/Hiring/Pages/Interviews/PreferredTimeSelector";
import CreateUpdateHiringPosition from "Pages/Hiring/Pages/JobPosts/CreateUpdateHiringPosition";
import JobPositions from "Pages/Hiring/Pages/JobPosts/JobPositions";
import JobPositionsDetailed from "Pages/Hiring/Pages/JobPosts/JobPositionsDetailed";
import HiringOverview from "Pages/Hiring/Pages/Overview/HiringOverview";
import ReferralsList from "Pages/Hiring/Pages/Referrals/ReferralsList";

// Shop
import Coins from "Pages/ShopV2/Pages/Coins";
import HowItworks from "Pages/ShopV2/Pages/HowItworks";
import Orders from "Pages/ShopV2/Pages/Orders";
import Products from "Pages/ShopV2/Pages/Products";
import Transactions from "Pages/ShopV2/Pages/Transactions";

// Analytics & Reports
import CodeReviewAnalytics from "Pages/Analytics/CodeReviewAnalytics/CodeReviewAnalytics";
import EmployeeAllocationReport from "Pages/Analytics/EmployeeAllocationReport/EmployeeAllocationReport";
import EmployeeOverviewReport from "Pages/Analytics/EmployeeOverviewReport/EmployeeOverviewReport";
import Reports from "Pages/Analytics/reports";
import CandidatesAddedReport from "../Pages/Analytics/CandidatesAddedReport/CandidatesAddedReport";
import InterviewsOverviewReport from "Pages/Analytics/InterviewsOverviewReport/InterviewsOverviewReport";
import JiraAnalytics from "Pages/Analytics/JiraAnalytics/JiraAnalytics";
import HrOnBoardingDashboard from "Pages/OnBoarding/HrOnBoarding/HrOnBoardingDashboard";
import HrOnBoardingApprove from "Pages/OnBoarding/HrOnBoarding/HrOnBoardingApprove";
import Game from "Pages/Game/Game";
// import RuleManagement from "Pages/RuleManagement/RuleManagement";
import AccessControlDashboard from "Pages/GroupManagement/GroupManagementDashboard";
import GroupManagementDashboard from "Pages/GroupManagement/GroupManagementDashboard";
import RuleManagement from "Pages/RuleManagement/RuleManagement";
// Onboarding
const EmployeeOnBoarding = React.lazy(() =>
  import("Pages/OnBoarding/EmployeeOnboarding/UnifiedEmployeeOnboarding")
);
const UnderReview = React.lazy(() =>
  import("Pages/OnBoarding/UnderReview/UnderReview")
);

/**
 * Application Router Component
 *
 * Manages all application routes with:
 * - Public routes (accessible without authentication)
 * - Private routes (require authentication)
 * - Role-based access control
 * - Account status handling (pending password, onboarding)
 * - Layout management
 */
export default function Router() {
  // User authentication and role data
  const token = useSelector(getCurrentToken);
  const currentUser = useSelector(getEmployeeDetails);
  const { account_status = null } = currentUser || {};

  // User role checks
  const isAExecutive = useSelector(isExecutive);
  const isAHr = useSelector(isHr);
  const userReportees = useSelector(getCurrentUserReportees);
  const isUserPUL = useSelector(isUnitTeamLeader);
  const isAITAdmin = useSelector(isITAdmin);
  const isARecruiter = useSelector(isRecruiter);
  const isAAdmin = useSelector(isAdmin);
  const curretnUserData = useSelector(getCurrentUserData);

  // ======================
  // Route Definitions
  // ======================

  /**
   * Public routes accessible without authentication
   */
  const publicRoutes = [
    {
      path: "/careers",
      element: <JobListings />,
    },
    {
      path: "/thankyou",
      element: <Thankyou />,
    },
    {
      path: "/careers/jobs/:id",
      element: <JobDetailsPage />,
    },
    {
      path: "/careers/apply/jobs/:id",
      element: <JobApplicationForm />,
    },
    {
      path: "/careers/apply",
      element: <DropResume isPublic />,
    },
    {
      path: "/candidateDetails/:id",
      element: <CandidateDetails />,
    },
    {
      path: "/assessment/:id",
      element: <Assessment />,
    },
    {
      path: "/mail-unsubscribe",
      element: <CandidateUnSubscribe />,
    },
    {
      path: "/candidate-time-selection/:id",
      element: <CandidatePreferredTimeSelector />,
    },
    {
      path: "/interviewer-feedback/:id",
      element: <InterviewerFeedbackForm />,
    },
    {
      path: "/referral-program",
      element: <ReferralProgram />,
    },
    {
      path: "/referral-program/:id",
      element: <ReferralProgram />,
    },
    {
      path: "/referrals/:id",
      element: <ListExternalReferrals />,
    },
    {
      path: "/link/:id",
      element: <SecureSharing />,
    },
  ];

  /**
   * Routes for unauthenticated users
   */
  const nonLoggedInRoutes = [
    {
      path: "/login",
      element: <Login />,
    },
  ];

  /**
   * Routes for users with pending password setup
   */
  const passwordRoute = [
    {
      path: "/set-new-password",
      directRender: true,
      element: <SetPassword />,
    },
  ];

  /**
   * Private routes for authenticated users with proper account status
   * Organized by functional areas with role-based access control
   */
  const privateRoutes = [
    // Core Application
    {
      path: "/dashboard",
      element: <Dashboard />,
    },
    {
      path: "/profile",
      element: <ProfileWrapper />,
    },
    {
      path: "/employee-view/:name",
      element: <ProfileWrapper />,
    },

    // Time & Attendance
    {
      path: "/timesheet",
      element: <TimeSheet />,
    },
    {
      path: "/leave",
      element: <LeaveAndWFH />,
    },
    {
      path: "/work-from-home",
      element: <LeaveAndWFH />,
    },

    // Organization Structure
    {
      path: "/squad",
      element: <Squad />,
    },
    {
      path: "/guilds",
      element: <Guilds />,
    },
    {
      path: "/product-unit",
      element: <ProductUnit />,
    },
    {
      path: "/game",
      element: <Game />,
    },
    // Employee Management
    {
      path: "/employee-directory",
      element: <EmployeeDirectory />,
    },
    (isAExecutive || isAHr || isUserPUL) && {
      path: "/edit-profile/:name",
      element: <Profile />,
    },

    // Project Management
    {
      path: "/project",
      element: <Project />,
    },
    {
      path: "/project-view/:id",
      element: <ProjectView />,
    },
    {
      path: "/v2/project",
      element: <ProjectList />,
    },
    {
      path: "/v2/project-view/:id",
      element: <ProjectDetails />,
    },

    // Performance & Feedback
    {
      path: "/interviews",
      element: <EmployeeInterviews />,
    },
    {
      path: "/interview-feedback/:id",
      element: <InterviewFeedbackPage />,
    },
    {
      path: "/feedback",
      element: <Feedback />,
    },
    {
      path: "/Kudos",
      element: <Kudos />,
    },
    {
      path: "/mbo",
      element: <MBO />,
    },
    {
      path: "/mbo/:id",
      element: <Detail />,
    },
    (isAExecutive || isUnitTeamLeader) && {
      path: "/action-items",
      element: <ActionItems />,
    },

    // HR & Admin
    (isAExecutive || isAHr || isAITAdmin) && {
      path: "/inventory",
      element: <Inventory />,
    },
    (isAExecutive || isAHr) && {
      path: "/checklist-items",
      element: <PendingChecklist isExecutive={isAExecutive} isHr={isAHr} />,
    },
    (isAExecutive || isUserPUL) && {
      path: "/redflags",
      element: <Redflag />,
    },

    // Finance
    isAExecutive && {
      path: "/quotation",
      element: <Quot />,
    },
    isAExecutive && {
      path: "/quotation-view/:quotationId",
      element: <QuotationDetail />,
    },
    isAExecutive && {
      path: "/finance",
      element: <Finance />,
    },
    (isAExecutive || isAHr) && {
      path: "/accounting",
      element: <Accounting />,
    },

    // Group Management Dashboard for Access Control
    isAExecutive && {
      path: "/access-control",
      element: <GroupManagementDashboard />,
    },

    // Hiring Process
    ...getHiringRoutes(isAExecutive, isAHr, isUserPUL, isARecruiter),

    // Analytics
    (isAExecutive || isUnitTeamLeader) && {
      path: "/reports",
      element: <Reports />,
    },
    (isAExecutive || isUnitTeamLeader) && {
      path: "/reports/employee-allocation",
      element: <EmployeeAllocationReport />,
    },
    (isAExecutive || isUnitTeamLeader) && {
      path: "/reports/candidates-added-report",
      element: <CandidatesAddedReport />,
    },
    (isAExecutive || isUnitTeamLeader) && {
      path: "/reports/interviews-overview-report",
      element: <InterviewsOverviewReport />,
    },
    (isAExecutive || isUnitTeamLeader) && {
      path: "/reports/employees-overview",
      element: <EmployeeOverviewReport />,
    },
    (isAExecutive ||
      isUnitTeamLeader ||
      engineeringManagers?.includes(curretnUserData?.email)) && {
      path: "/reports/code-quality",
      element: <CodeReviewAnalytics />,
    },
    (isAExecutive ||
      isUnitTeamLeader ||
      engineeringManagers?.includes(curretnUserData?.email)) && {
      path: "/reports/jira-analytics",
      element: <JiraAnalytics />,
    },

    // Onboarding
    (isAExecutive || isAHr) && {
      path: "/employee-onboarding-dashboard",
      element: <HrOnBoardingDashboard />,
    },
    (isAExecutive || isAHr) && {
      path: "/hr-onboarding-approve/:name",
      element: <HrOnBoardingApprove />,
    },
    isAExecutive && {
      path: "/rules-management",
      element: <RuleManagement />,
    },

    // Shop
    ...getShopRoutes(isAExecutive, isUserPUL, isAAdmin),

    // Miscellaneous
    (userReportees?.length || isAHr || isAExecutive) && {
      path: "/meeting",
      element: <Meetings />,
    },
    (isAExecutive || isAHr) && {
      path: "/executive-items",
      element: <ExecutiveItems />,
    },
    {
      path: "/typography",
      element: <HubTypography />,
    },
    {
      path: "/zoomRecordings",
      element: <ListZoomRecordings />,
    },
    {
      path: "/interviewer-time-selection/:id",
      element: <PreferredTimeSelector />,
    },
    {
      path: "/google-calendar-redirection",
      element: <GoogleCalendarRediretion />,
    },
    {
      path: "/floor-plan",
      element: <FloorPlan />,
    },
  ].filter(Boolean);

  return (
    <BrowserRouter>
      <Routes>
        {/* Non-logged in Routes */}
        {!token &&
          nonLoggedInRoutes.map((route, index) => (
            <Route
              key={`non-auth-${index}`}
              path={route.path}
              element={route.element}
            />
          ))}

        {/* Authenticated Routes - Only rendered when `token` exists */}
        {token &&
          privateRoutes?.map((item, index) => {
            // Handle different account status cases
            switch (account_status) {
              // Case 1: User needs to set their password
              case "PENDING_PASSWORD":
                return (
                  <React.Fragment key={`password-routes-${index}`}>
                    {passwordRoute?.map((route, i) => (
                      <Route
                        path={route.path}
                        key={`pw-route-${i}`}
                        element={route.element}
                      />
                    ))}
                  </React.Fragment>
                );

              // Case 2: User's profile is pending admin approval
              case "PENDING_APPROVAL":
                return (
                  <Route
                    key="pending-approval-route"
                    path="/new-employee/profile-under-review"
                    element={<UnderReview />}
                  />
                );

              // Case 3: User needs to complete onboarding
              case "ONBOARDING_PENDING":
              case "ONBOARDING_FORM":
                return (
                  <Route
                    key="onboarding-route"
                    path="/new-employee/onboarding"
                    element={<EmployeeOnBoarding />}
                  />
                );

              // Default Case: Regular authenticated routes
              default:
                return item.directRender ? (
                  // Routes that don't need MainLayout wrapper
                  <Route
                    key={`direct-route-${index}`}
                    path={item.path}
                    element={item.element}
                  />
                ) : (
                  // Routes wrapped with MainLayout
                  <Route key={`layout-route-${index}`} element={<MainLayout />}>
                    <Route path={item.path} element={item.element} />
                  </Route>
                );
            }
          })}

        {/* Public Routes */}
        {publicRoutes.map((route, index) => (
          <Route
            key={`public-${index}`}
            path={route.path}
            element={route.element}
          />
        ))}

        {/* Default Redirects */}
        <Route
          path="/"
          element={
            <Navigate
              to={
                token
                  ? account_status === "PENDING_PASSWORD"
                    ? "/set-new-password"
                    : account_status === "PENDING_APPROVAL"
                    ? "/new-employee/profile-under-review"
                    : account_status === "ONBOARDING_PENDING" ||
                      account_status === "ONBOARDING_FORM"
                    ? "/new-employee/onboarding"
                    : "/dashboard"
                  : "/login"
              }
            />
          }
        />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  );
}

/**
 * Helper function to organize hiring-related routes
 */
function getHiringRoutes(isExecutive, isHr, isPUL, isRecruiter) {
  const hasHiringAccess = isExecutive || isHr || isPUL || isRecruiter;
  if (!hasHiringAccess) return [];

  return [
    {
      path: "/hiring",
      directRender: true,
      element: (
        <HiringLayout>
          <HiringOverview />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/job-positions",
      directRender: true,
      element: (
        <HiringLayout>
          <JobPositions />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/job-positions/add",
      directRender: true,
      element: (
        <HiringLayout>
          <CreateUpdateHiringPosition />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/job-positions/edit",
      directRender: true,
      element: (
        <HiringLayout>
          <CreateUpdateHiringPosition />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/job-positions/:id",
      directRender: true,
      element: (
        <HiringLayout>
          <JobPositionsDetailed />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/candidates",
      directRender: true,
      element: (
        <HiringLayout>
          <CandidateListV2 />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/candidates/add",
      directRender: true,
      element: (
        <HiringLayout>
          <CreateCandidate />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/candidates/edit",
      directRender: true,
      element: (
        <HiringLayout>
          <UpdateCandidate />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/candidates/:id",
      directRender: true,
      element: (
        <HiringLayout>
          <CandidateProfile />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/interviews",
      directRender: true,
      element: (
        <HiringLayout>
          <InterviewListV2 />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/interviews/:id",
      directRender: true,
      element: (
        <HiringLayout>
          <InterviewDetails />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/referrals",
      directRender: true,
      element: (
        <HiringLayout>
          <ReferralsList />
        </HiringLayout>
      ),
    },
    {
      path: "/hiring/ai-interview-candidate-feedbacks",
      directRender: true,
      element: (
        <HiringLayout>
          <AssessmentFeedbacks />
        </HiringLayout>
      ),
    },
  ];
}

/**
 * Helper function to organize shop-related routes
 */
function getShopRoutes(isExecutive, isPUL, isAdmin) {
  const hasShopAdminAccess = isExecutive || isPUL || isAdmin;

  return [
    {
      path: "/shop/products",
      directRender: true,
      element: (
        <ShopLayout>
          <Products />
        </ShopLayout>
      ),
    },
    {
      path: "/shop/orders",
      directRender: true,
      element: (
        <ShopLayout>
          <Orders />
        </ShopLayout>
      ),
    },
    hasShopAdminAccess && {
      path: "/shop/manage-coins",
      directRender: true,
      element: (
        <ShopLayout>
          <Coins />
        </ShopLayout>
      ),
    },
    {
      path: "/shop/transactions",
      directRender: true,
      element: (
        <ShopLayout>
          <Transactions />
        </ShopLayout>
      ),
    },
    {
      path: "/shop/how-it-works",
      directRender: true,
      element: (
        <ShopLayout>
          <HowItworks />
        </ShopLayout>
      ),
    },
  ].filter(Boolean);
}
