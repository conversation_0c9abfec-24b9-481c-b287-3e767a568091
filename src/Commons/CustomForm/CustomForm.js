import { Form } from "antd";

/**
 * CustomForm - Enhanced wrapper over Ant Design's Form
 *
 * @param {Object} props
 * @param {string} [props.className=''] - Additional CSS class names
 * @param {React.ReactNode} props.children - Child form items
 * @param {'vertical' | 'horizontal' | 'inline'} [props.layout='vertical'] - Form layout
 * @param {boolean | 'optional'} [props.requiredMark=false] - Required field marker
 * @param {boolean} [props.scrollToError=true] - Auto-scroll to first error
 * @param {Object} [props.rest] - Additional props passed to Form
 *
 * Features:
 * - Smooth scroll to first validation error
 * - Clean vertical layout by default
 * - Pass-through of all Form props
 */
const CustomForm = ({
  className = "",
  children,
  layout = "vertical",
  requiredMark = false,
  scrollToError = true,
  ...rest
}) => {
  return (
    <Form
      layout={layout}
      requiredMark={requiredMark}
      scrollToFirstError={scrollToError ? { behavior: "smooth" } : false}
      className={["custom-form", className].filter(Boolean).join(" ")}
      {...rest}
    >
      {children}
    </Form>
  );
};

export default CustomForm;
