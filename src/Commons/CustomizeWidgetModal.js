import { Button, Modal, Switch, Typography } from "antd";
import { useState } from "react";

function CustomizeWidgetModal({
  isCustomizing,
  setIsCustomizing,
  widgetConfigs,
  onToggleWidget,
}) {
  const [open, setOpen] = useState(false);
  const hiddenCount = Object.values(widgetConfigs).filter(
    (w) => !w.visible
  ).length;
  // const hiddenCount = 1;

  return (
    <>
      <div className="flex flex-col items-end">
        <Button
          onClick={() => setIsCustomizing((prev) => !prev)}
          className="cursor-pointer"
          type="primary"
        >
          {isCustomizing ? "Save Dashboard" : "Customize Dashboard"}
        </Button>
        {hiddenCount > 0 && (
          <Typography.Text
            className="cursor-pointer text-xs text-green-600"
            onClick={() => setOpen(true)}
          >
            {hiddenCount} hidden widget{hiddenCount > 1 ? "s" : ""}
          </Typography.Text>
        )}
      </div>
      <Modal
        open={open}
        onCancel={() => setOpen(false)}
        title="Widget Visibility Settings"
        centered
        footer={null}
      >
        <div className="max-h-[80vh] overflow-y-auto pr-2">
          {Object.entries(widgetConfigs).map(([key, config]) => (
            <div
              key={key}
              className="flex justify-between items-center py-2 border-b"
            >
              <span className="capitalize">{key}</span>
              <Switch
                checked={config.visible}
                onChange={(checked) => onToggleWidget(key, checked)}
              />
            </div>
          ))}
        </div>
      </Modal>
    </>
  );
}

export default CustomizeWidgetModal;
