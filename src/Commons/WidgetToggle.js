import { Switch, Tooltip } from "antd";
import { EyeOutlined, EyeInvisibleOutlined } from "@ant-design/icons";

const WidgetToggle = ({ widgetKey, visible, onChange }) => {
  return (
    <div className="flex items-center gap-2 mb-2">
      <Tooltip title={visible ? "Hide widget" : "Show widget"}>
        <Switch
          checked={visible}
          onChange={(checked) => onChange(checked)}
          checkedChildren={<EyeOutlined />}
          unCheckedChildren={<EyeInvisibleOutlined />}
        />
      </Tooltip>
    </div>
  );
};

export default WidgetToggle;
