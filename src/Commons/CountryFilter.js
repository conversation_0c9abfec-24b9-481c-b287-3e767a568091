import React from "react";
import { Radio, Typography, Row, Col } from "antd";

const CountryFilter = ({
  value = "All",
  onChange,
  showLabel = true,
  size = "default",
  buttonStyle = "outline",
  className = "",
  style = {},
}) => {
  const handleChange = (e) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  return (
    <Row className={className} style={style}>
      <Col span={24}>
        {showLabel && (
          <Typography.Text strong>Filter by Country: </Typography.Text>
        )}
        <Radio.Group
          value={value}
          onChange={handleChange}
          size={size}
          buttonStyle={buttonStyle}
          className={`${showLabel ? "ml-2" : ""}`}
        >
          <Radio.Button value="All">All</Radio.Button>
          <Radio.Button value="India">India</Radio.Button>
          <Radio.Button value="USA">USA</Radio.Button>
        </Radio.Group>
      </Col>
    </Row>
  );
};

export default CountryFilter;
