// EmployeeCard.js
import React from "react";
import { Typography, Tooltip, Divider, Tag } from "antd";
import moment from "moment";
import { MailOutlined, ToolOutlined } from "@ant-design/icons";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import SkillsList from "AtomicComponents/SkillList";
import { LongText } from "utils/commonMethods";

const { Text } = Typography;

const EmployeeCard = ({ employee }) => {
  return (
    <div className="border rounded-xl p-3 shadow-sm bg-white mb-2">
      <div className="flex flex-wrap items-center gap-x-1 gap-y-1 sm:gap-3">
        <Text strong className="!text-sm">
          <RenderEmployeeFullName
            showAvatar
            employee={employee}
            hoverClassName="text-primary-500"
          />
        </Text>
        <Divider type="vertical" className="hidden sm:block mx-0" />

        {employee?.york_start_date && (
          <Tag className="text-xs text-gray-500 whitespace-nowrap ">
            Joined {moment(employee.york_start_date).fromNow()}
          </Tag>
        )}

        <Divider type="vertical" className="hidden sm:block mx-0" />

        {employee?.allocatedDesk ? (
          <>
            <Tooltip title="Allocated Desk">
              <div className="cursor-default sm:text-md text-sm whitespace-nowrap">
                {employee?.allocatedDesk}
              </div>
            </Tooltip>
            <Divider type="vertical" className="hidden sm:block mx-0" />
          </>
        ) : null}
        <Tooltip title="Click to send an email">
          <a href={`mailto:${employee?.email}`} target="_self">
            <MailOutlined className="text-black cursor-pointer text-sm" />
          </a>
        </Tooltip>
      </div>
      <div className="text-sm text-muted-foreground ml-10 mt-1">
        <div className="flex flex-wrap gap-x-2">
          <Text strong>Guild:</Text> {employee?.guildEmployeeId || "—"}
          <Text strong className="ml-2">
            Squad:
          </Text>
          {employee?.squad?.name || "—"}
        </div>

        {employee?.skills?.items?.length > 0 && (
          <div className="mt-2 flex items-center gap-2 flex-wrap">
            <ToolOutlined className="mt-[3px]  text-black" />
            <SkillsList skills={employee.skills.items} limit={3} />
          </div>
        )}
        {employee?.introduction && (
          <div className="mt-2 text-xs min-h-12 overflow-auto flex gap-1">
            <LongText
              text={employee.introduction}
              limit={100}
              isHtmlContent={true}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default EmployeeCard;
