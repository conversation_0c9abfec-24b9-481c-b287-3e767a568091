import React, { useState, useEffect } from "react";
import { Tooltip } from "antd";
import { ReloadOutlined } from "@ant-design/icons";
import moment from "moment";
import { DateFormatWithTime } from "utils/constants";
import { selectLastFetched, SetLastFetched } from "store/slices/loginSlice";
import { useDispatch, useSelector } from "react-redux";

function PageRefresh({ onRefresh, cacheKey }) {
  // Initialize lastFetched time from localStorage or default to now
  const dispatch = useDispatch();
  const lastFetched = useSelector(selectLastFetched);

  const [isLoading, setIsLoading] = useState(false);

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await onRefresh();
      const now = new Date().toISOString();
      if (cacheKey) localStorage.setItem(cacheKey, now);
      dispatch(SetLastFetched(now));
    } catch (error) {
      console.error("Failed to refresh data:", error);
    }
    setIsLoading(false);
  };

  const lastFetchedTime = lastFetched || new Date().toISOString;

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600">Last Fetched:</span>
      <Tooltip
        title={
          lastFetched
            ? moment(lastFetched).format(DateFormatWithTime)
            : "Not fetched yet"
        }
      >
        <div className="text-sm border-b-2 border-dashed border-gray-700 cursor-default pb-0.5">
          {lastFetched ? moment(lastFetched).fromNow() : "Refresh"}
        </div>
      </Tooltip>

      <Tooltip title="Refresh Data">
        <div
          onClick={handleRefresh}
          className="cursor-pointer text-gray-600 hover:text-black"
        >
          <ReloadOutlined spin={isLoading} />
        </div>
      </Tooltip>
    </div>
  );
}
export default PageRefresh;
