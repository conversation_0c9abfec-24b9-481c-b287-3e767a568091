import React from "react";
import { Avatar, Select, Tag } from "antd";
import CustomImage from "Commons/CustomImage";
import { getDefaultAvatarImage } from "utils/commonMethods";

const RenderEmployeeSelect = ({
  employeesList = [],
  value,
  onChange,
  placeholder = "Select employee",
  showSearch = true,
  size = "default",
  disabled = false,
  allowClear = true,
  className = "",
  avatarSize = 24,
  mode,
  ...rest
}) => {
  const renderOption = (employee) => ({
    value: employee.email,
    label: (
      <div className="flex items-center gap-2">
        <Avatar
          size={avatarSize}
          src={
            <CustomImage
              S3Key={employee.profile_pic}
              src={getDefaultAvatarImage(
                employee.first_name,
                employee.last_name
              )}
              className="w-full h-full object-cover"
            />
          }
          className="flex-shrink-0"
        >
          {!employee.profile_pic && (
            <span className="text-xs">
              {employee.first_name?.charAt(0)}
              {employee.last_name?.charAt(0)}
            </span>
          )}
        </Avatar>
        <span className="flex-1 text-left">
          {employee.first_name} {employee.last_name}
        </span>
      </div>
    ),
    // For search functionality, we need searchable text
    searchText: `${employee.first_name} ${employee.last_name} ${employee.email}`,
  });

  // Custom filter function for search
  const filterOption = (input, option) => {
    const searchText = option.searchText || "";
    return searchText.toLowerCase().includes(input.toLowerCase());
  };

  // Custom render for selected value (single mode)
  const renderSelectedValue = (selectedEmail) => {
    const selectedEmployee = employeesList.find(
      (emp) => emp.email === selectedEmail
    );
    if (!selectedEmployee) return selectedEmail;

    return (
      <div className="flex items-center gap-2">
        <Avatar
          size={Math.max(16, avatarSize - 12)}
          src={
            <CustomImage
              S3Key={selectedEmployee.profile_pic}
              src={getDefaultAvatarImage(
                selectedEmployee.first_name,
                selectedEmployee.last_name
              )}
              className="w-full h-full object-cover"
            />
          }
          className="flex-shrink-0 !h-[28px] !w-[28px] rounded-full"
        >
          {!selectedEmployee.profile_pic && (
            <span className="text-xs">
              {selectedEmployee.first_name?.charAt(0)}
              {selectedEmployee.last_name?.charAt(0)}
            </span>
          )}
        </Avatar>
        <span>
          {selectedEmployee.first_name} {selectedEmployee.last_name}
        </span>
      </div>
    );
  };

  // Custom tag render for multiple mode
  const tagRender = (props) => {
    const { label, value, closable, onClose } = props;
    const selectedEmployee = employeesList.find((emp) => emp.email === value);

    if (!selectedEmployee) {
      return (
        <Tag closable={closable} onClose={onClose} style={{ marginRight: 3 }}>
          {value}
        </Tag>
      );
    }

    return (
      <Tag
        closable={closable}
        onClose={onClose}
        className="mr-1 px-2 py-1 inline-flex items-center gap-1 rounded-md max-w-[200px] !pl-1"
      >
        <Avatar
          size={20}
          src={
            <CustomImage
              S3Key={selectedEmployee.profile_pic}
              src={getDefaultAvatarImage(
                selectedEmployee.first_name,
                selectedEmployee.last_name
              )}
              className="w-full h-full object-cover"
            />
          }
          className="flex-shrink-0"
        >
          {!selectedEmployee.profile_pic && (
            <span className="text-xs">
              {selectedEmployee.first_name?.charAt(0)}
              {selectedEmployee.last_name?.charAt(0)}
            </span>
          )}
        </Avatar>
        <span className="truncate text-sm">
          {selectedEmployee.first_name} {selectedEmployee.last_name}
        </span>
      </Tag>
    );
  };

  const options = employeesList.map(renderOption);

  const selectProps = {
    value,
    onChange,
    placeholder,
    showSearch,
    size,
    disabled,
    allowClear,
    className,
    filterOption,
    optionLabelProp: "label",
    options,
    mode,
    dropdownStyle: {
      padding: "4px 0",
    },
    style: { caretColor: "transparent" },
    optionRender: (option) => option.label,
    ...rest,
  };

  if (mode === "multiple") {
    selectProps.tagRender = tagRender;
  } else {
    selectProps.labelRender = ({ label, value }) => renderSelectedValue(value);
  }

  return <Select {...selectProps} />;
};

export default RenderEmployeeSelect;
