import React, { useCallback } from "react";
import { DatePicker as AntDatePicker } from "antd";
import dayjs from "dayjs";

/**
 * HubDatePicker - A customizable wrapper around Ant Design's DatePicker
 *
 * @param {Object} props - Component props
 * @param {string} [props.format='MMM DD, YYYY'] - Date display format
 * @param {boolean} [props.allowFutureDates=true] - Whether to allow selection of future dates
 * @param {boolean} [props.allowPastDates=true] - Whether to allow selection of past dates
 * @param {boolean} [props.showTime=false] - Whether to show time selection
 * @param {boolean} [props.disabled=false] - Disable the entire picker
 * @param {Function} [props.onChange] - Callback when the date is changed
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.rest] - Additional props for AntDatePicker
 */
const HubDatePicker = ({
  format = "MMM DD, YYYY",
  allowFutureDates = true,
  allowPastDates = true,
  showTime = false,
  disabled = false,
  onChange,
  className = "",
  ...rest
}) => {
  /**
   * Memoized function to disable dates based on props
   */
  const disabledDate = useCallback(
    (current) => {
      if (!current) return false;

      const today = dayjs();
      if (!allowFutureDates && current > today.endOf("day")) {
        return true;
      }
      if (!allowPastDates && current < today.startOf("day")) {
        return true;
      }
      return false;
    },
    [allowFutureDates, allowPastDates]
  );

  /**
   * Triggers the onChange handler if provided
   *
   * @param {dayjs.Dayjs | null} date - Selected date
   * @param {string} dateString - Formatted date string
   */
  const handleChange = (date, dateString) => {
    onChange?.(date, dateString);
  };

  return (
    <AntDatePicker
      format={format}
      disabledDate={disabledDate}
      onChange={handleChange}
      showTime={showTime}
      disabled={disabled}
      className={`w-full rounded-lg py-2 px-3 border-gray-300 hover:border-blue-400 focus:border-blue-500 ${className}`}
      placeholder="Select date"
      {...rest}
    />
  );
};

export default HubDatePicker;
