import { Button, Select, message } from "antd";
import { useEffect, useMemo, useRef, useState, useCallback } from "react";
import { executeMutationGraphData, getGraphAllDataList } from "store/Api";
import { handleGraphError } from "utils/toaster";

/**
 * SkillsSelect Component
 *
 * AntD Select for selecting/creating skills dynamically.
 */
const SkillsSelect = ({
  mode = null,
  fieldName,
  form,
  placeholder = "Select skills...",
  tooltip = null,
  formclassName = "",
  maxCount = Infinity,
  ...rest
}) => {
  const [searchText, setSearchText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [skillsList, setSkillsList] = useState([]);
  const selectRef = useRef(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  /**
   * Checks if the current search text matches an existing skill
   */
  const skillExists = useMemo(
    () =>
      skillsList.some(
        (skill) => skill.name.toLowerCase() === searchText.toLowerCase().trim()
      ),
    [searchText, skillsList]
  );

  // Get current selected items count
  const currentCount = (form.getFieldValue(fieldName) || []).length;
  const limitReached = mode && currentCount >= maxCount;

  /**
   * Fetches the list of all skills from the backend
   */
  const fetchSkills = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await getGraphAllDataList("listSkills", { limit: 1000 });
      const items = response?.items || [];
      const sorted = [...items].sort((a, b) => a.name.localeCompare(b.name));
      setSkillsList(sorted);
    } catch (error) {
      handleGraphError(error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch skills on component mount
  useEffect(() => {
    fetchSkills();
  }, [fetchSkills]);

  /**
   * Creates a new skill with the current search text
   */
  const createNewSkill = useCallback(async () => {
    const newSkillName = searchText.trim();
    if (!newSkillName || skillExists) return;

    setIsLoading(true);
    try {
      const newSkill = await executeMutationGraphData("createSkill", {
        input: { name: newSkillName },
      });

      // Update form field value with the new skill
      const currentValue = form.getFieldValue(fieldName) || [];
      const newValue = mode ? [...currentValue, newSkill.name] : newSkill.name;
      form.setFieldsValue({ [fieldName]: newValue });

      setSearchText("");

      // Refresh the skills list
      await fetchSkills();

      selectRef.current?.focus();
    } catch (error) {
      handleGraphError(error);
    } finally {
      setIsLoading(false);
    }
  }, [searchText, skillExists, form, fieldName, mode, fetchSkills]);

  /**
   * Handles search input and conditionally enables "Add new skill" option
   * @param {string} text - The search text
   */
  const handleSearch = useCallback((text) => {
    setSearchText(text);
  }, []);

  const skillOptions = useMemo(
    () =>
      skillsList.map((skill) => ({
        label: skill.name,
        value: skill.name,
        name: skill.name,
      })),
    [skillsList]
  );

  // Modified handleChange function
  const handleChange = (value) => {
    if (mode && value && value.length > maxCount) {
      message.warning(`You can only select up to ${maxCount} skills`);
      value = value.slice(0, maxCount);
    }

    // Update form field value
    form.setFieldsValue({ [fieldName]: value });

    if (rest.onChange) {
      rest.onChange(value);
    }
  };

  // Filter out options that would exceed the limit
  const filteredOptions = useMemo(() => {
    if (limitReached) {
      const selectedValues = form.getFieldValue(fieldName) || [];
      return skillOptions.filter((option) =>
        selectedValues.includes(option.value)
      );
    }
    return skillOptions;
  }, [skillOptions, limitReached, form, fieldName]);

  return (
    <Select
      ref={selectRef}
      mode={mode}
      showSearch
      searchValue={searchText}
      placeholder={placeholder}
      allowClear
      loading={isLoading}
      options={filteredOptions}
      fieldNames={{ label: "label", value: "value" }}
      optionFilterProp="name"
      optionLabelProp="name"
      filterOption={(input, option) =>
        option.name.toLowerCase().includes(input.toLowerCase())
      }
      onSearch={handleSearch}
      onChange={handleChange}
      className={`w-full ${formclassName}`}
      popupClassName="rounded-lg shadow-lg border border-gray-200"
      maxTagCount={mode === "multiple" ? maxCount : undefined}
      maxTagPlaceholder={
        mode === "multiple"
          ? (omittedValues) => `+${omittedValues.length} more`
          : undefined
      }
      open={dropdownOpen}
      onDropdownVisibleChange={(open) => {
        // If limit is reached, show warning and close dropdown
        if (open && limitReached) {
          message.warning(`Maximum of ${maxCount} skills already selected`);
          setDropdownOpen(false);
        } else {
          setDropdownOpen(open);
        }
      }}
      dropdownRender={(menu) => {
        return (
          <div className="flex flex-col">
            {isLoading && !searchText ? (
              <div className="flex items-center justify-center p-4">
                <div className="animate-spin h-6 w-6 border-b-2 border-green-500 rounded-full" />
                <span className="ml-2 text-gray-600">Loading skills...</span>
              </div>
            ) : (
              <div className="max-h-60 overflow-y-auto">
                {limitReached && (
                  <div className="p-2 text-orange-500 font-medium text-center border-b">
                    Maximum of {maxCount} skills already selected
                  </div>
                )}
                {menu}
              </div>
            )}

            {searchText && !limitReached && (
              <div className="border-t border-gray-200 p-2">
                <Button
                  type="text"
                  onMouseDown={(e) => e.preventDefault()}
                  onClick={createNewSkill}
                  disabled={
                    !searchText.trim() ||
                    isLoading ||
                    skillExists ||
                    limitReached
                  }
                  className={`w-full text-left px-3 py-1 rounded-md transition-colors ${
                    !searchText.trim() ||
                    isLoading ||
                    skillExists ||
                    limitReached
                      ? "text-gray-400 cursor-not-allowed"
                      : "text-green-600 hover:bg-green-50"
                  }`}
                >
                  <div className="flex items-center">
                    {isLoading ? (
                      <>
                        <div className="animate-spin h-4 w-4 border-b-2 border-green-500 mr-2 rounded-full" />
                        <span>Adding "{searchText.trim()}"...</span>
                      </>
                    ) : (
                      <>
                        <span className="mr-2">+</span>
                        <span>Add "{searchText.trim()}"</span>
                      </>
                    )}
                  </div>
                </Button>
              </div>
            )}
          </div>
        );
      }}
      notFoundContent={
        <div className="py-2 px-3 text-gray-500">
          {searchText ? "No matching skills found" : "Start typing to search"}
        </div>
      }
      {...rest}
    />
  );
};

export default SkillsSelect;
