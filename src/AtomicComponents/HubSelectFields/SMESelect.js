import { Button, Select } from "antd";
import { useEffect, useMemo, useRef, useState } from "react";
import { executeMutationGraphData, getGraphAllDataList } from "store/Api";
import { handleGraphError } from "utils/toaster";

/**
 * SMESelect Component
 *
 * Reusable Ant Design Select for selecting SMEs with "create-on-the-fly" support.
 *
 * Props:
 * @param {string} fieldName - Form field key
 * @param {object} form - AntD form instance
 * @param {string} [mode=null] - 'multiple' or 'tags' if multi-select
 * @param {string} [placeholder='Select SMEs...'] - Placeholder text
 * @param {string} [tooltip=null] - Tooltip for external use
 * @param {string} [formclassName=''] - Tailwind/extra classes
 * @param {object} rest - Other Select props
 */
const SMESelect = ({
  fieldName,
  form,
  mode = null,
  placeholder = "Select SMEs...",
  tooltip = null,
  formclassName = "",
  ...rest
}) => {
  const [searchText, setSearchText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [smesList, setSmesList] = useState([]);
  const selectRef = useRef(null);

  /**
   * Memoized check: does SME with same name already exist?
   */
  const smeExists = useMemo(() => {
    const trimmed = searchText.trim().toLowerCase();
    return smesList.some((sme) => sme.name.toLowerCase() === trimmed);
  }, [searchText, smesList]);

  /**
   * Fetch and set sorted SME list
   */
  const fetchSMEs = async () => {
    setIsLoading(true);
    try {
      const response = await getGraphAllDataList("listSMES", { limit: 1000 });
      if (response?.items) {
        const sorted = [...response.items].sort((a, b) =>
          a.name.localeCompare(b.name)
        );
        setSmesList(sorted);
      }
    } catch (error) {
      handleGraphError(error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch SMEs on component mount
  useEffect(() => {
    fetchSMEs();
  }, []);

  /**
   * Create new SME using current input, then update form
   */
  const createNewSME = async () => {
    const name = searchText.trim();
    if (!name || smeExists) return;

    setIsLoading(true);
    try {
      const newSME = await executeMutationGraphData("createSME", {
        input: { name },
      });

      const currentValue = form.getFieldValue(fieldName) || [];
      const newValue = mode ? [...currentValue, newSME.name] : newSME.name;

      form.setFieldsValue({ [fieldName]: newValue });
      setSearchText("");

      // Refresh the SMEs list
      await fetchSMEs();
      selectRef.current?.focus();
    } catch (error) {
      handleGraphError(error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle input search
   */
  const handleSearch = (text) => setSearchText(text);

  /**
   * SME options formatted for AntD Select
   */
  const smeOptions = useMemo(
    () =>
      smesList.map((sme) => ({
        label: sme.email ? `${sme.name} (${sme.email})` : sme.name,
        value: sme.name,
        name: sme.name,
        ...(sme.email && { email: sme.email }),
      })),
    [smesList]
  );

  return (
    <Select
      ref={selectRef}
      mode={mode}
      showSearch
      allowClear
      loading={isLoading}
      searchValue={searchText}
      options={smeOptions}
      placeholder={placeholder}
      className={`w-full ${formclassName}`}
      popupClassName="rounded-lg shadow-lg border border-gray-200"
      fieldNames={{ label: "label", value: "value" }}
      optionLabelProp="name"
      optionFilterProp="name"
      filterOption={(input, option) =>
        option.name.toLowerCase().includes(input.toLowerCase()) ||
        option.email?.toLowerCase().includes(input.toLowerCase())
      }
      onSearch={handleSearch}
      dropdownRender={(menu) => (
        <div className="flex flex-col">
          {/* Show loader when loading without user input */}
          {isLoading && !searchText ? (
            <div className="flex items-center justify-center p-4">
              <div className="animate-spin h-6 w-6 border-b-2 border-green-500 rounded-full" />
              <span className="ml-2 text-gray-600">Loading SMEs...</span>
            </div>
          ) : (
            <div className="max-h-60 overflow-y-auto">{menu}</div>
          )}

          {/* "Add new SME" CTA */}
          {searchText && (
            <div className="border-t border-gray-200 p-2">
              <Button
                type="text"
                onMouseDown={(e) => e.preventDefault()}
                onClick={createNewSME}
                disabled={!searchText.trim() || isLoading || smeExists}
                className={`w-full text-left px-3 py-1 rounded-md transition-colors ${
                  !searchText.trim() || isLoading || smeExists
                    ? "text-gray-400 cursor-not-allowed"
                    : "text-green-600 hover:bg-green-50"
                }`}
              >
                <div className="flex items-center">
                  {isLoading ? (
                    <>
                      <div className="animate-spin h-4 w-4 border-b-2 border-green-500 mr-2 rounded-full" />
                      <span>Adding "{searchText.trim()}"...</span>
                    </>
                  ) : (
                    <>
                      <span className="mr-2">+</span>
                      <span>Add "{searchText.trim()}"</span>
                    </>
                  )}
                </div>
              </Button>
            </div>
          )}
        </div>
      )}
      notFoundContent={
        <div className="py-2 px-3 text-gray-500">
          {searchText ? "No matching SMEs found" : "Start typing to search"}
        </div>
      }
      {...rest}
    />
  );
};

export default SMESelect;
