import { Select } from "antd";
import { optionsMap } from "./utils/constants";

/**
 * SelectWrapper - Reusable Select component wrapper with Ant Design
 *
 * @param {Object} props
 * @param {string} placeholder - Placeholder text for the select
 * @param {any} value - Currently selected value
 * @param {Array} options - Options for the dropdown
 * @param {Object} rest - Additional props passed to AntD Select
 * @returns {JSX.Element}
 */
const SelectWrapper = ({ placeholder, value, options, ...rest }) => (
  <Select
    placeholder={placeholder}
    allowClear
    value={value}
    options={options}
    onSelect={() => {}} // Keep dropdown open after selection
    {...rest}
  />
);

/**
 * GenderSelect - Select component for choosing gender
 *
 * @param {Object} props - Props passed to SelectWrapper
 * @returns {JSX.Element}
 */
export const GenderSelect = ({ placeholder = "Select gender", ...props }) => (
  <SelectWrapper
    placeholder={placeholder}
    options={optionsMap.genderOptions}
    {...props}
  />
);

/**
 * BloodGroupSelect - Select component for choosing blood group
 *
 * @param {Object} props - Props passed to SelectWrapper
 * @returns {JSX.Element}
 */
export const BloodGroupSelect = ({
  placeholder = "Select blood group",
  ...props
}) => (
  <SelectWrapper
    placeholder={placeholder}
    options={optionsMap.bloodGroupOptions}
    {...props}
  />
);
