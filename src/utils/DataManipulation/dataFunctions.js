import { UploadConstants } from "utils/constants";
import { notifyError } from "utils/toaster";

/**
 * Validates a file against format and size constraints
 *
 * @param {File} file - The file to validate
 * @param {string} validFormats - Comma-separated allowed extensions (e.g., ".jpg,.png")
 * @param {number} [maxSize=UploadConstants.MAX_SIZE] - Maximum file size in MB (default: 10MB)
 * @returns {boolean} True if file is valid, false otherwise
 * @example
 * // Returns true if valid
 * validateFile(file, ".jpg,.png", 5);
 */
export const validateFile = (
  file,
  validFormats,
  maxSize = UploadConstants.MAX_SIZE
) => {
  if (!file || !validFormats) {
    // Invalid file or format specification
    notifyError({ message: "Invalid file or format specification" });
    return false;
  }

  // Convert valid formats to a Set for fast lookup (case-insensitive)
  const allowedExtensions = new Set(
    validFormats
      .split(",")
      .map((ext) => ext.trim().toLowerCase().replace(/^\./, ""))
  );

  // Extract file extension, default to empty if not found
  const fileExtension =
    file.name
      .slice(((file.name.lastIndexOf(".") - 1) >>> 0) + 2)
      .toLowerCase() || "";

  // Check if the file extension is in the allowed list
  if (!allowedExtensions.has(fileExtension)) {
    notifyError({
      message: `Invalid file format. Allowed formats: ${validFormats.replace(
        /\./g,
        ""
      )}. You uploaded a .${fileExtension} file.`,
    });
    return false;
  }

  // Validate file size (converts bytes to MB)
  const maxSizeBytes = maxSize * 1024 * 1024; // Convert MB to bytes
  if (file.size > maxSizeBytes) {
    notifyError({
      message: `File must be smaller than ${maxSize}MB. You uploaded a ${Math.round(
        file.size / (1024 * 1024)
      )}MB file.`,
    });
    return false;
  }

  return true; // Valid file
};
