import { message as ToastMessage } from "antd";
import { Auth } from "aws-amplify";

// Default error messages
const DEFAULT_ERROR_MESSAGE = "Something went wrong! Please try again later.";
const SESSION_TIMEOUT_MESSAGE =
  "Your session has timed out due to inactivity. Please log in again to continue.";
const UNAUTHORIZED_MESSAGE = "Not authorized to perform this action.";

/**
 * Maps specific error types to user-friendly messages.
 * Extend this map to support more specific error types.
 * @param {string} errorType
 * @returns {string}
 */
const getErrorMessage = (errorType) => {
  const errorMap = {
    Unauthorized: UNAUTHORIZED_MESSAGE,
    // Extendable: 'SomeErrorType': 'Custom message'
  };
  return errorMap[errorType] || DEFAULT_ERROR_MESSAGE;
};

/**
 * Displays an Ant Design error toast.
 * @param {Object} params
 * @param {string} params.message
 */
export const notifyError = ({ message }) => {
  ToastMessage.error(message);
};

/**
 * Extracts the most relevant message from a wide variety of error shapes.
 * Handles AWS AppSync, Amplify, GraphQL, and custom error formats.
 * @param {Error|Object} error
 * @returns {Promise<string>}
 */
const extractErrorMessage = async (error) => {
  if (error?.message) return error.message;
  if (error?.Message) return error.Message;
  if (Array.isArray(error?.errors) && error.errors.length > 0) {
    const errorType = error.errors[0]?.errorType;
    return getErrorMessage(errorType);
  }
  return DEFAULT_ERROR_MESSAGE;
};

/**
 * Centralized error handler for GraphQL/API/AWS Amplify requests.
 * Handles common issues like session expiration, unauthorized access, and user cancellation.
 * @param {Error|Object} error
 * @returns {Promise<boolean|null>}
 */
export const handleGraphError = async (error) => {
  console.error("Graph error:", error);

  // If the request was manually canceled (e.g., Axios CancelToken)
  if (error?.name === "CanceledError") return true;

  // Handle session timeout errors from AWS Amplify Auth
  if (error?.message === "No current user") {
    await handleSessionTimeout();
    return null;
  }

  // Fallback: show user-friendly extracted error message
  const errorMessage = await extractErrorMessage(error);
  notifyError({ message: errorMessage });

  return null;
};

/**
 * Handles session timeout: signs user out and reloads the app.
 */
const handleSessionTimeout = async () => {
  notifyError({ message: SESSION_TIMEOUT_MESSAGE });
  await Auth.signOut({ global: false }); // Local sign-out only
  localStorage.clear();
  window.location.reload(); // Reload app to re-init auth state
};
