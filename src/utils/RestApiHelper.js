import { API } from "aws-amplify";
import { handleGraphError } from "./toaster";

// Configuration constants
const AWS_COGNITO_NAME = "RestHRMS";
// const PUBLIC_PATH_PREFIX = '/public/';
const SUCCESS_STATUS_CODE = 200;

// Object to track ongoing requests for cancellation support
const ongoingRequests = {};

/**
 * Fetches data from API with cancellation support and proper error handling.
 * Automatically cancels any previous request for the same path.
 *
 * @param {string} path - The API endpoint path
 * @param {Object|null} [filter=null] - Optional query parameters
 * @param {AbortSignal|null} [signal=null] - Optional AbortSignal from an existing controller
 * @returns {Promise<any>} Resolves with response data if successful
 * @throws {Error} When request fails or is canceled
 */
export const getRestAPIData = async (path, filter = null, signal = null) => {
  // Cancel any ongoing request for this path
  if (ongoingRequests[path]) {
    ongoingRequests[path].abort("Canceling previous request for same path");
    delete ongoingRequests[path];
  }

  // Create a new AbortController if none is provided
  const abortController = signal ? signal.controller : new AbortController();
  ongoingRequests[path] = abortController;

  try {
    // Determine if this is a public path that doesn't need auth
    // const isPublicPath = path.includes(PUBLIC_PATH_PREFIX);

    // Get auth token for private paths
    // let authToken = null;
    // if (!isPublicPath) {
    //   try {
    //     const session = await Auth.currentSession();
    //     authToken = session.getIdToken().getJwtToken();
    //   } catch (authError) {
    //     console.warn('Failed to get auth token:', authError);
    //   }
    // }

    // Prepare request options
    const options = {
      signal: abortController.signal,
      // ...(authToken && {
      //   headers: {
      //     Authorization: `Bearer ${authToken}`,
      //   },
      // }),
      ...(filter && { queryStringParameters: filter }),
    };

    // Create the API request promise
    const apiPromise = API.get(AWS_COGNITO_NAME, path, options);

    // Set up cancellation handler to cancel the API request if needed
    abortController.signal.addEventListener("abort", () => {
      API.cancel(
        apiPromise,
        "Request canceled by new request or component unmount"
      );
    });

    // Wait for the API response
    const response = await apiPromise;

    // Handle successful response
    if (response?.statusCode === SUCCESS_STATUS_CODE || response?.success) {
      return response.body || response.data;
    }

    // Handle non-success responses (excluding whitelisted paths)
    const nonErrorPaths = []; // Add paths that shouldn't throw errors here
    if (!nonErrorPaths.some((validPath) => path.includes(validPath))) {
      throw response?.body || new Error("Invalid API response structure");
    }

    return null;
  } catch (error) {
    // Handle errors other than request cancellations
    if (error.name !== "CanceledError") {
      handleGraphError(error);
    }
    throw error;
  } finally {
    // Clean up the ongoing request tracking
    if (ongoingRequests[path]?.signal === abortController.signal) {
      delete ongoingRequests[path];
    }
  }
};

// Calling graph API for POST method
export const putRestAPIData = async (path, data) => {
  try {
    // const authToken = `Bearer ${(await Auth.currentSession())
    //   ?.getIdToken()
    //   ?.getJwtToken()}`;
    const options = {
      // headers: { Authorization: authToken },
      ...(data && { ...data }),
    };
    const { statusCode, body, success } = await API.put(
      AWS_COGNITO_NAME,
      path,
      options
    );
    const nonErrorPath = [];
    // const nonErrorPath = [];
    if (statusCode === SUCCESS_STATUS_CODE || success) {
      return body;
    } else if (!nonErrorPath.some((validPath) => path.includes(validPath))) {
      handleGraphError(body);
      throw body;
    } else {
      return null;
    }
  } catch (error) {
    console.error("Error posting graph data:", error);
    throw error;
  }
};
