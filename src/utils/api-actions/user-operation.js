import dayjs from "dayjs";
import {
  executeMutationGraphData,
  getGraphAllDataList,
  getGraphDetails,
} from "store/Api";
import { getPresignedUrl } from "utils/helperFunction";

/**
 * Constants for configuration
 */
const DATE_FIELDS = ["birth_date", "career_start_date", "anniversary_date"]; // Fields that require date handling
const API_DATE_FORMAT = "YYYY-MM-DD"; // Standard API date format
const DOCUMENT_FIELDS = [
  "aadhar_card",
  "pan_card",
  "passport_photo",
  "profile_pic",
]; // Fields containing document keys

/**
 * Main Employee Service Module
 * Handles all employee data operations including:
 * - Fetching employee details with related data
 * - Updating employee information
 * - Managing skills and SME relationships
 */
export const EmployeeService = {
  /**
   * Fetches complete employee data including documents, skills, and SME
   * @param {string} email - Employee email (required)
   * @returns {Promise<Object>} Normalized employee data
   * @throws {Error} When email is missing or data fetch fails
   */
  getEmployeeDetails: async (email) => {
    if (!email)
      throw new Error("Email is required for fetching employee details");

    try {
      // 1. Fetch base employee data
      const employeeRes = await getGraphDetails("getEmployee", { email });

      // 2. Initialize result object with core data
      const result = {
        ...employeeRes,
        skills: [], // Will be populated below
        sme: "", // Will be populated below
        documentUrls: {}, // Will store presigned URLs
      };

      // 3. Parallel fetch for related data and documents
      const [skills, sme, documentUrls] = await Promise.all([
        EmployeeService._extractEmployeeSkills(email),
        EmployeeService._extractEmployeeSME(email),
        EmployeeService._fetchDocumentUrls(employeeRes),
      ]);

      // 4. Assign fetched data
      result.skills = skills;
      result.sme = sme;
      result.documentUrls = documentUrls;

      // 5. Convert date strings to Day.js objects for form handling
      DATE_FIELDS.forEach((field) => {
        result[field] = employeeRes[field] ? dayjs(employeeRes[field]) : null;
      });

      return result;
    } catch (error) {
      console.error(
        `EmployeeService.getEmployeeDetails failed for ${email}:`,
        error
      );
      throw new Error(`Failed to load employee data: ${error.message}`);
    }
  },

  /**
   * Updates employee data including optional skills and SME
   * @param {Object} data - Employee data to update
   * @param {string} data.email - Employee email (required)
   * @returns {Promise<boolean>} True if successful
   * @throws {Error} When email is missing or update fails
   */
  updateEmployeeDetails: async (data) => {
    const { email, skills, sme, ...employeeData } = data || {};

    if (!email) throw new Error("Employee email is required for update");

    try {
      // 1. Format dates for API consumption
      const formattedData = EmployeeService._formatDatesForApi(employeeData);

      // 2. Update core employee data (excluding relationships)
      await executeMutationGraphData("updateEmployee", {
        input: { email, ...formattedData },
      });

      // 3. Conditional parallel updates for relationships
      const updatePromises = [];

      if ("skills" in data) {
        updatePromises.push(
          EmployeeService._updateEmployeeSkills(email, skills || [])
        );
      }

      if ("sme" in data) {
        updatePromises.push(EmployeeService._updateEmployeeSME(email, sme));
      }

      if (updatePromises.length) {
        await Promise.all(updatePromises);
      }

      return true;
    } catch (error) {
      console.error("EmployeeService.updateEmployeeDetails failed:", error);
      throw error;
    }
  },

  // ==================== Internal Helper Methods ====================

  /**
   * Fetches presigned URLs for all document fields
   * @param {Object} employeeData - Raw employee data containing document keys
   * @returns {Promise<Object>} Map of document field to URL
   */
  _fetchDocumentUrls: async (employeeData) => {
    const urlEntries = await Promise.all(
      DOCUMENT_FIELDS.map(async (field) => [
        field,
        employeeData[field]
          ? await getPresignedUrl({ key: employeeData[field] }).catch(
              () => null
            )
          : null,
      ])
    );

    return Object.fromEntries(urlEntries);
  },

  /**
   * Formats date fields to API string format
   * @param {Object} data - Raw employee data
   * @returns {Object} Data with formatted dates
   */
  _formatDatesForApi: (data) => {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        DATE_FIELDS.includes(key) && value
          ? dayjs(value).format(API_DATE_FORMAT)
          : value,
      ])
    );
  },

  /**
   * Extracts employee skills from API
   * @param {string} email - Employee email
   * @returns {Promise<string[]>} Array of skill IDs
   */
  _extractEmployeeSkills: async (email) => {
    try {
      const { items } = await getGraphAllDataList("listEmployeeSkills", {
        filter: { employeeID: { eq: email } },
      });
      return items.map((item) => item.skillID).filter(Boolean);
    } catch (error) {
      console.error("EmployeeService._extractEmployeeSkills failed:", error);
      return [];
    }
  },

  /**
   * Extracts primary SME from API (ensures single SME)
   * @param {string} email - Employee email
   * @returns {Promise<string>} SME ID or empty string
   */
  _extractEmployeeSME: async (email) => {
    try {
      const { items } = await getGraphAllDataList("listEmployeeSMES", {
        filter: { employeeID: { eq: email } },
      });

      // Enforce single SME by cleaning up extras
      if (items.length > 1) {
        const [primarySme, ...extraSmes] = items;
        await EmployeeService._batchDeleteItems("deleteEmployeeSME", extraSmes);
        return primarySme.sMEID;
      }

      return items[0]?.sMEID || "";
    } catch (error) {
      console.error("EmployeeService._extractEmployeeSME failed:", error);
      return "";
    }
  },

  /**
   * Updates employee skills using optimal diff algorithm
   * @param {string} email - Employee email
   * @param {string[]} newSkills - New skill IDs
   */
  _updateEmployeeSkills: async (email, newSkills) => {
    try {
      const { items: currentSkills } = await getGraphAllDataList(
        "listEmployeeSkills",
        { filter: { employeeID: { eq: email } } }
      );

      const { toAdd, toDelete } = EmployeeService._calculateDifferences(
        currentSkills,
        newSkills,
        "skillID"
      );

      await Promise.all([
        EmployeeService._batchDeleteItems("deleteEmployeeSkill", toDelete),
        EmployeeService._batchCreateItems(
          "createEmployeeSkill",
          toAdd,
          (skill) => ({ employeeID: email, skillID: skill })
        ),
      ]);
    } catch (error) {
      console.error("EmployeeService._updateEmployeeSkills failed:", error);
      throw error;
    }
  },

  /**
   * Updates employee SME (maintains single relationship)
   * @param {string} email - Employee email
   * @param {string|null} newSme - New SME ID
   */
  _updateEmployeeSME: async (email, newSme) => {
    try {
      const { items: currentSmes } = await getGraphAllDataList(
        "listEmployeeSMES",
        { filter: { employeeID: { eq: email } } }
      );

      // Remove existing SME if changing
      if (
        currentSmes.length > 0 &&
        (!newSme || currentSmes[0].sMEID !== newSme)
      ) {
        await EmployeeService._batchDeleteItems(
          "deleteEmployeeSME",
          currentSmes
        );
      }

      // Add new SME if provided and different
      if (newSme && (!currentSmes.length || currentSmes[0].sMEID !== newSme)) {
        await executeMutationGraphData("createEmployeeSME", {
          input: { employeeID: email, sMEID: newSme },
        });
      }
    } catch (error) {
      console.error("EmployeeService._updateEmployeeSME failed:", error);
      throw error;
    }
  },

  /**
   * Calculates differences between current and desired items
   * @param {Array} currentItems - Existing items
   * @param {Array} newItems - Desired items
   * @param {string} idKey - Comparison key
   * @returns {Object} { toAdd: Array, toDelete: Array }
   */
  _calculateDifferences: (currentItems, newItems, idKey = "id") => ({
    toAdd: (newItems || []).filter(
      (id) => !currentItems.some((item) => item[idKey] === id)
    ),
    toDelete: currentItems.filter(
      (item) => !(newItems || []).includes(item[idKey])
    ),
  }),

  /**
   * Batch creates items with single mutation type
   * @param {string} mutationName - GraphQL mutation name
   * @param {Array} items - Items to create
   * @param {Function} inputCreator - Creates input for each item
   */
  _batchCreateItems: async (mutationName, items, inputCreator) => {
    if (!items.length) return;
    await Promise.all(
      items.map((item) =>
        executeMutationGraphData(mutationName, { input: inputCreator(item) })
      )
    );
  },

  /**
   * Batch deletes items with single mutation type
   * @param {string} mutationName - GraphQL mutation name
   * @param {Array} items - Items to delete
   */
  _batchDeleteItems: async (mutationName, items) => {
    if (!items.length) return;
    await Promise.all(
      items.map((item) =>
        executeMutationGraphData(mutationName, { input: { id: item.id } })
      )
    );
  },
};

// Export the main service methods for backward compatibility
export const { getEmployeeDetails, updateEmployeeDetails } = EmployeeService;
