/**
 * Validation message generator utility for consistent form validations
 */

// Required field messages
export const REQUIRED_ERROR = (field) => `${field} is required!`;
export const REQUIRED_TYPE = (field, type) => `${field} must be ${type}.`;
export const REQ_SELECTED = () => "At least one option must be selected.";

// Password complexity messages
export const ONE_UPPER_CASE = () =>
  "At least one uppercase letter is required.";
export const ONE_LOWER_CASE = () =>
  "At least one lowercase letter is required.";
export const ONE_DIGIT_CASE = () => "At least one digit is required.";
export const ONE_SPECIAL_CHAR = () =>
  "At least one special character is required.";

// Character restriction messages
export const ALLOW_CHARACTERS = () => "Only alphabetic characters are allowed.";
export const ALLOW_CHAR = () => "Only alphanumeric characters are allowed.";

// Length validation messages
export const MIN_LENGTH = (field, len) =>
  `${field} must be at least ${len} characters long.`;
export const MAX_LENGTH = (field, len) =>
  `${field} must be at most ${len} characters long.`;

// Numeric validation messages
export const MIN_NUMBER = (field, min) =>
  `${field} cannot be less than ${min}.`;
export const MAX_NUMBER = (field, max) =>
  `${field} cannot be more than ${max}.`;
export const MIN_RATE = (rate) => `A minimum rate of ${rate} is required.`;

// Selection validation messages
export const MIN_CHECKED = (field, count) =>
  `At least ${count} option(s) must be checked for ${field}.`;
export const MIN_SELECTED = (field, count) =>
  `At least ${count} ${field} must be selected.`;
export const MAX_SELECTED = (field, count) =>
  `At most ${count} ${field} should be selected.`;
export const SELECTED_MIN = (field, count) =>
  `At least ${count} option(s) must be selected for ${field}.`;

// UI feedback
export const COPY_MESSAGE = (name) => `${name} copied successfully.`;
