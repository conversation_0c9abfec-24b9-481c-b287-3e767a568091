import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  filters: {
    searchTerm: "",
    status: null,
    projectLead: null,
    projectManager: null,
    prodctStrategist: null,
    type: "active",
  },
  cachedProjects: [],
};

const projectListSlice = createSlice({
  name: "projectList",
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = action.payload;
    },
    setCachedProjects: (state, action) => {
      state.cachedProjects = action.payload;
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
  },
});
export const { setFilters, setCachedProjects, clearFilters } =
  projectListSlice.actions;
export default projectListSlice.reducer;
