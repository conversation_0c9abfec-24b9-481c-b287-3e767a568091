/* eslint-disable import/namespace */
import { API } from "aws-amplify";
import * as mutationsQuery from "graphql/appMutations";
import * as appQueries from "graphql/appQueries";
import * as queriesGetCustom from "graphql/customQueries";
import { handleGraphError } from "utils/toaster";

// Combine default mutations with any potential custom mutations
const mutations = { ...mutationsQuery };

/**
 * GraphQL API Helper Module
 * Provides a clean interface for all GraphQL operations with:
 * - Consistent error handling
 * - Type safety
 * - Pagination support
 * - Request cancellation
 */

/**
 * Executes a GraphQL query or mutation and handles errors consistently.
 * @param {string} queryName - The query/mutation name to execute
 * @param {string} query - The GraphQL query or mutation
 * @param {object} variables - Variables to be passed to the query/mutation
 * @returns {Promise<object>} The result data or error
 */
const executeGraphqlQuery = async (queryName, query, variables) => {
  try {
    const { data } = await API.graphql({
      query,
      variables,
    });
    return data[queryName]; // Return specific query result from response
  } catch (error) {
    handleGraphError(error); // Handle errors consistently
    throw error;
  }
};

/**
 * Creates a new record using GraphQL mutation
 * @param {string} query - The mutation name
 * @param {object} variables - Mutation variables
 * @returns {Promise<object>} The created data or error
 */
export const executeMutationGraphData = (query, variables) => {
  if (!mutations[query]) {
    console.error(`Mutation ${query} not found`);
    return null;
  }
  return executeGraphqlQuery(query, mutations[query], variables);
};

/**
 * Fetches a single record by ID using a query
 * @param {string} query - The query name
 * @param {string} id - Record ID to fetch
 * @returns {Promise<object>} The fetched data or error
 */
export const getGraphDataById = (query, id) => {
  return executeGraphqlQuery(query, appQueries[query], { id });
};

/**
 * Fetches a paginated list of records
 * @param {string} query - The query name
 * @param {object} variables - Query variables
 * @returns {Promise<{items: array, total: number}>} Paginated results or error
 */
export const getGraphDataList = (query, variables) => {
  return executeGraphqlQuery(query, appQueries[query], variables).then(
    (data) => {
      return data
        ? { items: data.items || [], total: data.total || 0 }
        : { items: [], total: 0 };
    }
  );
};

/**
 * Fetches complete details for a record using a query
 * @param {string} query - The query name
 * @param {object} variables - Query variables
 * @returns {Promise<object>} Record details or error
 */
export const getGraphDetails = (query, variables) => {
  return executeGraphqlQuery(query, appQueries[query], variables);
};

/**
 * Fetches all records with pagination handling
 * @param {string} query - The query name
 * @param {object} variables - Initial query variables
 * @returns {Promise<{items: array, total: number}>} All records or error
 */
export const getGraphAllDataList = async (query, variables) => {
  const options = { ...variables };
  const combinedItems = [];

  try {
    // Fetch all records in paginated chunks
    do {
      const data = await executeGraphqlQuery(query, appQueries[query], options);
      if (data?.items) {
        combinedItems.push(...data.items);
        options.nextToken = data.nextToken || null; // Use nextToken for pagination
      }
    } while (options.nextToken);

    return { items: combinedItems, total: combinedItems.length };
  } catch (error) {
    return handleGraphError(error);
  }
};

/**
 * Fetches data using custom queries with request cancellation support
 * @param {string} query - The custom query name
 * @param {object} variables - Query variables
 * @param {string} returnKey - The data key to return
 * @param {AbortSignal} [signal] - Optional abort signal for cancellation
 * @returns {Promise<object>} Query results or error
 */
export const getGraphDataListCustom = async (
  query,
  variables,
  returnKey,
  signal
) => {
  try {
    const { data } = await API.graphql(
      { query: queriesGetCustom[query], variables: { ...variables } },
      { signal } // Support for request cancellation
    );
    return data[returnKey]; // Return the desired data key
  } catch (error) {
    return handleGraphError(error);
  }
};

/**
 * Updates an existing record using GraphQL mutation
 * @param {string} query - The mutation name
 * @param {object} variables - Mutation variables
 * @returns {Promise<object>} Updated data or error
 */
export const updateGraphData = (query, variables) => {
  if (!mutations[query]) {
    console.error(`Mutation ${query} not found`);
    return null;
  }
  return executeGraphqlQuery(query, mutations[query], variables);
};
