import React, { useEffect, useState, useRef } from "react";
import JiraQualityCard from "./JiraQualityCard";
import Loader from "Commons/Loader";
import { listJiraQualityForProjectAction } from "Pages/CodeQuality/Actions/JiraQualityActions";

function JiraQualityList(props) {
  const [jiraIssuesList, setJiraIssuesList] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const isFetching = useRef(false);
  const scrollTimeout = useRef(null);
  const [rowIssues, setRowIssues] = useState([]);
  const [nextToken, setNextToken] = useState(null);

  const { jiraProjectId } = props;

  const fetchDetails = async () => {
    if (isFetching.current) return;
    isFetching.current = true;
    setIsLoading(true);

    try {
      const res = await listJiraQualityForProjectAction(
        nextToken,
        jiraProjectId
      );
      setRowIssues((prevList) => [...prevList, ...res?.items]);
      setNextToken(res.nextToken);
      setHasMore(!!res.nextToken);
    } catch (error) {
      console.error("Error fetching jira quality data:", error);
      setHasMore(false);
    } finally {
      isFetching.current = false;
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (jiraProjectId) {
      fetchDetails();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jiraProjectId]);

  useEffect(() => {
    const uniqueIssues = rowIssues?.reduce((acc, current) => {
      const x = acc.find((item) => item.issueKey === current.issueKey);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, []);
    // Group data by issueKey and sort by createdAt (latest first)
    const groupedData = Object.values(
      uniqueIssues?.reduce((acc, item) => {
        let descQuality = item?.descriptionQualityAI;
        if (descQuality && typeof descQuality === "string") {
          try {
            descQuality = JSON.parse(descQuality);
          } catch (e) {
            console.error("Error parsing description quality:", e);
          }
        }

        item = { ...item, descriptionQualityAI: descQuality };
        const key = item.issueKey;
        if (!acc[key]) {
          acc[key] = { ...item, versions: [item] };
        } else {
          acc[key].versions.push(item);
        }
        return acc;
      }, {})
    ).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    setJiraIssuesList(groupedData);
  }, [rowIssues]);

  const handleScroll = (e) => {
    const container = e.target;
    if (scrollTimeout.current) {
      clearTimeout(scrollTimeout.current);
    }
    scrollTimeout.current = setTimeout(() => {
      if (
        container.scrollLeft + container.clientWidth >=
          container.scrollWidth - 50 &&
        hasMore &&
        !isFetching.current
      ) {
        fetchDetails();
      }
    }, 150); // Debounce interval
  };

  return (
    <div>
      {isLoading && jiraIssuesList.length === 0 ? (
        <Loader title={"Please wait while we do some Jira quality check!"} />
      ) : (
        <div
          className="overflow-x-auto whitespace-nowrap p-4"
          onScroll={handleScroll}
        >
          <div className="flex gap-4">
            {jiraIssuesList?.map((issue, index) => (
              <JiraQualityCard
                key={index}
                issue={issue}
                type={props?.type || "EMAIL"}
              />
            ))}
            {isLoading && hasMore && (
              <div className="flex justify-center items-center min-w-96">
                <Loader />
              </div>
            )}
            {!hasMore && jiraIssuesList.length > 0 && (
              <div className="text-center text-gray-500 my-auto h-auto">
                No more issues
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default JiraQualityList;
