import {
  CheckCircleFilled,
  CloseCircleFilled,
  ExportOutlined,
} from "@ant-design/icons";
import { Badge, Card, Tag, Tooltip } from "antd";
import { Storage } from "aws-amplify";
import moment from "moment";
import { renderTag } from "Pages/Hiring/Pages/Candidates/CandidateAssessmentDetail";
import { useEffect, useState } from "react";
import { DateFormatWithTime } from "utils/constants";
import { boxClass } from "utils/TailwindCommonClasses";

const JiraQualityCard = ({ issue, type }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedIssue, setSelectedIssue] = useState(null);
  const [avatarUrl, setAvatarUrl] = useState(null);
  const [creatorAvatarUrl, setCreatorAvatarUrl] = useState(null);

  const handleCardClick = (issue) => {
    setSelectedIssue(issue);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedIssue(null);
  };

  // Function to get score from descriptionQualityAI
  const getScore = () => {
    if (!issue?.descriptionQualityAI) return null;
    // Handle both object format and string format
    if (typeof issue.descriptionQualityAI === "object") {
      return issue.descriptionQualityAI.score;
    }
    try {
      const parsed = JSON.parse(issue.descriptionQualityAI);
      return parsed.score?.N;
    } catch (e) {
      return null;
    }
  };
  //TODO: need to change
  const getJiraUrl = () => {
    return `https://yorkdocs.atlassian.net/browse/${issue.issueKey}`;
  };

  const renderCheckBox = (title, value) => (
    <p className="flex items-center gap-2">
      {typeof value === "boolean" ? (
        value === true ? (
          <CheckCircleFilled className="text-green-500 text-base" />
        ) : (
          <CloseCircleFilled className="text-red-500 text-base" />
        )
      ) : (
        <Badge color="green" count={value} />
      )}
      <span>{title}</span>
    </p>
  );

  // Helper function to check if a field exists in descriptionQualityAI and is not undefined or null or empty string
  const hasQualityField = (field) => {
    if (!issue?.descriptionQualityAI) return false;
    let value;
    if (typeof issue.descriptionQualityAI === "object") {
      value = issue.descriptionQualityAI[field];
    } else {
      try {
        const parsed = JSON.parse(issue.descriptionQualityAI);
        value = parsed[field];
      } catch (e) {
        return false;
      }
    }
    // Allow false boolean, just check for existence and non-empty (not undefined, not null, not empty string)
    return value !== undefined && value !== null && value !== "";
  };

  // Helper function to get a field value from descriptionQualityAI
  const getQualityField = (field) => {
    if (!issue?.descriptionQualityAI) return null;
    if (typeof issue.descriptionQualityAI === "object") {
      if (
        field === "isDescriptionComplete" ||
        field === "isDescriptionClear" ||
        field === "isActionable"
      ) {
        return issue.descriptionQualityAI[field];
      }
      return (
        issue.descriptionQualityAI[field] || issue.descriptionQualityAI[field]
      );
    }
    try {
      const parsed = JSON.parse(issue.descriptionQualityAI);
      if (
        field === "isDescriptionComplete" ||
        field === "isDescriptionClear" ||
        field === "isActionable"
      ) {
        return parsed[field]?.BOOL;
      }
      return parsed[field]?.S || parsed[field]?.N;
    } catch (e) {
      return null;
    }
  };

  // Format the description
  const formatDescription = (desc) => {
    if (!desc) return "";
    return desc.replace(/\\n/g, "\n").replace(/\*/g, "");
  };

  useEffect(() => {
    let isMounted = true;
    async function fetchAvatar() {
      if (issue.employee && issue.employee.profile_pic) {
        try {
          const url = await Storage.get(issue.employee.profile_pic, {
            level: "public",
          });
          if (isMounted) setAvatarUrl(url);
        } catch {
          if (isMounted) setAvatarUrl(null);
        }
      } else {
        setAvatarUrl(null);
      }
    }
    fetchAvatar();
    return () => {
      isMounted = false;
    };
  }, [issue.employee?.profile_pic]);

  useEffect(() => {
    let isMounted = true;
    async function fetchCreatorAvatar() {
      if (issue.issueCreator && issue.issueCreator.profile_pic) {
        try {
          const url = await Storage.get(issue.issueCreator.profile_pic, {
            level: "public",
          });
          if (isMounted) setCreatorAvatarUrl(url);
        } catch {
          if (isMounted) setCreatorAvatarUrl(null);
        }
      } else {
        setCreatorAvatarUrl(null);
      }
    }
    fetchCreatorAvatar();
    return () => {
      isMounted = false;
    };
  }, [issue.issueCreator?.profile_pic]);

  return (
    <>
      <Card
        key={issue.issueKey}
        title={
          <div className="flex w-full justify-between">
            <div className="flex flex-col">
              <strong>
                {issue.issueKey}
                {issue.issueType && (
                  <Tag color="blue" className="ml-2 text-xs">
                    {issue.issueType}
                  </Tag>
                )}
                &nbsp;{" "}
                {issue?.versions?.length > 1 ? (
                  <Tag>{issue?.versions?.length}</Tag>
                ) : (
                  ""
                )}
              </strong>{" "}
              <small className="text-gray-500">
                {moment(issue.createdAt)?.format(DateFormatWithTime)}
              </small>
            </div>
            <div className="flex ">
              <Tooltip>
                <div>{renderTag(getScore())}</div>
              </Tooltip>
              <Tooltip title="View Jira Issue">
                <a
                  href={getJiraUrl()}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ExportOutlined />
                </a>
              </Tooltip>{" "}
            </div>
          </div>
        }
        bordered={true}
        className={`rounded-md min-w-96 text-wrap ${
          issue?.versions?.length > 1 ? "shadow-xl cursor-pointer" : ""
        } hover:scale-[1.01] transition-transform`}
        size="small"
      >
        <div className="flex justify-between">
          <div>
            <strong>Title:</strong> <span>{issue.issueSummary}</span>
          </div>
          <div>
            {issue.storyPoints && (
              <Tag color="green">{issue.storyPoints} pts</Tag>
            )}
          </div>
        </div>

        {/* Show status, priority, severity */}
        <div className="flex flex-wrap gap-2 mt-2">
          {issue.status && <Tag color="blue">Status: {issue.status}</Tag>}
          {issue.priority && (
            <Tag color="orange">Priority: {issue.priority}</Tag>
          )}
          {issue.severity && <Tag color="red">Severity: {issue.severity}</Tag>}
        </div>
        <div className="my-2">
          <hr className="border-t border-gray-200" />
        </div>

        <div>
          {issue.sprintName && (
            <span className="flex items-center">
              <strong>Sprint:</strong>&nbsp;
              {renderCheckBox("", issue.sprintName)}
            </span>
          )}
          {issue.boardName && renderCheckBox("Board", issue.boardName)}
          <div className="grid grid-cols-2 gap-2 mt-2">
            {hasQualityField("isDescriptionComplete") &&
              renderCheckBox(
                "Description Complete",
                getQualityField("isDescriptionComplete")
              )}
            {hasQualityField("isDescriptionClear") &&
              renderCheckBox(
                "Description Clear",
                getQualityField("isDescriptionClear")
              )}
            {hasQualityField("isActionable") &&
              renderCheckBox("Actionable", getQualityField("isActionable"))}
          </div>
          <div className="my-2">
            <hr className="border-t border-gray-200" />
          </div>
          <div className="mt-2 flex flex-col gap-y-1">
            {hasQualityField("missingElements") && (
              <div className={`flex flex-col bg-slate-100 ${boxClass}`}>
                <small>
                  <strong>Missing Elements:</strong>
                </small>{" "}
                {getQualityField("missingElements")}
              </div>
            )}

            {hasQualityField("justification") &&
              !!getQualityField("justification") && (
                <div className={`flex flex-col bg-slate-100 ${boxClass}`}>
                  <small>
                    <strong>Justification:</strong>
                  </small>{" "}
                  {getQualityField("justification")}
                </div>
              )}

            {issue.description && (
              <div className={`flex flex-col bg-slate-100 ${boxClass}`}>
                <small>
                  <strong>Description Preview:</strong>
                </small>{" "}
                <div className={"overflow-y-auto max-h-40"}>
                  {formatDescription(issue.description)}
                </div>
              </div>
            )}

            {issue.parentIssueKey && (
              <div className={`flex flex-col bg-slate-100 ${boxClass}`}>
                <small>
                  <strong>Parent Issue:</strong>
                </small>{" "}
                <a
                  // TODO: Need to change
                  href={`https://yorkdocs.atlassian.net/browse/${issue.parentIssueKey}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {issue.parentIssueKey}
                </a>
              </div>
            )}

            <div className={`flex flex-col ${boxClass}`}>
              <div className="flex flex-col gap-1">
                <small className="flex items-center gap-2">
                  <strong>Assigned To:</strong>
                  {issue.employee ? (
                    <>
                      {issue.employee.profile_pic ? (
                        <img
                          src={avatarUrl}
                          alt={issue.employee.first_name}
                          className="w-6 h-6 rounded-full object-cover inline-block mr-1"
                        />
                      ) : (
                        <span className="inline-block w-6 h-6 rounded-full bg-gray-300 mr-1" />
                      )}
                      <span>
                        {issue.employee.email ? (
                          <a
                            href={`/employee-view/${
                              issue.employee.email.split("@")[0]
                            }`}
                            className="text-primary-600 hover:underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {issue.employee.first_name}{" "}
                            {issue.employee.last_name}
                          </a>
                        ) : (
                          <>
                            {issue.employee.first_name}{" "}
                            {issue.employee.last_name}
                          </>
                        )}
                      </span>
                    </>
                  ) : (
                    <>{issue.employeeEmail}</>
                  )}
                </small>
                <small className="flex items-center gap-2">
                  <strong>Created By:</strong>
                  {issue.issueCreator ? (
                    <>
                      {issue.issueCreator.profile_pic ? (
                        <img
                          src={creatorAvatarUrl}
                          alt={issue.issueCreator.first_name}
                          className="w-6 h-6 rounded-full object-cover inline-block mr-1"
                        />
                      ) : (
                        <span className="inline-block w-6 h-6 rounded-full bg-gray-300 mr-1" />
                      )}
                      <span>
                        {issue.issueCreator.email ? (
                          <a
                            href={`/employee-view/${
                              issue.issueCreator.email.split("@")[0]
                            }`}
                            className="text-primary-600 hover:underline"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {issue.issueCreator.first_name}{" "}
                            {issue.issueCreator.last_name}
                          </a>
                        ) : (
                          <>
                            {issue.issueCreator.first_name}{" "}
                            {issue.issueCreator.last_name}
                          </>
                        )}
                      </span>
                    </>
                  ) : (
                    <>{issue.issueCreatorEmail}</>
                  )}
                </small>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Modal on card click is disabled */}
    </>
  );
};

export default JiraQualityCard;
