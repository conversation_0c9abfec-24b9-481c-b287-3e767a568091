import { Empty, Tag, Typography, Table } from "antd";
import CustomTable from "Commons/CustomTable";
import Loader from "Commons/Loader";
import InterviewFeedbackCard from "Pages/Analytics/InterviewsOverviewReport/components/InterviewFeedbackCard";
import { listInterviewsByInterviewerProfileAction } from "Pages/Profile/EmployeeProfile/Actions/employeeProfileActions";
import React, { useEffect, useState } from "react";
import moment from "moment";
import { DateFormatWithTime } from "utils/constants";
import { CheckCircleFilled, CloseCircleFilled } from "@ant-design/icons";
import { getBaseUrl } from "utils/commonMethods";

const EmployeeInterviewFeedback = ({ profileDetails }) => {
  //states
  const [interviewFeedback, setInterviewFeedback] = useState([]);
  const [loading, setLoading] = useState([]);

  // Helper function to render boolean icons
  const renderBooleanIcon = (value) => {
    let boolValue = value;
    if (typeof value === "string") {
      boolValue = value.toLowerCase() === "yes";
    }
    if (boolValue === true) {
      return <CheckCircleFilled className="text-green-500" />;
    } else if (boolValue === false) {
      return <CloseCircleFilled className="text-red-500" />;
    } else {
      return "-";
    }
  };

  // Helper function to calculate summary statistics
  const getSummaryData = () => {
    if (!interviewFeedback || interviewFeedback.length === 0) return {};

    const summary = {
      recommendedForFuture: { true: 0, total: 0 },
      confident: { true: 0, total: 0 },
      jobDescriptionConsidered: { true: 0, total: 0 },
      profileRelevance: { true: 0, total: 0 },
      companySummary: { true: 0, total: 0 },
      interviewerIntro: { true: 0, total: 0 },
      comfortLevel: { true: 0, total: 0 },
      overallExperience: { sum: 0, count: 0 },
      difficultyLevel: { sum: 0, count: 0 },
    };

    interviewFeedback.forEach((interview) => {
      // AI Feedback fields
      try {
        if (interview.aiFeedback) {
          const ai =
            typeof interview.aiFeedback === "string"
              ? JSON.parse(interview.aiFeedback)
              : interview.aiFeedback;

          if (ai?.interviewer?.["Recommended for Future"] !== undefined) {
            summary.recommendedForFuture.total++;
            if (ai.interviewer["Recommended for Future"] === true) {
              summary.recommendedForFuture.true++;
            }
          }

          if (ai?.interviewer?.["Confident"] !== undefined) {
            summary.confident.total++;
            if (ai.interviewer["Confident"] === true) {
              summary.confident.true++;
            }
          }

          if (ai?.interviewer?.["Job Description Considered"] !== undefined) {
            summary.jobDescriptionConsidered.total++;
            if (ai.interviewer["Job Description Considered"] === true) {
              summary.jobDescriptionConsidered.true++;
            }
          }
        }
      } catch {}

      // Employee feedback fields
      try {
        if (interview.employee_feedback) {
          const fb =
            typeof interview.employee_feedback === "string"
              ? JSON.parse(interview.employee_feedback)
              : interview.employee_feedback;

          if (fb?.profileRelevance !== undefined) {
            summary.profileRelevance.total++;
            if (fb.profileRelevance === true) {
              summary.profileRelevance.true++;
            }
          }

          if (fb?.companySummary !== undefined) {
            summary.companySummary.total++;
            if (fb.companySummary === true) {
              summary.companySummary.true++;
            }
          }

          if (fb?.interviewerIntro !== undefined) {
            summary.interviewerIntro.total++;
            if (fb.interviewerIntro === true) {
              summary.interviewerIntro.true++;
            }
          }

          if (fb?.comfortLevel !== undefined) {
            summary.comfortLevel.total++;
            if (fb.comfortLevel === true) {
              summary.comfortLevel.true++;
            }
          }

          if (typeof fb?.overallExperience === "number") {
            summary.overallExperience.sum += fb.overallExperience;
            summary.overallExperience.count++;
          }

          if (typeof fb?.difficultyLevel === "number") {
            summary.difficultyLevel.sum += fb.difficultyLevel;
            summary.difficultyLevel.count++;
          }
        }
      } catch {}
    });

    return summary;
  };

  // Render percentage for boolean values
  const renderBooleanSummary = (field) => {
    let trueCount = 0;
    let total = 0;
    filteredInterviewFeedback.forEach((interview) => {
      let value;
      if (
        field === "recommendedForFuture" ||
        field === "confident" ||
        field === "jobDescriptionConsidered"
      ) {
        let ai = interview.aiFeedback;
        if (typeof ai === "string") {
          try {
            ai = JSON.parse(ai);
          } catch {
            ai = null;
          }
        }
        if (
          ai &&
          ai.interviewer &&
          ai.interviewer[fieldMap[field]] !== undefined
        ) {
          total++;
          if (ai.interviewer[fieldMap[field]] === true) trueCount++;
        }
      } else if (field === "comfortLevel") {
        let fb = interview.employee_feedback;
        if (typeof fb === "string") {
          try {
            fb = JSON.parse(fb);
          } catch {
            fb = null;
          }
        }
        if (
          fb &&
          fb.comfortLevel !== undefined &&
          fb.comfortLevel !== null &&
          fb.comfortLevel !== ""
        ) {
          let val = fb.comfortLevel;
          if (typeof val === "string") {
            val = val.trim().toLowerCase();
            if (val === "yes") {
              trueCount++;
              total++;
            } else {
              total++;
            }
          } else if (typeof val === "boolean") {
            if (val === true) trueCount++;
            total++;
          }
        }
      } else {
        let fb = interview.employee_feedback;
        if (typeof fb === "string") {
          try {
            fb = JSON.parse(fb);
          } catch {
            fb = null;
          }
        }
        if (fb && fb[field] !== undefined) {
          total++;
          if (fb[field] === true) trueCount++;
        }
      }
    });
    if (total === 0) return "-";
    const percent = Math.round((trueCount / total) * 100);
    let iconValue;
    if ((trueCount) => total - trueCount) {
      iconValue = true;
    } else if (trueCount < total - trueCount) {
      iconValue = false;
    } else {
      iconValue = null;
    }
    return (
      <span>
        {renderBooleanIcon(iconValue)} {percent}% ({trueCount}/{total})
      </span>
    );
  };

  // Helper for mapping field names
  const fieldMap = {
    recommendedForFuture: "Recommended for Future",
    confident: "Confident",
    jobDescriptionConsidered: "Job Description Considered",
  };

  // Render average for ratings
  const renderRatingSummary = (field) => {
    let sum = 0;
    let count = 0;
    filteredInterviewFeedback.forEach((interview) => {
      let fb = interview.employee_feedback;
      if (typeof fb === "string") {
        try {
          fb = JSON.parse(fb);
        } catch {
          fb = null;
        }
      }
      if (fb && typeof fb[field] === "number") {
        sum += fb[field];
        count++;
      }
    });
    if (count === 0) return "-";
    const avg = (sum / count).toFixed(2);
    let color = "red";
    if (avg >= 4) color = "green";
    else if (avg >= 3) color = "gold";
    return <Tag color={color}>{avg} / 5</Tag>;
  };

  // columns for interview feedback table
  const columns = [
    {
      title: "Candidate Name",
      dataIndex: "candiate",
      render: (candiate) =>
        `${candiate?.first_name || ""} ${candiate?.last_name || ""}`,
    },
    {
      title: "Interview Title",
      dataIndex: "title",
      render: (title, record) => {
        const displayTitle = record?.candiate?.position?.title || title || "-";
        const interviewId = record?.id;
        if (interviewId) {
          return (
            <a
              href={`${getBaseUrl()}/hiring/interviews/${interviewId}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              {displayTitle}
            </a>
          );
        }
        return displayTitle;
      },
    },
    {
      title: "Recommended for Future",
      dataIndex: "aiFeedback",
      render: (aiFeedback) => {
        let value = null;
        try {
          if (aiFeedback) {
            const ai = JSON.parse(aiFeedback);
            value = ai?.interviewer?.["Recommended for Future"];
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Confident",
      dataIndex: "aiFeedback",
      render: (aiFeedback) => {
        let value = null;
        try {
          if (aiFeedback) {
            const ai = JSON.parse(aiFeedback);
            value = ai?.interviewer?.["Confident"];
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Job Description Considered",
      dataIndex: "aiFeedback",
      render: (aiFeedback) => {
        let value = null;
        try {
          if (aiFeedback) {
            const ai = JSON.parse(aiFeedback);
            value = ai?.interviewer?.["Job Description Considered"];
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Profile Relevance",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.profileRelevance;
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Company Summary",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.companySummary;
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Interviewer Intro",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.interviewerIntro;
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Comfort Level",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.comfortLevel;
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Overall Experience",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.overallExperience;
          }
        } catch {}
        if (value === undefined || value === null || value === "") return "-";
        let color = "red";
        if (value >= 4) color = "green";
        else if (value >= 3) color = "gold";
        return <Tag color={color}>{value} / 5</Tag>;
      },
    },
    {
      title: "Difficulty Level",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.difficultyLevel;
          }
        } catch {}
        if (value === undefined || value === null || value === "") return "-";
        let color = "red";
        if (value >= 4) color = "green";
        else if (value >= 3) color = "gold";
        return <Tag color={color}>{value} / 5</Tag>;
      },
    },
    {
      title: "Interview Time",
      dataIndex: "selected_time",
      render: (time) => moment(time, "X").format(DateFormatWithTime),
    },
  ];

  //useEffect
  useEffect(() => {
    fetchInterviewFeedback();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Filter only completed interviews
  const completedInterviewFeedback = interviewFeedback.filter(
    (item) => item.status === "Completed"
  );

  // Filter out rows where all columns are empty
  const isRowNotEmpty = (item) => {
    // Check candidate name or title
    if (item.candiate?.first_name || item.candiate?.last_name || item.title)
      return true;
    // Check interview time or status
    if (item.selected_time || item.status) return true;
    // Check aiFeedback fields
    let ai = item.aiFeedback;
    if (typeof ai === "string") {
      try {
        ai = JSON.parse(ai);
      } catch {
        ai = null;
      }
    }
    if (
      ai &&
      ai.interviewer &&
      (ai.interviewer["Recommended for Future"] !== undefined ||
        ai.interviewer["Confident"] !== undefined ||
        ai.interviewer["Job Description Considered"] !== undefined)
    )
      return true;
    // Check employee_feedback fields
    let fb = item.employee_feedback;
    if (typeof fb === "string") {
      try {
        fb = JSON.parse(fb);
      } catch {
        fb = null;
      }
    }
    if (
      fb &&
      (fb.profileRelevance !== undefined ||
        fb.companySummary !== undefined ||
        fb.interviewerIntro !== undefined ||
        fb.comfortLevel !== undefined ||
        fb.overallExperience !== undefined ||
        fb.difficultyLevel !== undefined)
    )
      return true;
    return false;
  };

  const filteredInterviewFeedback =
    completedInterviewFeedback.filter(isRowNotEmpty);

  //function
  const fetchInterviewFeedback = async () => {
    try {
      setLoading(true);
      let responseForInterviewFeedback =
        await listInterviewsByInterviewerProfileAction(profileDetails?.email);
      console.log("responseForInterviewFeedback", responseForInterviewFeedback);
      setInterviewFeedback(responseForInterviewFeedback);
    } catch (err) {
      console.error("Error fetching interview feedback:", err);
    } finally {
      setLoading(false);
    }
  };
  if (loading) {
    return <Loader title={"Please wait while we fetch interview feedback"} />;
  }

  return (
    <>
      <Typography.Title level={4} className="mb-4 !text-primary-500">
        Interview Feedback
      </Typography.Title>
      <CustomTable
        title="Interview Feedback"
        columns={columns}
        dataSource={[
          {
            id: "summary-row",
            isSummaryRow: true,
          },
          ...filteredInterviewFeedback,
        ]}
        loading={loading}
        local={{
          emptyText: <Empty description="No interview feedback found" />,
        }}
        pagination={false}
        rowKey="id"
        scroll={{ x: 1000 }}
        rowClassName={(record) =>
          record.isSummaryRow ? "bg-gray-100 font-medium summary-row" : ""
        }
        expandable={{
          expandRowByClick: true,
          rowExpandable: (record) => !record.isSummaryRow,
          expandedRowRender: (row) => {
            if (row.isSummaryRow) return null;
            return (
              <div style={{ maxWidth: "500px", minWidth: "100%" }}>
                <InterviewFeedbackCard
                  aiFeedback={{
                    ...(row.aiFeedback
                      ? typeof row.aiFeedback === "string"
                        ? JSON.parse(row.aiFeedback)
                        : row.aiFeedback
                      : {}),
                    candidateFeedback: row.employee_feedback
                      ? typeof row.employee_feedback === "string"
                        ? JSON.parse(row.employee_feedback)
                        : row.employee_feedback
                      : null,
                  }}
                  candidateFeedback={
                    row.candiate_feedback
                      ? typeof row.candiate_feedback === "string"
                        ? JSON.parse(row.candiate_feedback)
                        : row.candiate_feedback
                      : null
                  }
                />
              </div>
            );
          },
        }}
        components={{
          body: {
            row: (props) => {
              if (props.children[0]?.props?.record?.isSummaryRow) {
                const summaryData = getSummaryData();
                return (
                  <tr className="bg-gray-100 font-medium">
                    <td></td>
                    <td colSpan={2} className="mr-4">
                      <div className="flex flex-col flex-wrap word-break-all font-bold">
                        <span className="font-bold">Summary:</span>
                        <span className="font-medium">
                          ({filteredInterviewFeedback.length}{" "}
                          {filteredInterviewFeedback.length === 1
                            ? "Candidate"
                            : "Candidates"}
                          )
                        </span>
                      </div>
                    </td>
                    <td>{renderBooleanSummary("recommendedForFuture")}</td>
                    <td>{renderBooleanSummary("confident")}</td>
                    <td>{renderBooleanSummary("jobDescriptionConsidered")}</td>
                    <td>{renderBooleanSummary("profileRelevance")}</td>
                    <td>{renderBooleanSummary("companySummary")}</td>
                    <td>{renderBooleanSummary("interviewerIntro")}</td>
                    <td>{renderBooleanSummary("comfortLevel")}</td>
                    <td>{renderRatingSummary("overallExperience")}</td>
                    <td>{renderRatingSummary("difficultyLevel")}</td>
                    <td colSpan={2}></td>
                  </tr>
                );
              }
              return <tr {...props} />;
            },
          },
        }}
      />
    </>
  );
};

export default EmployeeInterviewFeedback;
