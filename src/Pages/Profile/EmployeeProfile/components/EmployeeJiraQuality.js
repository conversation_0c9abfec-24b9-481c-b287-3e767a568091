import { Typography } from "antd";
import { getJiraEventsByCreatorForProfileAction } from "Pages/Profile/EmployeeProfile/Actions/employeeProfileActions";
import React, { useEffect, useRef, useState } from "react";
import JiraQualityCard from "Pages/Project/Components/JiraQualityCard";
import Loader from "Commons/Loader";

const EmployeeJiraQuality = ({ profileDetails }) => {
  const [jiraIssuesList, setJiraIssuesList] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [rowIssues, setRowIssues] = useState([]);
  const [nextToken, setNextToken] = useState(null);
  const isFetching = useRef(false);
  const scrollTimeout = useRef(null);
  //func
  const fetchEmployeeJiraQualityDetails = async () => {
    if (isFetching.current) return;
    isFetching.current = true;
    setIsLoading(true);

    try {
      //NOTE: Field needs to be added in the query
      const res = await getJiraEventsByCreatorForProfileAction(
        profileDetails?.email,
        nextToken
      );
      setRowIssues((prevList) => [...prevList, ...(res?.items || [])]);
      setNextToken(res?.nextToken);
      setHasMore(!!res?.nextToken);
    } catch (error) {
      console.error("Error fetching Jira Quality details:", error);
      setHasMore(false);
    } finally {
      isFetching.current = false;
      setIsLoading(false);
    }
  };

  //useEffect
  useEffect(() => {
    fetchEmployeeJiraQualityDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    // Remove duplicate issues
    const uniqueIssues = rowIssues?.reduce((acc, current) => {
      const x = acc.find((item) => item.issueKey === current.issueKey);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, []);

    // Group data by issueKey and sort by createdAt (latest first)
    const groupedData = Object.values(
      uniqueIssues?.reduce((acc, item) => {
        let descQuality = item?.descriptionQualityAI;
        if (descQuality && typeof descQuality === "string") {
          try {
            descQuality = JSON.parse(descQuality);
          } catch (e) {
            console.error("Error parsing description quality:", e);
          }
        }

        item = { ...item, descriptionQualityAI: descQuality };
        const key = item.issueKey;
        if (!acc[key]) {
          acc[key] = { ...item, versions: [item] };
        } else {
          acc[key].versions.push(item);
        }
        return acc;
      }, {})
    ).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    setJiraIssuesList(groupedData);
  }, [rowIssues]);

  //Handle Infinite Scroll
  const handleScroll = (e) => {
    const container = e.target;
    if (scrollTimeout.current) {
      clearTimeout(scrollTimeout.current);
    }
    scrollTimeout.current = setTimeout(() => {
      if (
        container.scrollLeft + container.clientWidth >=
          container.scrollWidth - 50 &&
        hasMore &&
        !isFetching.current
      ) {
        fetchEmployeeJiraQualityDetails();
      }
    }, 150); // Debounce interval
  };

  // Helper: Calculate summary stats
  const getJiraSummary = () => {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    let totalStories = jiraIssuesList.length;
    let lastWeekStories = jiraIssuesList.filter((issue) => {
      const created = new Date(issue.createdAt);
      return created >= oneWeekAgo && created <= now;
    }).length;
    // Average score: try to use descriptionQualityAI.score or similar if available
    let scoreSum = 0;
    let scoreCount = 0;
    let storyPointsCount = 0;
    let completedStories = 0;
    let uniqueSprints = new Set();
    let uniqueProjects = new Set();
    jiraIssuesList.forEach((issue) => {
      // Score
      let score = null;
      if (
        issue.descriptionQualityAI &&
        typeof issue.descriptionQualityAI === "object"
      ) {
        score = issue.descriptionQualityAI.score;
      } else if (
        issue.descriptionQualityAI &&
        typeof issue.descriptionQualityAI === "number"
      ) {
        score = issue.descriptionQualityAI;
      }
      if (typeof score === "number") {
        scoreSum += score;
        scoreCount++;
      }
      // Story Points
      if (typeof issue.storyPoints === "number") {
        storyPointsCount++;
      }
      // Completed Stories
      if (issue.status && ["Done", "Completed"].includes(issue.status)) {
        completedStories++;
      }
      // Unique Sprints
      if (issue.sprintName) {
        uniqueSprints.add(issue.sprintName);
      }
      // Unique Projects
      if (issue.projectId) {
        uniqueProjects.add(issue.projectId);
      }
    });
    let avgScore = scoreCount > 0 ? (scoreSum / scoreCount).toFixed(2) : "-";
    let avgStoryPoints =
      storyPointsCount > 0
        ? (storyPointsCount / storyPointsCount).toFixed(2)
        : "-";
    return {
      totalStories,
      lastWeekStories,
      avgScore,
      avgStoryPoints,
      completedStories,
      uniqueSprints: uniqueSprints.size,
      uniqueProjects: uniqueProjects.size,
    };
  };

  const summary = getJiraSummary();

  return (
    <>
      <Typography.Title level={4} className="mb-4 !text-primary-500">
        Jira Quality
      </Typography.Title>

      {/* Summary Boxes */}
      <div className="flex flex-wrap gap-4 mb-4">
        <div className="bg-white border rounded-lg shadow p-4 min-w-[180px] text-center">
          <div className="text-gray-500 text-xs mb-1">Total Stories</div>
          <div className="text-2xl font-bold text-primary-500">
            {summary.totalStories}
          </div>
        </div>
        <div className="bg-white border rounded-lg shadow p-4 min-w-[180px] text-center">
          <div className="text-gray-500 text-xs mb-1">Stories in Last Week</div>
          <div className="text-2xl font-bold text-primary-500">
            {summary.lastWeekStories}
          </div>
        </div>
        <div className="bg-white border rounded-lg shadow p-4 min-w-[180px] text-center">
          <div className="text-gray-500 text-xs mb-1">Average Score</div>
          <div className="text-2xl font-bold text-primary-500">
            {summary.avgScore}
          </div>
        </div>
        <div className="bg-white border rounded-lg shadow p-4 min-w-[180px] text-center">
          <div className="text-gray-500 text-xs mb-1">Average Story Points</div>
          <div className="text-2xl font-bold text-primary-500">
            {summary.avgStoryPoints}
          </div>
        </div>
        <div className="bg-white border rounded-lg shadow p-4 min-w-[180px] text-center">
          <div className="text-gray-500 text-xs mb-1">Completed Stories</div>
          <div className="text-2xl font-bold text-primary-500">
            {summary.completedStories}
          </div>
        </div>
        <div className="bg-white border rounded-lg shadow p-4 min-w-[180px] text-center">
          <div className="text-gray-500 text-xs mb-1">Unique Sprints</div>
          <div className="text-2xl font-bold text-primary-500">
            {summary.uniqueSprints}
          </div>
        </div>
        <div className="bg-white border rounded-lg shadow p-4 min-w-[180px] text-center">
          <div className="text-gray-500 text-xs mb-1">Unique Projects</div>
          <div className="text-2xl font-bold text-primary-500">
            {summary.uniqueProjects}
          </div>
        </div>
      </div>

      {isLoading && jiraIssuesList.length === 0 ? (
        <Loader title={"Please wait while we fetch Jira quality data..."} />
      ) : (
        <div
          className="overflow-x-auto whitespace-nowrap p-4"
          onScroll={handleScroll}
        >
          <div className="flex gap-4">
            {jiraIssuesList?.map((issue, index) => (
              <JiraQualityCard key={index} issue={issue} type="EMAIL" />
            ))}
            {isLoading && hasMore && (
              <div className="flex justify-center items-center min-w-96">
                <Loader />
              </div>
            )}
            {!hasMore && jiraIssuesList.length > 0 && (
              <div className="text-center text-gray-500 my-auto h-auto">
                No more issues
              </div>
            )}
            {!isLoading && jiraIssuesList.length === 0 && (
              <div className="text-center text-gray-500 my-4 w-full">
                No Jira quality data found
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default EmployeeJiraQuality;
