/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState } from "react";
import { Editor } from "Commons/Editor/Editor";

import {
  Button,
  Checkbox,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Slider,
  Space,
} from "antd";
import moment from "moment";

import { notUndefined, SelectFieldFilterOption } from "utils/commonMethods";

import { AWSDATETYPE, FormRequiredRule } from "utils/constants";
import {
  createHiringPositionSkillsAction,
  createUpdateHiringPosition,
  getAllHiringOptions,
  updateHiringPositionSkills,
} from "../../Actions/HiringActions";
import {
  getCurrentUserData,
  isHr,
  isRecruiter,
  isUnitTeamLeader,
} from "store/slices/loginSlice";
import { useSelector } from "react-redux";
import { PlusOutlined } from "@ant-design/icons";
import { createNewSkill } from "Pages/Profile/ProfileActions";
import { isEmpty } from "lodash";
import { Content } from "antd/lib/layout/layout";
import { JobPostStatuses } from "../../Constants/Constants";
import { ListEmployeesCustomAction } from "../../../../utils/Actions";
import { useLocation, useNavigate } from "react-router-dom";
import RenderEmployeeSelect from "AtomicComponents/RenderEmployeeSelect";

const CreateUpdateHiringPosition = () => {
  const [editorContent, setEditorContent] = useState("");
  const location = useLocation();
  const navigate = useNavigate();

  const isEditing = location?.state?.editMode;
  const JobPostToEdit = location?.state?.position
    ? JSON.parse(location?.state?.position)
    : {};

  const [form] = Form.useForm();
  const [isLoading, setisLoading] = useState(false);
  const [initLoading, setinitLoading] = useState(false);
  const [disableAdding, setDisableAdding] = useState(true);
  const [employeeData, setEmployeeData] = useState(null);
  const [newSkill, setNewSkill] = useState("");
  const [positionMap, setPositionMap] = useState({});
  const inputRef = useRef(null);
  const IsHrLogin = useSelector(isHr);
  const isARecruiter = useSelector(isRecruiter);
  const isUserPUL = useSelector(isUnitTeamLeader);
  const currentUser = useSelector(getCurrentUserData);

  const [SelectFieldData, setSelectFieldData] = useState({});

  useEffect(() => {
    if (isEditing) {
      setEditorContent(JobPostToEdit?.description);
      let item = JobPostToEdit;

      let updatedFormData = {
        id: item?.id,
        title: item?.title,
        hiringPositionPositionId: item?.position?.id,
        hiringPositionPotential_squadId: item?.potential_squad?.name,
        hiringPositionGuildId: item?.guild?.name,
        skills: item?.skills?.items?.map((i) => i?.skillID),
        required_experience: item?.required_experience,
        deadline: item?.deadline ? moment(item?.deadline) : null,
        potential_project: item?.potential_project,
        hiringPositionOwnerId: item?.owner ? `${item?.owner?.email}` : null,
        status: item?.status,
        priority: item?.priority,
        isPracticalNeeded: item?.isPracticalNeeded,
      };
      form.setFieldsValue(updatedFormData);
    }
    init();
  }, [location]);

  const init = async () => {
    setinitLoading(true);
    try {
      let hiringOptions;

      hiringOptions = await getAllHiringOptions(
        IsHrLogin,
        isARecruiter,
        isUserPUL
      );

      let filter = {
        active: { ne: false },
      };
      const employeeDetails = await ListEmployeesCustomAction(filter);
      setEmployeeData(employeeDetails);

      setSelectFieldData(hiringOptions);
      if (hiringOptions.positions) {
        const positionIdMap = {};
        hiringOptions.positions.forEach((position) => {
          positionIdMap[position.id] = position.name;
        });
        setPositionMap(positionIdMap);
      }
    } catch (error) {
      console.error("Error initializing form:", error);
      message.error("Failed to load form data");
    } finally {
      setinitLoading(false);
    }
  };

  const htmlToPlainText = (html) => {
    const doc = new DOMParser().parseFromString(html, "text/html");
    const textContent = doc.body.textContent || "";
    return textContent.trim();
  };

  const handleCreateHiringPosition = async (formData) => {
    setisLoading(true);
    var createUpdateResponse = {};
    try {
      const plainTextContent = htmlToPlainText(editorContent);
      if (
        !editorContent ||
        !plainTextContent ||
        plainTextContent.trim().length === 0
      ) {
        message.error("Job Description is Required");
        setisLoading(false);
        return;
      }
      formData["deadline"] = formData["deadline"]
        ? moment(formData["deadline"]).format(AWSDATETYPE)
        : undefined;

      let positionOpen = formData?.status === "Closed" ? false : true;
      let skills = formData?.skills;

      delete formData?.skills;
      delete formData?.SMEs;
      if (!notUndefined(formData?.hiringPositionOwnerId)) {
        formData = {
          ...formData,
          hiringPositionOwnerId: currentUser.email,
        };
      }
      if (isEditing) {
        let inputData = {
          id: JobPostToEdit?.id,
          description: editorContent,
          ...formData,
          open: positionOpen,
          ...(!positionOpen && { benchEmployeeSuggestion: null }), // If position is closed then remove the suggestion list
        };
        let HiringPosition = await createUpdateHiringPosition(inputData);
        createUpdateResponse = HiringPosition;
        await updateHiringPositionSkills(skills, HiringPosition?.id);
      } else {
        formData = {
          ...formData,
          description: editorContent,
          open: positionOpen,
        };
        let HiringPosition = await createUpdateHiringPosition(formData);

        createUpdateResponse = HiringPosition;
        await Promise.all(
          skills?.map((item) =>
            createHiringPositionSkillsAction({
              hiringPositionID: HiringPosition?.id,
              skillID: item,
            })
          )
        );
      }
    } catch (error) {
      message.error("Something Went Wrong");
      setisLoading(false);
    } finally {
      setisLoading(false);

      onComplete(createUpdateResponse);
    }
  };

  const onComplete = (data) => {
    navigate("/hiring/job-positions");
  };

  // Handle Empty values
  const isNewSkillEmpty = (value) => {
    return isEmpty(value.replaceAll(/\s/g, ""));
  };

  // Add Skills
  // Handle Changes in Skill
  const onSkillNameChange = (event) => {
    let newSkillValue = event.target.value;
    let isEmptySkill = isNewSkillEmpty(newSkillValue);
    const isSkillAvailable = SelectFieldData["skills"].find(
      (item) => item.name === newSkillValue
    );
    isSkillAvailable || isEmptySkill
      ? setDisableAdding(true)
      : setDisableAdding(false);
    setNewSkill(newSkillValue);
  };

  // Handle Add Skill
  const addSkill = async () => {
    try {
      setinitLoading(true);
      await createNewSkill(newSkill);
      setNewSkill("");
      setinitLoading(false);
      await init(); // Call the init function after addSkill is completed
    } catch (error) {
      setinitLoading(false);
      console.error("Error adding skill:", error);
    }
  };

  return (
    <>
      <Row className="w-full" style={{ height: "83vh" }}>
        <Content style={{ maxWidth: "346px" }}>
          <div
            style={{
              width: "346px",
              padding: "24px 24px 50px 0px",
              overflowX: "auto",
            }}
          >
            <Form
              layout="vertical"
              className="notification-form"
              requiredMark={"optional"}
              onFinish={handleCreateHiringPosition}
              disabled={initLoading}
              form={form}
            >
              <Form.Item
                label="Title"
                title="title"
                name="title"
                rules={FormRequiredRule}
              >
                <Input placeholder="eg: Cloud Engineer" />
              </Form.Item>
              <Form.Item
                name="isPracticalNeeded"
                valuePropName="checked"
                label={null}
              >
                <Checkbox>Is Coding Round needed for this Job Post?</Checkbox>
              </Form.Item>

              <Form.Item
                label="Designation"
                title="positions"
                name="hiringPositionPositionId"
                rules={FormRequiredRule}
              >
                <Select
                  placeholder="Select Position"
                  showSearch
                  allowClear
                  filterOption={(input, option) => {
                    const optionValue = positionMap[option?.value] || "";
                    return optionValue
                      .toLowerCase()
                      .includes(input.toLowerCase());
                  }}
                  loading={initLoading}
                >
                  {SelectFieldData["positions"]
                    ?.sort((a, b) => a.name.localeCompare(b.name))
                    .map((item) => (
                      <Select.Option value={item?.id} key={item?.id}>
                        {item?.name}
                        {item?.overall_level && `(OL: ${item?.overall_level})`}
                        {item?.level && `(L: ${item?.level})`}
                      </Select.Option>
                    ))}
                </Select>
              </Form.Item>
              <Form.Item
                label="Skills"
                title="skills"
                name="skills"
                rules={FormRequiredRule}
              >
                <Select
                  placeholder="Select Skills"
                  showSearch
                  allowClear
                  filterOption={SelectFieldFilterOption}
                  loading={initLoading}
                  mode="multiple"
                  dropdownRender={(menu) => (
                    <>
                      {menu}
                      <Divider
                        style={{
                          margin: "8px 0",
                        }}
                      />

                      <Space
                        style={{
                          padding: "0 8px 4px",
                        }}
                      >
                        <Input
                          placeholder="Enter skill name"
                          ref={inputRef}
                          value={newSkill}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              e.stopPropagation();
                              e.preventDefault();
                              addSkill();
                            }
                          }}
                          onChange={onSkillNameChange}
                        />
                        <Button
                          onClick={addSkill}
                          disabled={disableAdding}
                          loading={initLoading}
                          icon={<PlusOutlined />}
                        >
                          Add Skill
                        </Button>
                      </Space>
                    </>
                  )}
                >
                  {SelectFieldData["skills"]?.map((item) => (
                    <Select.Option value={item?.name} key={item?.name}>
                      {item?.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label="Required Experience"
                title="Required Experience"
                name="required_experience"
                rules={FormRequiredRule}
              >
                <InputNumber
                  placeholder="Enter Required Experience"
                  className="w-full"
                />
              </Form.Item>
              <Form.Item
                label="Priority"
                title="Priority"
                name="priority"
                rules={FormRequiredRule}
              >
                <Slider
                  min={0}
                  max={10}
                  defaultValue={1}
                  tipFormatter={(value) =>
                    value > 0 ? `${value} 🔥` : "No Priority"
                  }
                  marks={{
                    0: "🔥",
                    5: "🔥🔥",
                    10: (
                      <div className="flex">
                        <div>🔥</div>
                        <div>🔥</div>
                        <div>🔥</div>
                      </div>
                    ),
                  }}
                />
              </Form.Item>

              <Form.Item
                label="Potential Start Date"
                title="deadline"
                name="deadline"
                rules={FormRequiredRule}
              >
                <DatePicker
                  placeholder="Select Deadline date"
                  className="w-full"
                />
              </Form.Item>

              <Form.Item
                label="Potential Squad"
                title="Potential Squad"
                name="hiringPositionPotential_squadId"
              >
                <Select
                  placeholder="Select Potential Squad"
                  showSearch
                  allowClear
                  filterOption={SelectFieldFilterOption}
                  loading={initLoading}
                >
                  {SelectFieldData["squads"]?.map((item) => (
                    <Select.Option value={item?.name} key={item?.name}>
                      {item?.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                label="Owner Name"
                title="Owner Name"
                name="hiringPositionOwnerId"
              >
                <RenderEmployeeSelect
                  employeesList={employeeData || []}
                  placeholder="Select Owner Name"
                  showSearch
                  allowClear
                  disabled={initLoading}
                />
              </Form.Item>

              <Form.Item
                label="Guild"
                title="guild"
                name="hiringPositionGuildId"
              >
                <Select
                  placeholder="Select Guild"
                  showSearch
                  allowClear
                  filterOption={SelectFieldFilterOption}
                  loading={initLoading}
                >
                  {SelectFieldData["guilds"]?.map((item) => (
                    <Select.Option value={item?.name} key={item?.name}>
                      {item?.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label="Potential Project"
                title="potential_project"
                name="potential_project"
                // rules={FormRequiredRule}
              >
                <Input
                  placeholder="Enter Potential Project"
                  className="w-full"
                />
              </Form.Item>

              <Form.Item
                label="Status"
                title="status"
                name="status"
                rules={FormRequiredRule}
                style={{ paddingBottom: "16px" }}
              >
                <Select
                  placeholder="Select Status"
                  showSearch
                  allowClear
                  filterOption={SelectFieldFilterOption}
                  loading={initLoading}
                >
                  {JobPostStatuses?.map((value) => (
                    <Select.Option value={value} key={value}>
                      {value}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              <div className="button-create-position-form">
                <Button htmlType="reset" className="mr-1" onClick={onComplete}>
                  Cancel
                </Button>
                <Button
                  htmlType="submit"
                  type="primary"
                  loading={isLoading}
                  disabled={initLoading || isLoading}
                >
                  {isEditing ? "Update" : "Create"} Job Position
                </Button>
              </div>
            </Form>
          </div>
        </Content>
        <Divider
          type="vertical"
          style={{
            height: "100%",
            margin: "0px 0px 0px 0px",
            position: "sticky",
            top: "40px",
            zIndex: 2,
          }}
        />

        <div style={{ flex: 1, padding: "26px 0px 0px 30px" }}>
          <Editor
            editorContent={editorContent}
            setEditorContent={setEditorContent}
          />
        </div>
      </Row>
    </>
  );
};

export default CreateUpdateHiringPosition;
