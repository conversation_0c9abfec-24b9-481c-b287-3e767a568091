/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import {
  But<PERSON>,
  Col,
  message,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Switch,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import {
  isExecutive,
  isHr,
  isRecruiter,
  isUnitTeamLeader,
} from "store/slices/loginSlice";
import {
  CandidateCountReportPerJobPosition,
  CandidateStatusColorMap,
  JobPostStatuses,
  KanbanBoardStatuses,
} from "../../Constants/Constants";
import moment from "moment";
import {
  AppstoreAddOutlined,
  CloseOutlined,
  CodeOutlined,
  EditOutlined,
  Loading3QuartersOutlined,
  ReloadOutlined,
  TableOutlined,
} from "@ant-design/icons";

import CustomTable from "Commons/CustomTable";
import { useNavigate } from "react-router-dom";
import { KanbanBoard } from "Commons/Kanban/KanbanBoard";
import JobCard from "Pages/Hiring/Components/KanbanCard/JobCard";
import {
  getCandidatesOverviewFromReducer,
  setCandidatesOverview,
} from "store/slices/hiringSlice";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import { notUndefined } from "utils/commonMethods";
import { API, Storage } from "aws-amplify";
import { DateFormatWithTime } from "utils/constants";
import {
  createUpdateHiringPosition,
  listJobPostAction,
} from "Pages/Hiring/Actions/HiringActions";

function HiringPositions(props) {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    userData: { email },
  } = useSelector((state) => state?.loginReducer);

  const IsHrLogin = useSelector(isHr);
  const isARecruiter = useSelector(isRecruiter);
  const isUserExecutive = useSelector(isExecutive);
  const isUserPUL = useSelector(isUnitTeamLeader);

  const isPowerUser = isUserExecutive || IsHrLogin || isARecruiter || isUserPUL;

  const [skeletonLoader, setskeletonLoader] = useState(false);
  const [ShowClosedPositions, setShowClosedPositions] = useState(false);

  const handleEditPosition = (position) => {
    navigate("/hiring/job-positions/edit", {
      state: {
        position: JSON.stringify(position),
        editMode: true,
      },
    });
  };

  const handleAddPosition = () => {
    navigate("/hiring/job-positions/add", {
      state: {
        editMode: false,
      },
    });
  };

  const [isKanbanView, setIsKanbanView] = useState(false);

  const handleEditStatus = (position) => {
    Modal.confirm({
      title: "Change Job Post Status",
      content: (
        <Select
          style={{ width: "100%" }}
          defaultValue={position.status}
          onChange={(value) => handleStatusChange(position, value)}
        >
          {JobPostStatuses.map((status) => (
            <Select.Option key={status} value={status}>
              {status}
            </Select.Option>
          ))}
        </Select>
      ),
      onOk() {},
      onCancel() {},
      okButtonProps: { style: { display: "none" } },
      cancelText: "Close",
    });
  };

  const handleStatusChange = async (position, newStatus) => {
    try {
      setskeletonLoader(true);
      let positionOpen = newStatus !== "Closed";
      const inputData = {
        id: position?.id,
        open: positionOpen,
        status: newStatus,
      };
      await createUpdateHiringPosition(inputData);
      message.success("Job post status updated successfully");
    } catch (error) {
      console.error("Error updating job post status:", error);
      message.error("Failed to update job post status");
    } finally {
      setskeletonLoader(false);
    }
  };

  const columns = [
    {
      title: "Title",
      dataIndex: "title",
      width: "12%",
      className: "min-w-[150px]",
      render: (title, position) => {
        return (
          <>
            <div
              className="cursor-pointer text-primary-500 flex gap-2"
              onClick={() => goToHiringDetails(position?.id)}
            >
              {title}{" "}
              {position?.isPracticalNeeded ? (
                <Tooltip title="Coding Round Needed">
                  <CodeOutlined className="text-gray-500" />
                </Tooltip>
              ) : null}
            </div>
            {Array.from({ length: Math.max(position?.priority, 1) }).map(
              (_, index) => (
                <span key={index} style={{ marginRight: 4 }}>
                  🔥
                </span>
              )
            )}
          </>
        );
      },
      sorter: (a, b) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
        return a.title.localeCompare(b.title);
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      width: 180,
      render: (status, position) => {
        return (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Tag>{status}</Tag>
            <EditOutlined
              style={{ color: "dimgray", cursor: "pointer" }}
              onClick={(e) => {
                handleEditStatus(position);
              }}
            />
          </div>
        );
      },
    },
    ShowClosedPositions && {
      title: "Closed on",
      dataIndex: "updatedAt",
      width: 150,
      render: (_, position) => {
        return (
          <>
            {notUndefined(position?.updatedAt)
              ? moment(position?.updatedAt).format("Do MMM YYYY")
              : "-"}
          </>
        );
      },
      sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt),
      defaultSortOrder: "descend",
    },

    {
      title: () => (
        <div className="flex flex-col">
          <span>Candidate Overview</span>{" "}
          <div className="text-smallest text-gray-500 flex gap-1">
            Last Updated{" "}
            <Tooltip
              title={moment(candidatesOverview?.generated_at)?.format(
                DateFormatWithTime
              )}
            >
              <div className="border-b-2 border-dashed border-gray-700 pb-0.5 cursor-default">
                {moment(candidatesOverview?.generated_at).fromNow()}
              </div>
            </Tooltip>
            <Tooltip title="Update Overview">
              <ReloadOutlined
                className="cursor-pointer"
                onClick={handleUpdateCandidateOverview}
              />{" "}
            </Tooltip>
          </div>
        </div>
      ),
      dataIndex: "candidates",
      width: "15%",
      render: (candidates, position) => {
        if (fetchCandidatesLoader) {
          return (
            <div className="grid align-middle">
              <Loading3QuartersOutlined spin />
            </div>
          );
        }

        return (
          <Space direction="vertical" size="small">
            {candidatesOverview?.overview?.[position?.id]?.status_counts?.map(
              ({ status, count }) => (
                <Tag key={status} color={CandidateStatusColorMap[status]}>
                  {status}: {count}
                </Tag>
              )
            )}
            <Tag color="green">
              Total:{" "}
              {candidatesOverview?.overview?.[position?.id]?.total_candidates}
            </Tag>
          </Space>
        );
      },
    },
    {
      title: "Potential Project",
      dataIndex: "potential_project",
      render: (potential_project, position) => (
        <div className="flex flex-col gap-y-2">
          <span>{potential_project}</span>
          <span className="text-smallest">
            Requried Experience: {position?.required_experience}
          </span>
        </div>
      ),
      width: 150,
      sorter: (a, b) =>
        a.potential_project?.localeCompare(b?.potential_project),
    },
    {
      title: "Notes",
      dataIndex: "notes",
      width: "25%",
      render: (note) => {
        if (!note) {
          return "-";
        }
        return (
          <div>
            <p>{note?.note}</p>
            <small className="ml-2 mt-2">
              {"- "}
              {note?.owner_name}
            </small>
          </div>
        );
      },
    },
    {
      title: "Potential Start Date",
      dataIndex: "deadline",
      width: 150,
      render: (deadline, position) => {
        return (
          <>
            <span
              className={`whitespace-nowrap ${
                position.deadlineCross ? "text-red-500" : "text-black"
              }`}
            >
              {deadline}
            </span>
          </>
        );
      },
      sorter: (a, b) => new Date(a.deadline) - new Date(b.deadline),
      defaultSortOrder: ShowClosedPositions ? undefined : "ascend",
    },
    {
      title: "Owner",
      dataIndex: "owner",
      render: (owner) => {
        return <RenderEmployeeFullName employee={owner} />;
      },
    },
    {
      title: "Actions",
      dataIndex: "",
      fixed: "right",
      align: "center",
      width: 90,
      render: (_, position) => {
        return position?.open ? (
          <div style={{ display: "flex", gap: "10px" }}>
            <EditOutlined
              style={{ color: "dimgray", cursor: "pointer" }}
              onClick={() => handleEditPosition(position)}
            />
            <Popconfirm
              title="Are you sure you want to mark this as closed?"
              onConfirm={() => handleClosePosition(position)}
              okText="Yes"
              cancelText="No"
            >
              <CloseOutlined className="cursor-pointer" />
            </Popconfirm>
          </div>
        ) : null;
      },
    },
  ].filter(Boolean);

  const handleShowClosedPositions = () => {
    setShowClosedPositions(!ShowClosedPositions);
    handleFetchJobPosts({ open: ShowClosedPositions });
  };

  const goToHiringDetails = (positionId) => {
    navigate(`/hiring/job-positions/${positionId}`);
  };

  const [fetchJobPostLoader, setfetchJobPostLoader] = useState(false);
  const [fetchCandidatesLoader, setfetchCandidatesLoader] = useState(false);

  const candidatesOverview = useSelector(getCandidatesOverviewFromReducer);

  const [jobPostList, setjobPostList] = useState([]);

  useEffect(() => {
    handleFetchCandidatesPerJobPost();
    handleFetchJobPosts({ open: true });
  }, []);

  // Fetch From DB
  const handleFetchJobPosts = async ({ open = true }) => {
    try {
      setfetchJobPostLoader(true);
      const response = await listJobPostAction({ open: { eq: open } });
      handleTransformJobPostData(response);
    } catch (error) {
      console.log("Error Fethcing chart data");
      message.error("Unable to fetch data at the moment!");
    } finally {
      setfetchJobPostLoader(false);
    }
  };

  // Fetch Candidate For Job Post from DB
  const handleFetchCandidatesPerJobPost = async () => {
    try {
      setfetchCandidatesLoader(true);

      const url = await Storage.get(CandidateCountReportPerJobPosition, {
        level: "public",
      });
      console.log(url);
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const data = await response.json(); // Use .json() if the file contains JSON data
      dispatch(
        setCandidatesOverview({
          ...data,
          overview: data?.data ?? data?.overview,
        })
      );
      console.log(data);
    } catch (error) {
      console.log("Error Fethcing Candidates data");
      message.error("Unable to fetch Candidates oveview at the moment!");
    } finally {
      setfetchCandidatesLoader(false);
    }
  };

  const handleUpdateCandidateOverview = async () => {
    try {
      setfetchCandidatesLoader(true);
      await API.get("RestHRMS", "/reports/update-hiring-reports");
      handleFetchCandidatesPerJobPost();
    } catch (error) {
      console.log(error);
    }
  };

  // Transform Data
  const handleTransformJobPostData = (jobPosts) => {
    let data = [];
    data = jobPosts.map((item) => {
      let noteObj;
      if (item?.notes && item?.notes?.slice(-1)[0]) {
        noteObj = item?.notes?.slice(-1)[0];
      }

      let candidatesArray = undefined;
      if (item?.candidates?.length > 0) {
        let dataObj = {};
        candidatesArray = [];
        item?.candidates?.forEach((item) => {
          dataObj[item?.status] = dataObj[item?.status]
            ? dataObj[item?.status] + 1
            : 1;
        });
        Object.keys(dataObj)?.forEach((status) =>
          candidatesArray?.push({ status, count: dataObj[status] })
        );
      }

      let data = parseJobPostDataForTable(item);

      data.candidates = candidatesArray;
      data.totalCandidates = item?.candidates?.length;
      data.notes = noteObj;
      data.key = data?.id;
      return data;
    });
    console.log(data);
    setjobPostList(data);
  };

  // Diffrent Function Becuase in somecase need to update only one
  const parseJobPostDataForTable = (item) => {
    return {
      title: item?.title,
      status: item?.status,
      potential_project: item?.potential_project ?? "-",
      deadline: item?.deadline ?? "-",
      deadlineCross: moment(item?.deadline).isBefore(moment()),
      required_experience: item?.required_experience ?? "-",
      owner: item?.owner,
      open: item?.open,
      priority: item?.priority ?? 0,

      // Saving for Edit
      id: item?.id,
      description: item?.description,
      potential_squad: item?.hiringPositionPotential_squadId,
      guild: item?.hiringPositionGuildId,
      position: item?.position?.name,
      quotation: item?.quotation?.project,
      potential_closeDate: item?.potential_closeDate,
      location: item?.location,
      hiringPositionPositionId: item?.hiringPositionPositionId,
      ...item,
    };
  };

  // Close Position
  const handleClosePosition = async (position) => {
    try {
      let inputData = {
        id: position?.id,
        open: false,
        benchEmployeeSuggestion: null,
      };
      await createUpdateHiringPosition(inputData);
      handleFetchJobPosts({ open: true });
    } catch (error) {
      console.log(error);
    }
  };

  // Row Selection for seeking help
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [askReferralsLoader, setaskReferralsLoader] = useState(false);
  const onSelectChange = (newSelectedRowKeys) => {
    if (newSelectedRowKeys?.length >= 4) {
      message.error("You can select maximum 3 Job posts at a time!");
      return;
    }
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handleAskForRefferals = async () => {
    console.log(selectedRowKeys);
    try {
      setaskReferralsLoader(true);
      await API.post("RestHRMS", "/jobPostSharing/addReferralNotice", {
        body: { jobPostIds: selectedRowKeys, userEmail: email },
      });
      message.success("Asked for referrals successfully!");
      setSelectedRowKeys([]);
    } catch (error) {
      message.error("Unable to ask for referral at a moment.");
    } finally {
      setaskReferralsLoader(false);
    }
  };

  return (
    <div>
      <div className="flex gap-5 ">
        <Row justify="space-between" align="middle" style={{ width: "100%" }}>
          <Col>
            <div>
              {isKanbanView && (
                <Typography.Title level={4} className="mt-3">
                  Job Positions Kanban View
                </Typography.Title>
              )}
            </div>
          </Col>
          <Col>
            <div className="flex justify-end w-full">
              {isPowerUser && (
                <Row justify="end" gutter={[20]}>
                  {selectedRowKeys?.length > 0 ? (
                    <Button
                      onClick={handleAskForRefferals}
                      loading={askReferralsLoader}
                      className="ml-2"
                    >
                      Ask for Refferals
                    </Button>
                  ) : null}
                  <Col style={{ paddingTop: "5px" }}>
                    Show Closed Positions{" "}
                    <Switch
                      checked={ShowClosedPositions}
                      onChange={handleShowClosedPositions}
                    />
                  </Col>
                  <Col>
                    <Row gutter={[10, 0]} align="middle" className="mt-1">
                      <Col>
                        <Tooltip title="Table View">
                          <TableOutlined
                            style={{
                              color: !isKanbanView
                                ? "#00b48a"
                                : "rgba(0,0,0,0.45)",
                              fontSize: "26px",
                              cursor: "pointer",
                            }}
                            onClick={() => setIsKanbanView(false)}
                          />
                        </Tooltip>
                      </Col>
                      <Col>
                        <Tooltip title="Kanban View">
                          <AppstoreAddOutlined
                            style={{
                              color: isKanbanView
                                ? "#00b48a"
                                : "rgba(0,0,0,0.45)",
                              fontSize: "26px",
                              cursor: "pointer",
                            }}
                            onClick={() => setIsKanbanView(true)}
                          />
                        </Tooltip>
                      </Col>
                    </Row>
                  </Col>
                </Row>
              )}
              <Button
                onClick={handleAddPosition}
                type="primary"
                className="ml-2"
              >
                + Add Position
              </Button>
            </div>
          </Col>
        </Row>
      </div>

      {isKanbanView ? (
        <KanbanBoard
          items={jobPostList}
          type={KanbanBoardStatuses.JOB_POSITION}
          statuses={JobPostStatuses}
          renderCard={JobCard}
          isLoading={skeletonLoader}
        />
      ) : (
        <CustomTable
          skeletonLoading={fetchJobPostLoader}
          title={"Job Positions"}
          columns={columns}
          dataSource={jobPostList}
          bordered
          pagination={{ pageSize: 20 }}
          scroll={{ x: 1500 }}
          rowSelection={rowSelection}
        />
      )}
    </div>
  );
}

export default HiringPositions;
