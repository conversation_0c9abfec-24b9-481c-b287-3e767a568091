export const listJiraQualityForProject = /* GraphQL */ `
  query ListJiraEvents($eq: String, $limit: Int, $nextToken: String) {
    listJiraEvents(
      filter: { jiraProjectId: { eq: $eq } }
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        issueKey
        eventType
        issueSummary
        description
        descriptionQualityAI
        sprintId
        sprintName
        boardId
        boardName
        storyPoints
        employeeEmail
        employee {
          email
          first_name
          last_name
          profile_pic
        }
        issueCreatorEmail
        issueCreator {
          email
          first_name
          last_name
          profile_pic
        }
        projectId
        parentIssueKey
        createdAt
        issueType
        status
        priority
        severity
      }
      nextToken
      __typename
    }
  }
`;
