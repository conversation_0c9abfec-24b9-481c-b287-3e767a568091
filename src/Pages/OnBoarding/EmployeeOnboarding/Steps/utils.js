import dayjs from "dayjs";

// Reusable styling classes for consistent UI components

/**
 * Common input styles to maintain consistent UI across text fields
 */
export const inputClasses =
  "rounded-lg py-2 px-3 border-gray-300 hover:border-blue-400 focus:border-blue-500";

/**
 * Common date picker styling classes
 */
export const datePickerClasses =
  "w-full rounded-lg py-2 px-3 border-gray-300 hover:border-blue-400 focus:border-blue-500";

/**
 * Title styling for section headers
 */
export const sectionTitleClasses = "text-lg font-semibold text-gray-800";

/**
 * Container for sections, with spacing between items
 */
export const sectionContainerClasses = "space-y-2";

/**
 * Layout class for grid structure with responsiveness
 */
export const gridLayoutClasses =
  "grid grid-cols-1 md:grid-cols-2 gap-x-4 md:gap-x-4";

/**
 * Disables future dates for date picker fields (i.e., user cannot pick a date after today)
 * @param {dayjs} current - The current date being evaluated for disabling
 * @returns {boolean} - Returns true if the date is in the future, thus disabling it; false otherwise
 */
export const disableFutureDates = (current) =>
  current && current.isAfter(dayjs().endOf("day")); // Use `isAfter` to check if the date is in the future
