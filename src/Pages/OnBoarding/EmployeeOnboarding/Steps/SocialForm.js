import { Form, Input } from "antd";
import { inputClasses } from "./utils";

/**
 * SocialForm Component
 *
 * A form for collecting and validating social media profile links.
 * The form uses URL validation for each social media field and provides character count feedback.
 *
 * Features:
 * - URL validation for all social media fields
 * - Responsive grid layout (single column on mobile, two columns on desktop)
 * - Integrated form validation with `yupSync`
 * - Character count with validation feedback for all input fields
 *
 * @param {Object} props - Component props
 * @param {Function} props.yupSync - Yup validation synchronizer for Ant Design Form
 */
const SocialForm = ({ yupSync }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 md:gap-x-4">
        {/* Facebook */}
        <Form.Item name="facebook_link" label="Facebook" rules={[yupSync]}>
          <Input
            placeholder="https://facebook.com/username"
            className={inputClasses}
            allowClear
          />
        </Form.Item>

        {/* LinkedIn */}
        <Form.Item name="linkedin_link" label="LinkedIn" rules={[yupSync]}>
          <Input
            placeholder="https://linkedin.com/in/username"
            className={inputClasses}
            allowClear
          />
        </Form.Item>

        {/* Twitter */}
        <Form.Item name="twitter_link" label="Twitter" rules={[yupSync]}>
          <Input
            placeholder="https://twitter.com/username"
            className={inputClasses}
            allowClear
          />
        </Form.Item>

        {/* Instagram */}
        <Form.Item name="instagram_link" label="Instagram" rules={[yupSync]}>
          <Input
            placeholder="https://instagram.com/username"
            className={inputClasses}
            allowClear
          />
        </Form.Item>
      </div>
    </div>
  );
};

export default SocialForm;
