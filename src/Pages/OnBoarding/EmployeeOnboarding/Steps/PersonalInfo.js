import { Divider, Form, Input, message, Select } from "antd";
import HubDatePicker from "AtomicComponents/Antd/HubDatePicker";
import {
  BloodGroupSelect,
  GenderSelect,
} from "AtomicComponents/HubSelectFields/HubSelect";
import PhoneInput from "react-phone-number-input";
import {
  gridLayoutClasses,
  inputClasses,
  sectionContainerClasses,
  sectionTitleClasses,
} from "./utils";
import {
  pattenRequiredRule,
  validateFirstCharacterAsSpace,
  validateLastCharacterAsSpace,
  validateOnlyAlphabets,
} from "utils/validationRules";
import moment from "moment";
import { MailOutlined } from "@ant-design/icons";
import { getRestAPIData } from "utils/RestApiHelper";
/**
 * PersonalInfo Component
 *
 * A form for collecting personal, contact, and family information.
 * The form is structured into three sections: Personal Information, Contact Details, and Family Information.
 *
 * Features:
 * - Utilizes a responsive grid layout (single column on mobile, two columns on desktop)
 * - Integrated with Ant Design Form for validation
 * - Date picker with future date restriction for birth date and anniversary
 *
 * @param {Object} props - Component props
 * @param {Function} props.yupSync - Yup validation handler for form fields
 */
const PersonalInfo = ({ yupSync, country, form, setCountry }) => {
  const handleEmailChange = async (e) => {
    const email = e.target.value;
    if (
      email &&
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email)
    ) {
      try {
        const response = await getRestAPIData(
          `/api/candidate-details?email=${email}`
        );
        if (response) {
          const { email: _, ...cleanData } = response;
          const employeeData = { ...cleanData, personal_email: email };
          form.setFieldsValue(employeeData);
          if (Object.keys(employeeData).length > 0)
            message.success(
              "Found your information! Data has been pre-filled."
            );
        }
      } catch (error) {
        console.error("Error fetching candidate details:", error);
        message.error("No matching data found for this email.");
      }
    }
  };

  const handleCountryChange = (value) => {
    setCountry(value);
    form.setFieldValue("country", value);
  };

  return (
    <div className="space-y-4">
      {/* ========== PERSONAL EMAIL SECTION ========== */}
      <section className={sectionContainerClasses}>
        <div className="mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="personal_email"
              label="Personal Email"
              rules={[
                {
                  validator(_, value) {
                    if (!value) {
                      return Promise.reject(
                        new Error("Personal Email is required")
                      );
                    }
                    const emailRegex =
                      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (!emailRegex.test(value)) {
                      return Promise.reject(new Error("Enter a valid email"));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="<EMAIL>"
                type="email"
                className={inputClasses + " mt-2 "}
                onChange={handleEmailChange}
                size="large"
              />
            </Form.Item>

            <Form.Item
              name="country"
              label="Country"
              initialValue="IN"
              rules={[
                {
                  required: true,
                  message: "Please select your country",
                },
              ]}
            >
              <Select
                placeholder="Select Country"
                size="large"
                className={inputClasses + " pr-2"}
                onChange={handleCountryChange}
                options={[
                  { value: "IN", label: "🇮🇳 India" },
                  { value: "US", label: "🇺🇸 United States" },
                ]}
              />
            </Form.Item>
          </div>
          <p className="text-sm text-gray-500 -mt-4">
            We'll try to pre-fill your information if we find a match for this
            email.
          </p>
        </div>
      </section>

      <Divider className="my-6 border-gray-200" />

      {/* ========== PERSONAL INFORMATION SECTION ========== */}
      <section className={sectionContainerClasses}>
        <h3 className={sectionTitleClasses}>Personal Information</h3>

        <div className={gridLayoutClasses}>
          {/* Name Fields */}
          <Form.Item
            name="first_name"
            label="First Name"
            rules={[
              yupSync,
              validateOnlyAlphabets("First name should contain only alphabets"),
              validateFirstCharacterAsSpace(
                "First name cannot start with a space"
              ),
              validateLastCharacterAsSpace(
                "First name cannot end with a space"
              ),
            ]}
          >
            <Input placeholder="John" className={inputClasses} />
          </Form.Item>

          <Form.Item
            name="last_name"
            label="Last Name"
            rules={[
              yupSync,
              validateOnlyAlphabets("Last name should contain only alphabets"),
              validateFirstCharacterAsSpace(
                "Last name cannot start with a space"
              ),
              validateLastCharacterAsSpace("Last name cannot end with a space"),
            ]}
          >
            <Input placeholder="Doe" className={inputClasses} />
          </Form.Item>

          {/* Personal Details */}
          <Form.Item
            name="email"
            label="Email"
            rules={[
              yupSync,
              pattenRequiredRule(
                /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                "Please enter a valid email address"
              ),
            ]}
          >
            <Input
              disabled
              placeholder="<EMAIL>"
              type="email"
              className={inputClasses}
            />
          </Form.Item>

          <Form.Item name="birth_date" label="Date of Birth" rules={[yupSync]}>
            <HubDatePicker
              allowFutureDates={false}
              disabledDate={(current) =>
                current &&
                (current >= moment().endOf("day") ||
                  current.isSame(moment(), "day"))
              }
            />
          </Form.Item>

          <Form.Item name="gender" label="Gender" rules={[yupSync]}>
            <GenderSelect />
          </Form.Item>

          {country !== "US" && (
            <Form.Item name="blood_group" label="Blood Group" rules={[yupSync]}>
              <BloodGroupSelect />
            </Form.Item>
          )}
        </div>
      </section>

      {/* ========== CONTACT DETAILS SECTION ========== */}
      <Divider className="my-6 border-gray-200" />
      <section className={sectionContainerClasses}>
        <h3 className={sectionTitleClasses}>Contact Details</h3>

        <div className={gridLayoutClasses}>
          {/* Full-width address field */}
          <div className="md:col-span-2">
            <Form.Item
              name="address"
              label="Address"
              rules={[
                yupSync,
                validateFirstCharacterAsSpace(
                  "Address cannot start with a space"
                ),
                validateLastCharacterAsSpace("Address cannot end with a space"),
                {
                  validator: (_, value) => {
                    if (value && !/^[a-zA-Z0-9\s,.#\-/]+$/.test(value)) {
                      return Promise.reject(
                        new Error("Address contains invalid characters")
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input.TextArea
                placeholder="123 Main St, City, Country"
                rows={3}
                showCount
              />
            </Form.Item>
          </div>

          {/* Contact Information */}
          <Form.Item name="mobile" label="Mobile Number" rules={[yupSync]}>
            <PhoneInput
              className={`border ${inputClasses}`}
              international
              defaultCountry="US"
              placeholder="+91 ************"
            />
            {/* <Input placeholder="+1 ************" className={inputClasses} /> */}
          </Form.Item>

          {/* Emergency Contact */}
          <Form.Item
            name="emergency_contact_num"
            label="Emergency Contact Number"
            rules={[
              yupSync,
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const mobile = getFieldValue("mobile");
                  if (value && mobile && value === mobile) {
                    return Promise.reject(
                      new Error(
                        "Emergency Contact Number cannot be the same as Mobile Number"
                      )
                    );
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <PhoneInput
              className={`border ${inputClasses}`}
              international
              defaultCountry="US"
              placeholder="+91 ************"
            />
          </Form.Item>

          <Form.Item
            name="emergency_contact_relation"
            label="Emergency Contact Relation"
            rules={[
              yupSync,
              validateFirstCharacterAsSpace(
                "Relation cannot start with a space"
              ),
              validateLastCharacterAsSpace("Relation cannot end with a space"),
              validateOnlyAlphabets("Relation should contain only alphabets"),
            ]}
          >
            <Input
              placeholder="Father/Mother/Spouse"
              className={inputClasses}
            />
          </Form.Item>
        </div>
      </section>

      {/* ========== FAMILY INFORMATION SECTION ========== */}
      <Divider className="my-6 border-gray-200" />
      <section className={sectionContainerClasses}>
        <h3 className={sectionTitleClasses}>Family Information</h3>

        <div className={gridLayoutClasses}>
          {/* Spouse Full Name */}
          <Form.Item
            name="spouse_full_name"
            label="Spouse Full Name"
            rules={[
              validateFirstCharacterAsSpace(
                "Spouse name cannot start with a space"
              ),
              validateLastCharacterAsSpace(
                "Spouse name cannot end with a space"
              ),
              validateOnlyAlphabets(
                "Spouse name should contain only alphabets"
              ),
            ]}
          >
            <Input placeholder="Jane Doe" className={inputClasses} />
          </Form.Item>

          {country !== "US" && (
            <Form.Item name="anniversary_date" label="Anniversary Date">
              <HubDatePicker
                allowFutureDates={false}
                disabledDate={(current) =>
                  current &&
                  (current >= moment().endOf("day") ||
                    current.isSame(moment(), "day"))
                }
              />
            </Form.Item>
          )}
        </div>
      </section>
    </div>
  );
};

export default PersonalInfo;
