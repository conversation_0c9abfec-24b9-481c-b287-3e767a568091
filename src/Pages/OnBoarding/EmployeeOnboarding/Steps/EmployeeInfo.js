import { Divider, Form, Input, message } from "antd";
import HubDatePicker from "AtomicComponents/Antd/HubDatePicker";
import SkillsSelect from "AtomicComponents/HubSelectFields/SelectSkills";
import SMESelect from "AtomicComponents/HubSelectFields/SMESelect";
import { disableFutureDates } from "utils/commonMethods";
import {
  gridLayoutClasses,
  inputClasses,
  sectionContainerClasses,
  sectionTitleClasses,
} from "./utils";
import { useState, useEffect } from "react";
import { Editor } from "Commons/Editor/Editor";
import {
  validateFirstCharacterAsSpace,
  validateLastCharacterAsSpace,
  validateOnlyAlphabets,
  validateOnlyNumeric,
} from "utils/validationRules";

/**
 * EmployeeInfo Component
 *
 * A form for capturing professional details and employment history of an employee.
 * The form contains fields for skills, SME designation, EPF number, UAN number, career start date, and referral details.
 *
 * Features:
 * - Responsive 2-column layout with Ant Design's Form and Input components
 * - Validation via Yup
 * - Date picker with future date prevention for career start date
 * - Consistent styling using utility classes
 *
 * @param {Object} props - Component props
 * @param {Object} props.form - Ant Design form instance
 * @param {Function} props.yupSync - Yup validation handler
 * @param {string} props.country - Country code to determine field requirements
 */
const EmployeeInfo = ({ form, yupSync, country }) => {
  const [editorContent, setEditorContent] = useState("");

  useEffect(() => {
    const introduction = form.getFieldValue("introduction");
    if (introduction && introduction !== editorContent) {
      setEditorContent(introduction);
    }
  }, [form, editorContent]);

  const validateIntroductionLength = (content) => {
    // Remove HTML tags to count only text content
    const textContent = content ? content.replace(/<[^>]*>/g, "") : "";
    return textContent.length <= 500;
  };

  // Handle editor content change and update form field
  const handleEditorChange = (content) => {
    // Remove HTML tags to count only text content
    const textContent = content ? content.replace(/<[^>]*>/g, "") : "";

    // Check if the text content exceeds 500 characters
    if (textContent.length > 500) {
      message.warning("Introduction cannot exceed 500 characters");
      return;
    }

    setEditorContent(content);
    form.setFieldValue("introduction", content);
  };

  return (
    <div className="space-y-4">
      {/* ========== PROFESSIONAL DETAILS SECTION ========== */}
      <section className={sectionContainerClasses}>
        <h3 className={sectionTitleClasses}>Professional Details</h3>

        <div className={gridLayoutClasses}>
          {/* Skills Multi-Select Field */}
          <Form.Item name="skills" label="Skills" rules={[yupSync]}>
            <SkillsSelect
              mode="multiple"
              fieldName="skills"
              form={form}
              maxCount={5}
            />
          </Form.Item>

          {/* SME Single-Select Field */}
          <Form.Item
            name="sme"
            label="Subject Matter Expertise"
            rules={[yupSync]}
          >
            <SMESelect
              form={form}
              fieldName="sme"
              placeholder="Select subject matter experts..."
            />
          </Form.Item>

          {/* EPF Account Number - Only for non-US countries */}
          {country !== "US" && (
            <Form.Item
              name="epf"
              label="EPF Account Number"
              rules={[
                yupSync,
                validateOnlyNumeric(
                  "EPF account number should contain only numbers"
                ),
              ]}
            >
              <Input
                placeholder="EPF account number"
                className={inputClasses}
              />
            </Form.Item>
          )}

          {/* UAN Number - Only for non-US countries */}
          {country !== "US" && (
            <Form.Item
              name="uan"
              label="UAN Number"
              help="12-digit Universal Account Number"
              rules={[
                yupSync,
                validateOnlyNumeric("UAN should contain only numbers"),
              ]}
            >
              <Input placeholder="12-digit UAN" className={inputClasses} />
            </Form.Item>
          )}
        </div>
      </section>

      {/* ========== EMPLOYMENT HISTORY SECTION ========== */}
      <Divider className="my-6 border-gray-200" />
      <section className={sectionContainerClasses}>
        <h3 className={sectionTitleClasses}>Employment History</h3>

        <div className={gridLayoutClasses}>
          {/* Career Start Date Picker */}
          <Form.Item
            name="career_start_date"
            label="Career Start Date"
            rules={[yupSync]}
          >
            <HubDatePicker
              disabledDate={disableFutureDates} // Prevent future dates
              placeholder="Select start date"
            />
          </Form.Item>

          {/* Referral Information */}
          <Form.Item
            name="reffered_by"
            label="Referred By (if any)"
            rules={[
              validateOnlyAlphabets(
                "Referred By should contain only alphabets"
              ),
              validateFirstCharacterAsSpace(
                "Referred By cannot start with a space"
              ),
              validateLastCharacterAsSpace(
                "Referred By cannot end with a space"
              ),
            ]}
          >
            <Input placeholder="Referrer name" className={inputClasses} />
          </Form.Item>
        </div>
      </section>
      {/* ========== EMPLOYEE BIO SECTION ========== */}
      <Divider className="my-6 border-gray-200" />
      <section className={sectionContainerClasses}>
        <h3 className={sectionTitleClasses}>Employee Bio</h3>

        <Form.Item
          label="Personal Introduction"
          name="introduction"
          rules={[
            {
              validator: () => {
                if (validateIntroductionLength(editorContent)) {
                  return Promise.resolve();
                }
                return Promise.reject(
                  "Introduction cannot exceed 500 characters"
                );
              },
            },
          ]}
          className="mb-6"
          extra={`Introduction cannot exceed 500 characters (${
            (editorContent ? editorContent.replace(/<[^>]*>/g, "") : "").length
          }/500)`}
        >
          <Editor
            editorContent={editorContent}
            setEditorContent={handleEditorChange}
            allowImage={false}
          />
        </Form.Item>
      </section>
    </div>
  );
};

export default EmployeeInfo;
