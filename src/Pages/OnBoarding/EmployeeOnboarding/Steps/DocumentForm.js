import {
  FileImageOutlined,
  FilePdfOutlined,
  UploadOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { Button, Form, Upload, message } from "antd";
import { useCallback, useState, useMemo } from "react";
import { useSelector } from "react-redux";
import { getCurrentUserData } from "store/slices/loginSlice";
import { UploadConstants } from "utils/constants";
import { validateFile } from "utils/DataManipulation/dataFunctions";
import { getPresignedUrl, uploadToS3Bucket } from "utils/helperFunction";

/**
 * DocumentForm Component
 *
 * A document management form that supports upload, preview, delete, and validation.
 * - Clean and responsive UI with Tailwind CSS
 * - File validation before upload (type & size restrictions)
 * - Document preview in a new tab
 * - Deletion of uploaded documents
 * - Loading states for upload and delete actions
 *
 * @param {Object} props - Component props
 * @param {Object} props.form - AntD form instance
 * @param {Function} props.yupSync - Yup validation schema
 * @param {Object} props.documentUrls - Current document URLs state
 * @param {Function} props.setDocumentUrls - Function to update document URLs state
 * @param {string} props.country - Country code to determine document requirements
 */
const DocumentForm = ({
  form,
  yupSync,
  documentUrls,
  setDocumentUrls,
  country,
}) => {
  // Get current user data from the store
  const userData = useSelector(getCurrentUserData);

  // Loading states for each document field
  const [loadingStates, setLoadingStates] = useState({
    aadhar_card: false,
    pan_card: false,
    passport_photo: false,
    profile_pic: false,
  });

  /**
   * Configuration for each document type including labels, accepted file types, and icons.
   * Use useMemo to memoize the configuration and avoid unnecessary recalculations.
   */
  const documentConfig = useMemo(
    () => ({
      aadhar_card: {
        label: "Aadhar Card",
        accept: UploadConstants.GOV_ID_CARD,
        icon: <FilePdfOutlined className="text-blue-500" />,
        borderColor: "border-l-blue-500",
        showForCountries: ["IN"], // Only show for India
      },
      pan_card: {
        label: "PAN Card",
        accept: UploadConstants.GOV_ID_CARD,
        icon: <FilePdfOutlined className="text-purple-500" />,
        borderColor: "border-l-purple-500",
        showForCountries: ["IN"], // Only show for India
      },
      passport_photo: {
        label: "Passport Photo",
        accept: UploadConstants.IMAGE,
        icon: <FileImageOutlined className="text-cyan-500" />,
        borderColor: "border-l-cyan-500",
        showForCountries: ["IN", "US"], // Show for all countries
      },
      profile_pic: {
        label: "Profile Picture",
        accept: UploadConstants.IMAGE,
        icon: <FileImageOutlined className="text-orange-500" />,
        borderColor: "border-l-orange-500",
        showForCountries: ["IN", "US"], // Show for all countries
      },
    }),
    []
  );

  /**
   * Filter documents based on country requirements
   */
  const filteredDocumentConfig = useMemo(() => {
    return Object.keys(documentConfig).reduce((acc, fieldName) => {
      const config = documentConfig[fieldName];
      if (config.showForCountries.includes(country || "IN")) {
        acc[fieldName] = config;
      }
      return acc;
    }, {});
  }, [documentConfig, country]);

  /**
   * Validates file before upload, checking file type and size
   * @param {File} file - The file to validate
   * @param {string} type - Document field type (e.g., aadhar_card, pan_card, etc.)
   * @returns {boolean} True if the file is valid, false otherwise
   */
  const beforeUpload = useCallback(
    (file, type) => {
      // Set loading state for the respective document field
      setLoadingStates((prev) => ({ ...prev, [type]: true }));

      // Validate the file based on its type
      const isValid = validateFile(file, documentConfig[type].accept);
      if (!isValid) {
        setLoadingStates((prev) => ({ ...prev, [type]: false }));
        message.error("Invalid file type or size");
      }

      return isValid;
    },
    [documentConfig] // Only recompute if documentConfig changes
  );

  /**
   * Handles file upload to S3 bucket
   * @param {Object} values - The file values
   * @param {string} fieldName - The document field name
   */
  const handleFileUpload = async (values, fieldName) => {
    try {
      // Upload the file to S3 and get the response URL
      const response = await uploadToS3Bucket({
        file: values.file,
        folder: "documents",
        userId: userData?.email,
      });

      // Update form fields and document URLs state
      form.setFieldsValue({ ...form.getFieldsValue(), [fieldName]: response });
      const presignedUrl = await getPresignedUrl({ key: response });
      setDocumentUrls((prev) => ({ ...prev, [fieldName]: presignedUrl }));

      // Show success message
      message.success(
        `${documentConfig[fieldName].label} uploaded successfully`
      );
    } catch (error) {
      message.error("Upload failed. Please try again.");
    } finally {
      // Reset loading state after upload completion
      setLoadingStates((prev) => ({ ...prev, [fieldName]: false }));
    }
  };

  /**
   * Handles document deletion, clearing the URL and resetting the form
   * @param {string} fieldName - The document field name to delete
   */
  const handleDelete = (fieldName) => {
    // Remove document field from the form and update document URLs state
    form.setFieldsValue({ ...form.getFieldsValue(), [fieldName]: undefined });
    setDocumentUrls((prev) => {
      const newUrls = { ...prev };
      delete newUrls[fieldName];
      return newUrls;
    });

    // Show success message
    message.success(`${documentConfig[fieldName].label} removed`);
  };

  /**
   * Renders the document card with upload or preview functionality
   * @param {string} fieldName - The document field name (e.g., aadhar_card, pan_card)
   * @returns {JSX.Element} The rendered document card
   */
  const renderDocumentCard = (fieldName) => {
    const config = documentConfig[fieldName];
    const hasDocument = !!documentUrls?.[fieldName];

    return (
      <div
        className={`w-full border-l-4 ${config.borderColor} bg-white rounded shadow-sm p-3`}
      >
        <div className="flex flex-col space-y-2 w-full">
          <span className="font-medium text-gray-800">{config.label}</span>

          {hasDocument ? (
            <div className="flex justify-between items-center w-full">
              <a
                href={documentUrls[fieldName]}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
              >
                {config.icon}
                <span>View Document</span>
              </a>
              <button
                onClick={() => handleDelete(fieldName)}
                disabled={loadingStates[fieldName]}
                className="text-red-500 hover:text-red-700 disabled:opacity-50"
              >
                <DeleteOutlined />
              </button>
            </div>
          ) : (
            <Upload
              accept={config.accept}
              beforeUpload={(file) => beforeUpload(file, fieldName)}
              customRequest={(values) => handleFileUpload(values, fieldName)}
              multiple={false}
              showUploadList={false}
            >
              <Button
                icon={<UploadOutlined />}
                loading={loadingStates[fieldName]}
                className="w-full border-dashed hover:border-solid"
              >
                Upload {config.label}
              </Button>
            </Upload>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.keys(filteredDocumentConfig).map((fieldName) => (
          <Form.Item key={fieldName} name={fieldName} rules={[yupSync]}>
            {renderDocumentCard(fieldName)}
          </Form.Item>
        ))}
      </div>
    </div>
  );
};

export default DocumentForm;
