import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CodeSandboxOutlined,
  DropboxOutlined,
  InstagramOutlined,
  SaveOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Button, Divider, Form, message, Steps } from "antd";
import Background from "Commons/Background";
import CustomForm from "Commons/CustomForm/CustomForm";
import Loader from "Commons/Loader";
import dayjs from "dayjs";
import Navbar from "Pages/Careers/Navbar";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getCurrentUserData,
  SetEmployeeDetails,
} from "store/slices/loginSlice";
import {
  getEmployeeDetails,
  updateEmployeeDetails,
} from "utils/api-actions/user-operation";
import { getRestAPIData } from "utils/RestApiHelper";
import { REQUIRED_ERROR } from "utils/YupErrorHelper";
import * as yup from "yup";
import DocumentForm from "./Steps/DocumentForm";
import EmployeeInfo from "./Steps/EmployeeInfo";
import PersonalInfo from "./Steps/PersonalInfo";
import SocialForm from "./Steps/SocialForm";
import { isValidPhoneNumber } from "react-phone-number-input";
// Constants
const AUTO_SAVE_INTERVAL = 5000; // 5 seconds auto-save interval
const SCROLL_OPTIONS = { top: 0, behavior: "smooth" };

/**
 * Unified Employee Onboarding Component
 *
 * Optimizations made:
 * 1. Improved loading state management to show loader until initialization is complete
 * 2. Memoized all callback functions and schemas to prevent unnecessary re-renders
 * 3. Used useRef for timeout and previous values to avoid stale closures
 * 4. Implemented proper cleanup in useEffect
 * 5. Optimized form change handling with debouncing
 * 6. Added better TypeScript-like documentation
 * 7. Split large functions into smaller, focused ones
 * 8. Improved state management structure
 */
const UnifiedEmployeeOnboarding = () => {
  const dispatch = useDispatch();
  // Form instances
  const [form] = Form.useForm();

  // Component state
  const [loading, setLoading] = useState(false);
  const [initializing, setInitializing] = useState(true); // Tracks initialization state
  const [hasPrefilledData, setHasPrefilledData] = useState(false);
  const [employeeData, setEmployeeData] = useState(null);
  const [documentUrls, setDocumentUrls] = useState(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [formChanged, setFormChanged] = useState(false);
  const [country, setCountry] = useState(null);

  // Refs
  const autoSaveIntervalRef = useRef(null);
  const lastSavedValuesRef = useRef(null);
  const formValuesRef = useRef(null);

  // Redux state
  const currentUser = useSelector(getCurrentUserData);

  // Full form validation schema
  const validationSchema = useMemo(
    () =>
      yup.object().shape({
        // Personal Information
        first_name: yup
          .string(REQUIRED_ERROR("First Name"))
          .required(REQUIRED_ERROR("First Name")),
        last_name: yup
          .string(REQUIRED_ERROR("Last Name"))
          .required(REQUIRED_ERROR("Last Name")),
        email: yup
          .string(REQUIRED_ERROR("Email"))
          .email("Enter valid email")
          .required(REQUIRED_ERROR("Email")),
        personal_email: yup
          .string(REQUIRED_ERROR("Personal Email"))
          .email("Enter valid email")
          .required(REQUIRED_ERROR("Personal Email")),
        mobile: yup
          .string(REQUIRED_ERROR("Mobile Number"))
          .typeError(REQUIRED_ERROR("Mobile Number"))
          .required(REQUIRED_ERROR("Mobile Number"))
          .test(
            "isValidPhoneNumber",
            "Please enter a valid mobile number",
            (val) => isValidPhoneNumber(val)
          ),
        birth_date: yup
          .date("Enter valid date")
          .max(dayjs(), "Can't be in future")
          .required(REQUIRED_ERROR("DOB")),
        gender: yup
          .string(REQUIRED_ERROR("Gender"))
          .required(REQUIRED_ERROR("Gender")),
        blood_group: yup
          .string(REQUIRED_ERROR("Blood Group"))
          .required(REQUIRED_ERROR("Blood Group")),
        spouse_full_name: yup.string().nullable(),
        anniversary_date: yup.date("Enter valid date").nullable(),

        // Contact Information
        address: yup.string().required(REQUIRED_ERROR("Address")),
        emergency_contact_num: yup
          .string(REQUIRED_ERROR("Emergency Contact"))
          .typeError(REQUIRED_ERROR("Emergency Contact"))
          .required(REQUIRED_ERROR("Emergency Contact"))
          .test(
            "isValidPhoneNumber",
            "Please enter a valid emergency contact",
            (val) => isValidPhoneNumber(val)
          ),
        emergency_contact_relation: yup
          .string(REQUIRED_ERROR("Relation"))
          .required(REQUIRED_ERROR("Relation")),

        // Professional Information
        skills: yup
          .array()
          .of(yup.string(REQUIRED_ERROR("Skill")))
          .min(1, "Select at least one skill")
          .typeError(REQUIRED_ERROR("Skill"))
          .required(REQUIRED_ERROR("Skills")),
        sme: yup.string().required(REQUIRED_ERROR("SME")),
        // EPF and UAN - conditional based on country
        epf: yup.string().nullable(),
        uan: yup.string().nullable(),
        career_start_date: yup
          .date("Enter valid date")
          .max(dayjs().endOf("day"), "Can't be in future")
          .required(REQUIRED_ERROR("Career Start")),
        reffered_by: yup.string().nullable(),
        introduction: yup
          .string()
          .test(
            "max-length",
            "Introduction cannot exceed 500 characters",
            (value) => {
              if (!value) return true;
              const textContent = value.replace(/<[^>]*>/g, "");
              return textContent.length <= 500;
            }
          )
          .nullable(),

        // Documents - conditional based on country
        aadhar_card:
          country === "US"
            ? yup.string().nullable()
            : yup.string().required(REQUIRED_ERROR("Aadhar")),
        pan_card:
          country === "US"
            ? yup.string().nullable()
            : yup.string().required(REQUIRED_ERROR("PAN")),
        passport_photo: yup.string().required(REQUIRED_ERROR("Passport")),
        profile_pic: yup.string().required(REQUIRED_ERROR("Profile Pic")),

        // Social Media
        facebook_link: yup.string().url("Please enter a valid URL.").nullable(),
        linkedin_link: yup.string().url("Please enter a valid URL.").nullable(),
        twitter_link: yup.string().url("Please enter a valid URL.").nullable(),
        instagram_link: yup
          .string()
          .url("Please enter a valid URL.")
          .nullable(),
      }),
    [country]
  );

  const yupSync = useMemo(
    () => ({
      async validator({ field }, value) {
        await validationSchema.validateSyncAt(field, { [field]: value });
      },
    }),
    [validationSchema]
  );

  // Form step configuration
  const stepItems = useMemo(
    () => [
      {
        title: "Personal",
        icon: <UserOutlined />,
        fields: [
          "first_name",
          "last_name",
          "email",
          "personal_email",
          "mobile",
          "birth_date",
          "gender",
          "address",
          "emergency_contact_num",
          "emergency_contact_relation",
          "spouse_full_name",
          ...(country !== "US" ? ["blood_group", "anniversary_date"] : []),
        ],
        content: (
          <PersonalInfo
            yupSync={yupSync}
            country={country}
            form={form}
            setCountry={setCountry}
          />
        ),
      },
      {
        title: "Professional",
        icon: <CodeSandboxOutlined />,
        fields:
          country === "US"
            ? [
                "skills",
                "sme",
                "career_start_date",
                "reffered_by",
                "introduction",
              ]
            : [
                "skills",
                "sme",
                "epf",
                "uan",
                "career_start_date",
                "reffered_by",
                "introduction",
              ],
        content: (
          <EmployeeInfo form={form} yupSync={yupSync} country={country} />
        ),
      },
      {
        title: "Documents",
        icon: <DropboxOutlined />,
        fields:
          country === "US"
            ? ["passport_photo", "profile_pic"]
            : ["aadhar_card", "pan_card", "passport_photo", "profile_pic"],
        content: (
          <DocumentForm
            documentUrls={documentUrls}
            setDocumentUrls={setDocumentUrls}
            form={form}
            yupSync={yupSync}
            country={country}
          />
        ),
      },
      {
        title: "Social",
        icon: <InstagramOutlined />,
        fields: [
          "facebook_link",
          "linkedin_link",
          "twitter_link",
          "instagram_link",
        ],
        content: <SocialForm yupSync={yupSync} />,
      },
    ],
    [form, yupSync, documentUrls, country]
  );

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
      }
    };
  }, []);

  // Initialize component based on account status
  useEffect(() => {
    const initializeOnboarding = async () => {
      if (!currentUser?.email) return;

      try {
        const data = await getEmployeeDetails(currentUser.email);

        if (
          data?.account_status === "ONBOARDING_FORM" ||
          data?.account_status === "ONBOARDING_PENDING"
        ) {
          // User has already entered email, show form steps
          setEmployeeData(data);
          if (data.documentUrls) setDocumentUrls(data.documentUrls);
          setHasPrefilledData(true);
          // setCurrentStep(0);
          form.setFieldsValue(data);
          lastSavedValuesRef.current = data;
          formValuesRef.current = data;
          setCountry(data.country || "IN");
        } else {
          form.setFieldValue("country", "IN");
        }
      } catch (error) {
        message.error("Failed to load employee data");
      } finally {
        setInitializing(false); // Mark initialization as complete
      }
    };

    initializeOnboarding();
  }, [currentUser?.email, form]);

  /**
   * Fetches candidate details by email
   * @param {Object} formData - Contains email field
   */
  const fetchCandidateDetails = async ({ email }) => {
    try {
      setLoading(true);
      const response = await getRestAPIData(
        `/api/candidate-details?email=${email}`
      );

      if (response) {
        // Remove email field to avoid form conflicts
        const { email: _, ...cleanData } = response;
        const employeeData = { ...cleanData, personal_email: email };
        setEmployeeData(employeeData);
        await updateEmployeeDetails({
          email: currentUser?.email,
          account_status: "ONBOARDING_FORM",
        });
        message.success("Found your information! Please review the details.");
        lastSavedValuesRef.current = employeeData;
        formValuesRef.current = employeeData;
      } else {
        message.info(
          "No existing information found. Please complete manually."
        );
      }

      setHasPrefilledData(true);
      setCurrentStep(0);
    } catch (error) {
      setHasPrefilledData(true);
      setCurrentStep(0);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Validates and moves to next step
   */
  const handleNext = async () => {
    try {
      const currentFields = stepItems[currentStep].fields;
      await form.validateFields(currentFields);
      setCurrentStep((prev) => Math.min(prev + 1, stepItems.length - 1));
      window.scrollTo(SCROLL_OPTIONS);
    } catch {
      message.error("Please complete all required fields.");
    }
  };

  /**
   * Returns to previous step
   */
  const handlePrev = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
    window.scrollTo(SCROLL_OPTIONS);
  };

  /**
   * Submits the completed form
   */
  const handleSubmit = async (valuesForm) => {
    try {
      setLoading(true);
      await form.validateFields();

      // Parse existing metadata if available
      let existingMetadata = {};
      if (employeeData?.metadata) {
        try {
          existingMetadata = JSON.parse(employeeData.metadata);
        } catch (e) {
          existingMetadata = {};
        }
      }

      const metaData = {
        ...existingMetadata,
        account_onboarding_status: employeeData?.account_onboarding_resubmitted
          ? "RESUBMITTED"
          : "PENDING",
      };
      const stringifiedMetadata = JSON.stringify(metaData);

      // Filter out null and undefined values
      const cleanedValues = Object.entries(valuesForm).reduce(
        (acc, [key, value]) => {
          if (value !== null && value !== undefined) {
            // Also skip empty strings
            if (typeof value === "string" && value.trim() === "") {
              return acc;
            }
            acc[key] = value;
          }
          return acc;
        },
        {}
      );

      await updateEmployeeDetails({
        ...cleanedValues,
        email: currentUser?.email,
        metadata: stringifiedMetadata,
        account_status: "PENDING_APPROVAL",
        country: country,
      });

      const data = await getEmployeeDetails(currentUser?.email);
      if (data) {
        dispatch(SetEmployeeDetails(data));
      }

      message.success("Profile submitted successfully!");
    } catch (error) {
      console.error("Submit error:", error);
      message.error("Please fix all errors before submitting.");
    } finally {
      setLoading(false);
    }
  };

  // Auto-save valid fields when form has changed
  const autoSaveValidFields = useCallback(async () => {
    if (currentStep === null || isAutoSaving || !formChanged) return;

    try {
      setIsAutoSaving(true);
      const currentFields = stepItems[currentStep].fields;
      const currentValues = form.getFieldsValue(currentFields);

      const payload = {};
      let hasValidFields = false;

      for (const field of currentFields) {
        try {
          await validationSchema.validateAt(field, currentValues);
          // Only include fields that have non-null, non-undefined values
          if (
            currentValues[field] !== null &&
            currentValues[field] !== undefined
          ) {
            // For empty strings, also skip them
            if (
              typeof currentValues[field] === "string" &&
              currentValues[field].trim() === ""
            ) {
              continue;
            }
            payload[field] = currentValues[field];
            hasValidFields = true;
          }
        } catch {
          // Skip invalid fields
        }
      }

      if (hasValidFields) {
        await updateEmployeeDetails({
          ...payload,
          email: currentUser?.email,
          country: country,
        });
        lastSavedValuesRef.current = currentValues;
        setFormChanged(false);
        message.success("Progress auto-saved", 1.5);
      }
    } catch (error) {
      console.error("Auto-save error:", error);
    } finally {
      setIsAutoSaving(false);
    }
  }, [
    currentStep,
    isAutoSaving,
    formChanged,
    stepItems,
    form,
    validationSchema,
    currentUser?.email,
    country,
  ]);

  // Setup auto-save interval
  useEffect(() => {
    if (currentStep !== null) {
      autoSaveIntervalRef.current = setInterval(
        autoSaveValidFields,
        AUTO_SAVE_INTERVAL
      );
      return () => clearInterval(autoSaveIntervalRef.current);
    }
  }, [currentStep, autoSaveValidFields]);

  // Track form changes
  const handleFormChange = useCallback(() => {
    if (currentStep === null) return;

    const currentFields = stepItems[currentStep].fields;
    const currentValues = form.getFieldsValue(currentFields);

    if (!deepEqual(currentValues, formValuesRef.current)) {
      formValuesRef.current = currentValues;
      setFormChanged(true);
    }
  }, [currentStep, form, stepItems]);

  // Simple deep equality check
  const deepEqual = (obj1, obj2) => {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  };

  // Show loader until initialization is complete
  if (initializing) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar onlyLogo />
        <Background />
        <main className="container mx-auto px-4 py-8 flex-grow flex items-center justify-center">
          <Loader />
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar onlyLogo />
      <Background />

      <main className="container mx-auto px-4 py-8 flex-grow flex flex-col items-center">
        <div className="w-full max-w-4xl bg-white rounded-lg shadow-md p-6">
          {loading ? (
            <Loader />
          ) : (
            <>
              {/* Multi-step Form Header */}
              <header className="mb-6 text-center">
                <h1 className="text-2xl font-bold text-primary">
                  Complete Your Profile
                </h1>
                <p className="text-gray-600">
                  Step {currentStep + 1} of {stepItems.length}
                </p>
              </header>

              {/* Progress Steps */}
              <Steps
                current={currentStep}
                items={stepItems}
                responsive
                className="mb-6"
              />
              <Divider className="my-4" />

              {/* Form Content */}
              <CustomForm
                form={form}
                layout="vertical"
                onFieldsChange={handleFormChange}
                onFinish={handleSubmit}
              >
                {stepItems.map((step, index) => (
                  <div
                    key={index}
                    style={{
                      display: index === currentStep ? "block" : "none",
                    }}
                  >
                    {step.content}
                  </div>
                ))}

                {/* Navigation Buttons */}
                <div className="flex justify-between mt-8">
                  <Button
                    onClick={handlePrev}
                    disabled={currentStep === 0 || loading}
                    icon={<ArrowLeftOutlined />}
                  >
                    Previous
                  </Button>

                  {currentStep < stepItems.length - 1 ? (
                    <Button
                      type="primary"
                      onClick={handleNext}
                      disabled={loading}
                      icon={<ArrowRightOutlined />}
                    >
                      Next
                    </Button>
                  ) : (
                    <Button
                      type="primary"
                      onClick={() => form.submit()}
                      loading={loading}
                      icon={<SaveOutlined />}
                    >
                      Submit Profile
                    </Button>
                  )}
                </div>

                {/* Auto-save indicator */}
                {isAutoSaving && (
                  <div className="fixed top-4 right-4 bg-green-100 text-green-800 px-4 py-2 rounded-md shadow-md z-50">
                    Auto-saving your progress...
                  </div>
                )}
              </CustomForm>
            </>
          )}
        </div>
      </main>
    </div>
  );
};

export default UnifiedEmployeeOnboarding;
