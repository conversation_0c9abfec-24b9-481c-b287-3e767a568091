import { Tag, Typography } from "antd";
import { renderWithPopover } from "AtomicComponents/renderWithPopover";

export const OnBoardingTextDesignView = (title, value) => {
  return (
    <div className="profile-item-personal mb-2 lg:mb-4 break-words">
      <div>
        <Typography.Text className="text-gray-400 font-semibold whitespace-normal">
          {title}
        </Typography.Text>
      </div>
      <div className="mt-0.5">
        {Array.isArray(value) ? (
          value.length > 0 ? (
            <div className="flex flex-wrap gap-1">
              {renderWithPopover(value, 4, ({ item }) => (
                <Tag className="border-gray-500 my-1">{item}</Tag>
              ))}
            </div>
          ) : (
            <Typography.Text className="whitespace-normal">
              None
            </Typography.Text>
          )
        ) : (
          <Typography.Text className="whitespace-normal break-words">
            {value || "None"}
          </Typography.Text>
        )}
      </div>
    </div>
  );
};
export const renderLabelValuePair = (label, value) => {
  return (
    <div className="flex flex-col justify-center bg-gray-100 py-3 px-2 w-full rounded-md gap-1">
      <span className="text-sm font-medium text-gray-700 mr-1">{label}</span>
      <span className="text-sm text-gray-900">{value || "--"}</span>
    </div>
  );
};
