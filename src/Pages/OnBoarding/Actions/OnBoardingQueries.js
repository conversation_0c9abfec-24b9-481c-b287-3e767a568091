export const listEmployeeHROnboarding = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listEmployees(
      email: $email
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        email
        employee_id
        account_status
        first_name
        last_name
        country
        title {
          id
          name
          level
          overall_level
          min_experience
          max_experience
          createdAt
          updatedAt
          __typename
        }
        mobile
        reward_points
        slack_id
        blood_group
        emergency_contact_num
        emergency_contact_relation
        married
        spouse_full_name
        reporting_to {
          email
          employee_id
          account_status
          first_name
          last_name

          __typename
        }
        skills {
          items {
            id
            employeeID
            skillID
            createdAt
            updatedAt
          }
          nextToken
          __typename
        }
        SME {
          items {
            sMEID
          }
          nextToken
          __typename
        }
        pan_card
        aadhar_card
        form12BB
        experience_letter
        passport_photo
        address_proof
        resignation_letter
        salary_slip
        salary_slip_1
        salary_slip_2
        personal_email
        york_appointment
        york_agreement
        reffered_by
        address
        squad {
          name
          budget
          active
          createdAt
          updatedAt
          productUnitSquadId
          squadSquad_managerId
          __typename
        }
        guild {
          name
          active
          createdAt
          updatedAt
          guildGuild_managerId
          __typename
        }
        birth_date
        anniversary_date
        career_start_date
        york_start_date
        york_end_date
        profile_pic
        profile_pic_requested
        active
        documents
        gender
        facebook_link
        linkedin_link
        twitter_link
        instagram_link
        usual_starting_time
        epf
        uan
        employee_project_allocation {
          nextToken
          __typename
        }
        hidden_profile
        interviews {
          nextToken
          __typename
        }
        isInterviewer
        zoomMeetingsHosted {
          nextToken
          __typename
        }
        zoomMeetingsParticipated {
          nextToken
          __typename
        }
        projectMeetings {
          nextToken
          __typename
        }
        day_start_time
        day_end_time
        allocatedDesk
        wishlist
        projectPasswords {
          nextToken
          __typename
        }
        kudos {
          nextToken
          __typename
        }
        introduction
        configurations
        createdAt
        updatedAt
        squadEmployeeId
        guildEmployeeId
        employeeTitleId
        employeeReporting_toId
        employeeSquadId
        employeeGuildId
        metadata
        __typename
      }
      nextToken
      __typename
    }
  }
`;

export const listDesignationOnboarding = /* GraphQL */ `
  query ListDesignations(
    $filter: ModelDesignationFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listDesignations(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        level
        overall_level
      }
      nextToken
    }
  }
`;
export const listSquadsOnboarding = /* GraphQL */ `
  query ListSquads(
    $filter: ModelSquadFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listSquads(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        name
        active
        squad_manager {
          email
          employee_id
          first_name
          last_name
        }
        __typename
      }
      nextToken
    }
  }
`;
export const listGuildsOnboarding = /* GraphQL */ `
  query ListGuilds(
    $filter: ModelGuildFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listGuilds(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        name
        active
        guild_manager {
          email
          employee_id
          first_name
          last_name
        }
        __typename
      }
      nextToken
    }
  }
`;
export const listEmployeesOnboarding = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listEmployees(
      email: $email
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        email
        employee_id
        first_name
        last_name
        active
        hidden_profile
        profile_pic
        __typename
      }
      nextToken
    }
  }
`;
export const updateEmployeeForOnboarding = /* GraphQL */ `
  mutation UpdateEmployee(
    $input: UpdateEmployeeInput!
    $condition: ModelEmployeeConditionInput
  ) {
    updateEmployee(input: $input, condition: $condition) {
      email
    }
  }
`;
export const getEmployeeOnboarding = /* GraphQL */ `
  query GetEmployee($email: String!) {
    getEmployee(email: $email) {
      email
      employee_id
      first_name
      last_name
      account_status
      active
      country
      __typename
    }
  }
`;
