import { ExecuteMutationV2, ExecuteQueryCustomV2 } from "utils/Api";
import {
  getEmployeeOnboarding,
  listDesignationOnboarding,
  listEmployeeHROnboarding,
  listEmployeesOnboarding,
  listGuildsOnboarding,
  listSquadsOnboarding,
  updateEmployeeForOnboarding,
} from "Pages/OnBoarding/Actions/OnBoardingQueries";

export const listEmployeeHROnboardingAction = (filter) => {
  return ExecuteQueryCustomV2(listEmployeeHROnboarding, { filter });
};
export const listDesignationOnboardingAction = () => {
  return ExecuteQueryCustomV2(listDesignationOnboarding);
};
export const listSquadsOnboardingAction = (filter) => {
  return ExecuteQueryCustomV2(listSquadsOnboarding, { filter });
};
export const listGuildsOnboardingAction = (filter) => {
  return ExecuteQueryCustomV2(listGuildsOnboarding, { filter });
};
export const listEmployeesOnboardingAction = (filter) => {
  return ExecuteQueryCustomV2(listEmployeesOnboarding, { filter });
};
export const updateEmployeeForOnboardingAction = async (input) => {
  return ExecuteMutationV2(updateEmployeeForOnboarding, { input });
};
export const getEmployeeOnboardingAction = async (email) => {
  return ExecuteQueryCustomV2(getEmployeeOnboarding, { email });
};
