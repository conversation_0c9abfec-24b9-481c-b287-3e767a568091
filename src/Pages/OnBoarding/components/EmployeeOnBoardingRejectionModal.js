import React, { memo, useEffect, useState } from "react";
import { Button, Form, Input, message, Modal } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { updateEmployeeForOnboardingAction } from "Pages/OnBoarding/Actions/OnBoardingAction";

const { TextArea } = Input;

const EmployeeOnBoardingRejectionModal = memo(({ employee, onLocalReject }) => {
  // State to manage form and loading state
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  //useEffect to set the modal visibility based on employee prop
  useEffect(() => {
    if (employee) {
      setIsVisible(true);
    }
  }, [employee]);

  // Function to handle form submission
  const handleOk = async () => {
    try {
      setConfirmLoading(true);
      const values = form.getFieldsValue();
      console.log("values", values);
      const input = {
        email: employee?.email,
        account_status: "ONBOARDING_FORM",
        metadata: JSON.stringify({
          rejectionReason: values?.rejectionReason,
          account_onboarding_status: "REJECTED",
          account_onboarding_resubmitted: true,
        }),
      };
      await updateEmployeeForOnboardingAction(input);
      message.success("Onboarding rejected successfully!");
      // Call the local reject callback
      if (onLocalReject) {
        onLocalReject(employee, values?.rejectionReason);
      }
    } catch (error) {
      console.error("Validation failed:", error);
      message.error("Please provide a reason for rejection");
    } finally {
      setConfirmLoading(false);
      setIsVisible(false);
      form.resetFields();
    }
  };

  // Function to handle modal cancellation
  const handleCancel = () => {
    form.resetFields();
    setIsVisible(false);
  };

  return (
    // Modal component for rejecting onboarding
    <Modal
      title={<span>Reject Onboarding</span>}
      open={isVisible}
      onCancel={handleCancel}
      width={"60%"}
      footer={[
        <Button key="back" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button
          key="submit"
          type="primary"
          danger
          loading={confirmLoading}
          onClick={handleOk}
          icon={<CloseOutlined />}
        >
          Reject
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical">
        <div className="mb-4">
          Are you sure you want to reject the onboarding application for{" "}
          <strong>
            {employee?.first_name} {employee?.last_name}
          </strong>
          ?
        </div>
        <Form.Item
          name="rejectionReason"
          label="Reason for Rejection"
          rules={[
            {
              required: true,
              message: "Please provide a reason for rejection",
            },
            {
              min: 10,
              message: "Reason should be at least 10 characters",
            },
          ]}
        >
          <TextArea
            rows={4}
            placeholder="Please provide a detailed reason for rejection..."
            maxLength={500}
            showCount
          />
        </Form.Item>
      </Form>
    </Modal>
  );
});

export default EmployeeOnBoardingRejectionModal;
