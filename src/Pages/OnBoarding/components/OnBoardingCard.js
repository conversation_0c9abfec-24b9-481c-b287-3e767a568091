import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Divider } from "antd";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import moment from "moment";
import EmployeeOnBoardingModal from "Pages/OnBoarding/components/EmployeeOnBoardingModal";
import EmployeeOnBoardingRejectionModal from "Pages/OnBoarding/components/EmployeeOnBoardingRejectionModal";
import { renderLabelValuePair } from "Pages/OnBoarding/utils/common";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { DateFormatWithTime } from "utils/constants";
import { boxClass } from "utils/TailwindCommonClasses";

const OnBoardingCard = React.memo(
  ({ employee, sectionType, onLocalReject }) => {
    const navigate = useNavigate();
    //state
    const [selectedEmployee, setSelectedEmployee] = useState(null);
    const [typeOfModal, setTypeOfModal] = useState(null);

    // Determine if buttons should be disabled based on section type
    const isActionDisabled =
      sectionType === "Upcoming Approvals" || sectionType === "New Employees";

    //useEffect
    const handleViewDetails = () => {
      setTypeOfModal("view");
      setSelectedEmployee(null);
      setTimeout(() => {
        setSelectedEmployee(employee);
      }, 0);
    };

    //function to handle approve  actions
    const handleApprove = (employee) => {
      //redirect to onboarding approve page
      console.log("employee", employee);
      navigate(`/hr-onboarding-approve/${employee?.email?.split("@")[0]}`, {
        state: { employeeData: employee },
      });
    };
    //function to handle reject actions
    const handleReject = (employee) => {
      console.log("employee 1", employee);
      setTypeOfModal("reject");
      setSelectedEmployee(null);

      setTimeout(() => {
        setSelectedEmployee(employee);
      }, 0);
    };

    //This function is used to convert a string to title case
    const toTitleCase = (str) =>
      str.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());

    return (
      <div
        className={`${boxClass} p-4  hover:shadow-md  transition-shadow border border-gray-100 mb-4`}
      >
        <div className="flex flex-col gap-2">
          {/* Employee Name */}
          <div className="flex items-center justify-between gap-4">
            {employee?.first_name ? (
              <RenderEmployeeFullName
                employee={employee}
                noRedirect={true}
                className="text-lg font-semibold gap-2 mb-0"
                showAvatar
              />
            ) : (
              <div className="text-lg font-semibold text-yellow-600">
                User has not yet filled the form
              </div>
            )}
          </div>

          {/* Contact Info */}
          <div className="flex  gap-8 ">
            {renderLabelValuePair("Email", employee.email)}
            {renderLabelValuePair("Mobile", employee.mobile)}
            {renderLabelValuePair(
              "Account Status",
              employee.account_status
                ? toTitleCase(employee.account_status)
                : "--"
            )}{" "}
            {renderLabelValuePair(
              "Submitted On",
              moment(employee.updatedAt).format(DateFormatWithTime)
            )}
          </div>

          <Divider className="m-0" />
          {/* Action Buttons */}
          <div className="flex justify-between gap-2  -mb-1">
            <div className="-ml-2">
              <Button
                type="link"
                onClick={() => handleViewDetails(employee)}
                className="flex-1"
              >
                View Details
              </Button>
            </div>
            {!isActionDisabled && (
              <div className="flex gap-2">
                <Button
                  type="primary"
                  icon={<CheckOutlined />}
                  onClick={() => {
                    console.log("here");
                    handleApprove(employee);
                  }}
                  className="flex-1 "
                  disabled={isActionDisabled}
                >
                  Approve
                </Button>
                <Button
                  danger
                  icon={<CloseOutlined />}
                  onClick={() => handleReject(employee)}
                  className="flex-1"
                  disabled={isActionDisabled}
                >
                  Reject
                </Button>
              </div>
            )}
            {typeOfModal === "view" && (
              <EmployeeOnBoardingModal selectedEmployee={selectedEmployee} />
            )}
            {typeOfModal === "reject" && (
              <EmployeeOnBoardingRejectionModal
                employee={selectedEmployee}
                onLocalReject={onLocalReject}
              />
            )}
          </div>
        </div>
      </div>
    );
  }
);

export default OnBoardingCard;
