import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mo<PERSON>, <PERSON>, Typography } from "antd";
import { OnBoardingTextDesignView } from "Pages/OnBoarding/utils/common";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import moment from "moment";
import { DateFormat } from "utils/constants";
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  CodeSandboxOutlined,
  DownloadOutlined,
  DropboxOutlined,
  FacebookOutlined,
  FilePdfOutlined,
  InstagramOutlined,
  LinkedinOutlined,
  UserOutlined,
  XOutlined,
} from "@ant-design/icons";
import { downloadFileFromS3 } from "Pages/Profile/function/uploadFile";
import { getPresignedUrl } from "utils/helperFunction";
import { boxClass } from "utils/TailwindCommonClasses";

const EmployeeOnBoardingModal = React.memo(({ selectedEmployee }) => {
  // state
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [documentUrls, setDocumentUrls] = useState({});

  // useEffect
  useEffect(() => {
    if (selectedEmployee) {
      setIsModalVisible(true);
      setCurrentStep(0);
      loadDocumentUrls();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedEmployee]);

  // Load presigned URLs for documents when employee is selected
  const loadDocumentUrls = async () => {
    if (!selectedEmployee) return;

    const urls = {};
    const documentFields = [
      "aadhar_card",
      "pan_card",
      "passport_photo",
      "profile_pic",
    ];

    for (const field of documentFields) {
      if (selectedEmployee[field]) {
        try {
          const url = await getPresignedUrl({ key: selectedEmployee[field] });
          urls[field] = url;
        } catch (error) {
          console.error(`Error getting presigned URL for ${field}:`, error);
        }
      }
    }

    setDocumentUrls(urls);
  };

  // Function to get the value inside an object using a string path
  const getValueInside = useCallback((rowName, item) => {
    return (
      item.split(" ").reduce((acc, item) => acc && acc[item], rowName) ?? null
    );
  }, []);

  //  This will be in personal info section
  const personalInfoFields = useMemo(
    () => [
      { key: "first_name", label: "First Name" },
      { key: "last_name", label: "Last Name" },
      { key: "email", label: "Email" },
      {
        key: "birth_date",
        label: "Date of Birth",
        transform: (value) =>
          value ? moment(value).format(DateFormat) : "None",
      },
      { key: "gender", label: "Gender" },
      { key: "blood_group", label: "Blood Group" },
    ],
    []
  );

  // This will be in contact details section
  const contactDetailsFields = useMemo(
    () => [
      { key: "address", label: "Address", fullWidth: true },
      { key: "mobile", label: "Mobile Number" },
      { key: "personal_email", label: "Personal Email" },
      { key: "emergency_contact_num", label: "Emergency Contact Number" },
      {
        key: "emergency_contact_relation",
        label: "Emergency Contact Relation",
      },
    ],
    []
  );
  // This will be in family info section
  const familyInfoFields = useMemo(
    () => [
      { key: "spouse_full_name", label: "Spouse Full Name" },
      {
        key: "anniversary_date",
        label: "Anniversary Date",
        transform: (value) =>
          value ? moment(value).format(DateFormat) : "None",
      },
    ],
    []
  );

  //this will be in employee info section
  const professionalInfoFields = useMemo(
    () => [
      {
        key: "skills",
        label: "Skills",
        customAccessor: true,
        transform: (_, employee) => {
          // Access skills directly from the employee object
          if (employee && employee.skills && employee.skills.items) {
            return employee.skills.items.map((item) => item.skillID);
          }
          return []; // Return empty array if no skills
        },
      },
      {
        key: "sme",
        label: "Subject Matter Expertise",
        customAccessor: true,
        transform: (_, employee) => {
          // Access SME directly from the employee object
          if (employee && employee.SME && employee.SME.items) {
            return employee.SME.items.map((item) => item.sMEID);
          }
          return []; // Return empty array if no SME
        },
      },
      { key: "epf", label: "EPF Account Number" },
      { key: "uan", label: "UAN Number" },
    ],
    []
  );

  // This will be in employment history section
  const employmentHistoryFields = useMemo(
    () => [
      {
        key: "career_start_date",
        label: "Career Start Date",
        transform: (value) =>
          value ? moment(value).format(DateFormat) : "None",
      },
      { key: "reffered_by", label: "Referred By" },
    ],
    []
  );
  // This will be in employee bio section
  const employeeIntroductionFields = useMemo(
    () => [
      {
        key: "introduction",
        label: "Introduction",
        fullWidth: true,
        // Add a custom render function to safely display HTML content
        customRender: (value) => (
          <div
            dangerouslySetInnerHTML={{ __html: value }}
            className="html-content"
          />
        ),
      },
    ],
    []
  );
  // Social media links
  const socialMediaFields = useMemo(
    () => [
      {
        key: "linkedin_link",
        label: "LinkedIn",
        icon: <LinkedinOutlined className="text-blue-700" />,
      },
      {
        key: "facebook_link",
        label: "Facebook",
        icon: <FacebookOutlined className="text-blue-600" />,
      },
      {
        key: "instagram_link",
        label: "Instagram",
        icon: <InstagramOutlined className="text-pink-600" />,
      },
      {
        key: "twitter_link",
        label: "X (Twitter)",
        icon: <XOutlined />,
      },
    ],
    []
  );

  // Render profile items
  const renderProfileItems = useCallback(
    (profileItem, columnTake = 2) => {
      const rows = [];
      let currentRow = [];

      // Iterate through the profile items
      profileItem.forEach((currentField, index) => {
        if (currentField.fullWidth) {
          if (currentRow.length > 0) {
            rows.push(currentRow);
            currentRow = [];
          }
          rows.push([currentField]);
        } else {
          currentRow.push(currentField);
          if (
            currentRow.length === columnTake ||
            index === profileItem.length - 1
          ) {
            rows.push(currentRow);
            currentRow = [];
          }
        }
      });
      // If there are any remaining fields in the current row, add them to rows
      return rows.map((row, rowIndex) => (
        <div key={rowIndex} className="flex flex-wrap">
          {row.map((field) => {
            // For fields with custom accessors, skip getValueInside logic
            let value = field.customAccessor
              ? null
              : getValueInside(selectedEmployee, field.key);

            // Handle object values that can't be rendered directly
            if (
              value &&
              typeof value === "object" &&
              !React.isValidElement(value)
            ) {
              // Check if it's the problematic object with nextToken/__typename
              if (
                value.nextToken !== undefined &&
                value.__typename !== undefined
              ) {
                value = "Data available"; // Replace with a simple string
              } else if (Array.isArray(value)) {
                // If it's an array, join it
                value = value.join(", ");
              } else {
                // For other objects, convert to string representation
                try {
                  value = JSON.stringify(value);
                } catch (e) {
                  value = "Complex data";
                }
              }
            }

            // Apply any transformation function
            if (field.transform) {
              try {
                // Pass both the value and the full employee object for fields that need it
                value = field.customAccessor
                  ? field.transform(value, selectedEmployee)
                  : field.transform(value);
              } catch (e) {
                console.error(`Error transforming ${field.key}:`, e);
                value = "Error displaying data";
              }
            }
            // Render the field with its label and value
            return (
              <div
                key={field.key}
                className={`${
                  field.fullWidth ? "w-full" : "w-full sm:w-1/2"
                } px-1`}
              >
                {field.customRender
                  ? OnBoardingTextDesignView(
                      field.label,
                      field.customRender(
                        getValueInside(selectedEmployee, field.key)
                      )
                    )
                  : OnBoardingTextDesignView(field.label, value)}
              </div>
            );
          })}
        </div>
      ));
    },
    [selectedEmployee, getValueInside]
  );
  // Render social media links
  const renderSocialLinks = useCallback(() => {
    if (!selectedEmployee) return null;

    const availableSocialLinks = socialMediaFields.filter(
      (field) => selectedEmployee[field.key]
    );
    // Check if there are any available social links
    if (availableSocialLinks.length === 0) {
      return (
        <Typography.Text className="text-gray-500 italic">
          No social media links available
        </Typography.Text>
      );
    }
    // Render the social media links
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {availableSocialLinks.map((field) => (
          <div key={field.key} className={`${boxClass} flex items-center`}>
            <div className="flex items-center gap-2">
              {field.icon}
              <Typography.Text strong>{field.label}:</Typography.Text>
            </div>
            <Typography.Link
              href={selectedEmployee[field.key]}
              target="_blank"
              className="ml-2 truncate flex-1"
            >
              {selectedEmployee[field.key]}
            </Typography.Link>
          </div>
        ))}
      </div>
    );
  }, [selectedEmployee, socialMediaFields]);

  // Document section configuration
  const documentConfig = useMemo(
    () => ({
      aadhar_card: { label: "Aadhar Card" },
      pan_card: { label: "PAN Card" },
      passport_photo: { label: "Passport Photo" },
      profile_pic: { label: "Profile Picture" },
    }),
    []
  );

  // Download document handler
  const downloadFile = async (fileKey) => {
    try {
      let fileResponse = await downloadFileFromS3(fileKey);
      const url = URL.createObjectURL(fileResponse.Body);
      const a = document.createElement("a");
      a.href = url;
      a.download = fileKey.split("/").pop();
      a.click();
      setTimeout(() => URL.revokeObjectURL(url), 150);
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  // View document handler
  const viewDocument = (url) => {
    window.open(url, "_blank");
  };

  // Render document items
  const renderDocuments = useCallback(() => {
    if (!selectedEmployee) return null;
    // Filter available documents based on selected employee
    const availableDocuments = Object.keys(documentConfig)
      .filter((key) => selectedEmployee[key])
      .map((key) => ({
        key,
        label: documentConfig[key].label,
        fileKey: selectedEmployee[key],
        url: documentUrls[key],
      }));
    // Check if there are any available documents
    if (availableDocuments.length === 0) {
      return (
        <Typography.Text className="text-gray-500 italic">
          No documents available
        </Typography.Text>
      );
    }
    // Render the available documents
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {availableDocuments.map((doc) => (
          <div
            key={doc.key}
            className={`${boxClass} flex justify-between items-center`}
          >
            <div className="flex flex-col justify-center gap-1">
              <Typography.Text strong class>
                {doc.label}
              </Typography.Text>
              <span
                onClick={() => viewDocument(doc.url)}
                className="text-blue-500 cursor-pointer"
              >
                <FilePdfOutlined /> View Document
              </span>
            </div>

            <DownloadOutlined
              icon={<DownloadOutlined />}
              onClick={() => downloadFile(doc.fileKey)}
              className="mr-2"
            >
              Download
            </DownloadOutlined>
          </div>
        ))}
      </div>
    );
  }, [documentUrls, selectedEmployee, documentConfig]);

  // Define the major steps with their content
  const steps = useMemo(
    () => [
      {
        title: "Personal",
        icon: <UserOutlined />,
        content: (
          <div className="space-y-4">
            {/* Personal Information Section */}
            <div>
              <Typography.Title level={5}>
                Personal Information
              </Typography.Title>
              {renderProfileItems(personalInfoFields)}
            </div>

            {/* Contact Details Section */}
            <div>
              <Divider className="m-0 p-0" />
              <Typography.Title level={5} className="mt-3">
                Contact Details
              </Typography.Title>
              {renderProfileItems(contactDetailsFields)}
            </div>

            {/* Family Information Section */}
            <div>
              <Divider className="m-0 p-0" />
              <Typography.Title level={5} className="mt-3">
                Family Information
              </Typography.Title>
              {renderProfileItems(familyInfoFields)}
            </div>
          </div>
        ),
      },
      {
        title: "Professional",
        icon: <CodeSandboxOutlined />,
        content: (
          <div className="space-y-4">
            {/* Professional Info Section */}
            <div>
              <Typography.Title level={5}>
                Professional Information
              </Typography.Title>
              {renderProfileItems(professionalInfoFields)}
            </div>

            {/* Employment History Section */}
            <div>
              <Divider className="m-0 p-0" />
              <Typography.Title level={5} className="mt-3">
                Employment History
              </Typography.Title>
              {renderProfileItems(employmentHistoryFields)}
            </div>
            <div>
              <Divider className="m-0 p-0" />
              <Typography.Title level={5} className="mt-3">
                Employee Bio
              </Typography.Title>
              {renderProfileItems(employeeIntroductionFields)}
            </div>
          </div>
        ),
      },
      {
        title: "Documents",
        icon: <DropboxOutlined />,
        content: (
          <div className="space-y-4">
            <Typography.Title level={5}>Employee Documents</Typography.Title>
            {renderDocuments()}
          </div>
        ),
      },
      {
        title: "Social",
        icon: <InstagramOutlined />,
        content: (
          <div className="space-y-4">
            <Typography.Title level={5}>Social Media Links</Typography.Title>
            {renderSocialLinks()}
          </div>
        ),
      },
    ],
    [
      renderProfileItems,
      personalInfoFields,
      contactDetailsFields,
      familyInfoFields,
      professionalInfoFields,
      employmentHistoryFields,
      renderDocuments,
      renderSocialLinks,
      employeeIntroductionFields,
    ]
  );

  //Handle Next Step
  const handleNext = useCallback(() => {
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  }, [steps]);

  //Handle Previous Step
  const handlePrevious = useCallback(() => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  }, []);

  return (
    <>
      <Modal
        title={`Onboarding Details for ${selectedEmployee?.first_name} ${selectedEmployee?.last_name}`}
        open={isModalVisible}
        onCancel={(e) => {
          e.preventDefault();
          setIsModalVisible(false);
        }}
        width={"60%"}
        style={{ top: 20 }}
        footer={
          <div className="flex justify-between">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={handlePrevious}
              disabled={currentStep === 0}
            >
              Previous
            </Button>
            <Button
              type="primary"
              icon={
                currentStep === steps.length - 1 ? null : <ArrowRightOutlined />
              }
              onClick={
                currentStep === steps.length - 1
                  ? () => setIsModalVisible(false)
                  : handleNext
              }
            >
              {currentStep === steps.length - 1 ? "Done" : "Next"}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          {/* Step Progress Indicator using Ant Design Steps */}
          <Steps
            current={currentStep}
            items={steps.map((step) => ({
              title: step.title,
              icon: step.icon,
            }))}
          />

          <Divider className="my-4" />

          {/* Display current step content */}
          {steps[currentStep].content}
        </div>
      </Modal>
    </>
  );
});

export default EmployeeOnBoardingModal;
