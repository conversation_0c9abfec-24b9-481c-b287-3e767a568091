import { FilterOutlined, SearchOutlined } from "@ant-design/icons";
import { Divider, Empty, Input, Select, Space } from "antd";
import PageHeader from "AtomicComponents/PageHeader";
import Loader from "Commons/Loader";
import CountryFilter from "Commons/CountryFilter";
import { listEmployeeHROnboardingAction } from "Pages/OnBoarding/Actions/OnBoardingAction";
import OnBoardingCard from "Pages/OnBoarding/components/OnBoardingCard";
import { filterEmployeesByCountry } from "utils/commonMethods";
import React, { useEffect, useState } from "react";

const HrOnBoardingDashboard = () => {
  //states
  const [onboardingEmployees, setOnboardingEmployees] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [countryFilter, setCountryFilter] = useState("All");
  const [filteredEmployees, setFilteredEmployees] = useState([]);

  //useEffect
  useEffect(() => {
    fetchPendingApprovalEmployee();
  }, []);

  useEffect(() => {
    applyFilters();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [onboardingEmployees, searchText, statusFilter, countryFilter]);

  const fetchPendingApprovalEmployee = async () => {
    try {
      const filter = {
        or: [
          { account_status: { eq: "ONBOARDING_PENDING" } },
          { account_status: { eq: "ONBOARDING_FORM" } },
          { account_status: { eq: "PENDING_APPROVAL" } },
        ],
      };
      setIsLoading(true);
      const fetchedOnboardedEmployees = await listEmployeeHROnboardingAction(
        filter
      );
      setOnboardingEmployees(fetchedOnboardedEmployees);
    } catch (error) {
      console.error("Error fetching pending approval employees:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to apply filters based on search text, status, and country
  const applyFilters = () => {
    let filtered = [...onboardingEmployees];
    console.log(
      "🚀 ~ applyFilters ~ onboardingEmployees:",
      onboardingEmployees
    );

    // Apply country filter first
    filtered = filterEmployeesByCountry(filtered, countryFilter);

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((emp) => {
        // Parse metadata if it's a string
        let metadata = emp.metadata;
        if (typeof metadata === "string") {
          try {
            metadata = JSON.parse(metadata);
          } catch (error) {
            console.error("Error parsing metadata:", error);
            return false;
          }
        }
        if (statusFilter === "RESUBMITTED") {
          return metadata?.account_onboarding_resubmitted === true;
        }
        return metadata?.account_onboarding_status === statusFilter;
      });
    }

    // Apply search filter (name or email)
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(
        (emp) =>
          emp.first_name?.toLowerCase().includes(searchLower) ||
          emp.last_name?.toLowerCase().includes(searchLower) ||
          emp.email?.toLowerCase().includes(searchLower)
      );
    }

    setFilteredEmployees(filtered);
  };

  // Group employees by their account status
  const groupEmployeesByStatus = (employees) => {
    const pendingApproval = employees.filter(
      (emp) => emp.account_status === "PENDING_APPROVAL"
    );
    const onboardingForm = employees.filter(
      (emp) => emp.account_status === "ONBOARDING_FORM"
    );
    const onboardingPending = employees.filter(
      (emp) => emp.account_status === "ONBOARDING_PENDING"
    );

    return { pendingApproval, onboardingForm, onboardingPending };
  };

  //When data is Loading
  if (isLoading) {
    return <Loader title="Loading Onboarding Employees..." />;
  }

  // Group the filtered employees by status
  const { pendingApproval, onboardingForm, onboardingPending } =
    groupEmployeesByStatus(filteredEmployees);

  // Section renderer for each group of employees
  const renderEmployeeSection = (
    title,
    employees,
    emptyMessage,
    description
  ) => {
    return (
      <div className="mb-6">
        <div className="mb-4">
          <h2 className="text-xl font-semibold mb-1">{title}</h2>
          <p className="text-gray-600 text-sm">{description}</p>
        </div>
        {employees.length === 0 ? (
          <Empty description={emptyMessage} className="p-4" />
        ) : (
          employees.map((employee, index) => (
            <OnBoardingCard
              key={employee.id || index}
              employee={employee}
              sectionType={title}
              onLocalReject={handleLocalReject} // <-- Pass the handler
            />
          ))
        )}
      </div>
    );
  };

  // Add this function to update local state on rejection
  const handleLocalReject = (rejectedEmployee, rejectionReason) => {
    setOnboardingEmployees((prev) =>
      prev.map((emp) =>
        emp.email === rejectedEmployee.email
          ? {
              ...emp,
              account_status: "ONBOARDING_FORM",
              metadata: JSON.stringify({
                ...JSON.parse(emp.metadata || "{}"),
                rejectionReason,
                account_onboarding_status: "REJECTED",
                account_onboarding_resubmitted: true,
              }),
            }
          : emp
      )
    );
  };

  return (
    <>
      {/* Page Header */}
      <PageHeader title="HR Onboarding Dashboard" onlyTitle={true} isBeta />
      <div className="flex flex-col md:flex-row justify-between gap-4 mb-4 mt-2">
        <div className="flex flex-1 items-center">
          <Input
            placeholder="Search by name or email"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="w-full md:w-1/2"
          />
        </div>
        <Space>
          <CountryFilter
            value={countryFilter}
            onChange={setCountryFilter}
            showLabel={true}
            size="default"
          />

          <div className="flex items-center gap-2">
            <FilterOutlined />
            <span>Status:</span>
            <Select
              defaultValue="all"
              style={{ width: 140 }}
              onChange={(value) => setStatusFilter(value)}
            >
              <Select.Option value="all">All</Select.Option>
              <Select.Option value="PENDING">Pending</Select.Option>
              <Select.Option value="RESUBMITTED">Resubmitted</Select.Option>
            </Select>
          </div>
        </Space>
      </div>

      {/* No employees at all case */}
      {filteredEmployees.length === 0 && (
        <div className="flex justify-center items-center">
          <Empty
            description={`No ${
              onboardingEmployees.length === 0 ? "" : "Filtered"
            } Onboarding Employees`}
            className="p-4"
          />
        </div>
      )}

      {/* Only render sections if we have filtered employees */}
      {filteredEmployees.length > 0 && (
        <>
          {/* Pending Approval Section */}
          {renderEmployeeSection(
            "Pending Approvals",
            pendingApproval,
            "No employees pending approval",
            "Employees who have completed the onboarding form and are awaiting HR review."
          )}

          {/* Divider between sections */}
          {pendingApproval.length > 0 &&
            (onboardingForm.length > 0 || onboardingPending.length > 0) && (
              <Divider />
            )}

          {/* Onboarding Form Section */}
          {renderEmployeeSection(
            "Upcoming Approvals",
            onboardingForm,
            "No employees currently filling onboarding forms",
            "Employees who are in the process of completing their onboarding step form."
          )}

          {/* Divider between sections */}
          {onboardingForm.length > 0 && onboardingPending.length > 0 && (
            <Divider />
          )}

          {/* New Employees Section */}
          {renderEmployeeSection(
            "New Employees",
            onboardingPending,
            "No employees in initial onboarding stage",
            "Employees who need to verify their email and provide personal information to begin onboarding."
          )}
        </>
      )}
    </>
  );
};

export default HrOnBoardingDashboard;
