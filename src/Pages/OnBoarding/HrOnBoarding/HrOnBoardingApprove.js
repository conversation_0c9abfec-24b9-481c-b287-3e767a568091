import { UploadOutlined } from "@ant-design/icons";
import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Empty,
  Form,
  Input,
  message,
  Radio,
  Row,
  Select,
  Typography,
  Upload,
} from "antd";
import PageHeader from "AtomicComponents/PageHeader";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import RenderEmployeeSelect from "AtomicComponents/RenderEmployeeSelect";
import Loader from "Commons/Loader";
import moment from "moment";
import {
  listDesignationOnboardingAction,
  listEmployeesOnboardingAction,
  listGuildsOnboardingAction,
  listSquadsOnboardingAction,
  updateEmployeeForOnboardingAction,
} from "Pages/OnBoarding/Actions/OnBoardingAction";
import React, { useEffect, useMemo, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { AWSDateFormat, DateFormat } from "utils/constants";
import { uploadFileToS3 } from "utils/helperFunction";
import { boxClass } from "utils/TailwindCommonClasses";

const HrOnBoardingApprove = () => {
  // Get the current location and employee data from the state
  const location = useLocation();
  const { employeeData } = location.state || {};
  const { username } = useParams();

  const [form] = Form.useForm();
  // Set up state variables for designations, squads, guilds, reporting to, and file list
  const [designations, setDesignations] = useState([]);
  const [squads, setSquads] = useState([]);
  const [guilds, setGuilds] = useState([]);
  const [reportingTo, setReportingTo] = useState([]);
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState({
    appointment_letter: [],
    york_agreement: [],
    salary_slip_1: [],
    salary_slip_2: [],
    salary_slip_3: [],
  });
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(
    employeeData?.country || "IN"
  );

  //list details for designation, squad, guild, reporting to
  useEffect(() => {
    const fetchAll = async () => {
      setLoading(true);
      await Promise.all([
        listDesignationDetails(),
        listSquadDetails(),
        listGuildDetails(),
        listReportingToDetails(),
      ]);
      setLoading(false);
    };
    fetchAll();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Function to fetch reporting to details
  const listDesignationDetails = async () => {
    try {
      const data = await listDesignationOnboardingAction();
      setDesignations(data);
    } catch (error) {
      console.error("Failed to fetch designations:", error);
      message.error("Failed to fetch designations");
    }
  };

  // Function to fetch squad details
  const listSquadDetails = async () => {
    try {
      const filter = {
        active: { eq: true },
      };
      const data = await listSquadsOnboardingAction(filter);
      setSquads(data);
    } catch (error) {
      console.error("Failed to fetch squads:", error);
      message.error("Failed to fetch squads");
    }
  };

  // Function to fetch guild details
  const listGuildDetails = async () => {
    try {
      const filter = {
        active: { eq: true },
      };
      const data = await listGuildsOnboardingAction(filter);
      setGuilds(data);
    } catch (error) {
      console.error("Failed to fetch guilds:", error);
      message.error("Failed to fetch guilds");
    }
  };

  // Function to fetch reporting to details
  const listReportingToDetails = async () => {
    try {
      const filter = {
        and: [{ hidden_profile: { ne: true } }, { active: { ne: false } }],
      };

      const data = await listEmployeesOnboardingAction(filter);
      setReportingTo(data);
    } catch (error) {
      console.error("Failed to fetch reporting to:", error);
      message.error("Failed to fetch reporting to");
    }
  };

  // Set the form fields with employee data if available
  const personalInfoFields = useMemo(
    () => [
      { key: "first_name", label: "First Name" },
      { key: "last_name", label: "Last Name" },
      { key: "email", label: "Email" },
      { key: "mobile", label: "Mobile Number" },
      {
        key: "birth_date",
        label: "Date of Birth",
        transform: (value) =>
          value ? moment(value).format(DateFormat) : "None",
      },
      { key: "gender", label: "Gender" },
    ],
    []
  );
  useEffect(() => {
    if (employeeData?.country) {
      form.setFieldsValue({ country: employeeData.country });
      setSelectedCountry(employeeData.country);
    }
  }, [employeeData, form]);

  // Generic function to render detail fields in a 3-column layout
  const renderDetailFields = (data, fields) => {
    if (!data) return null;

    return (
      <Row gutter={[24, 16]}>
        {fields.map((field) => {
          const value = field.transform
            ? field.transform(data[field.key])
            : data[field.key] || "N/A";

          return (
            <Col span={8} key={field.key}>
              <div className="detail-item">
                <Typography.Text className="text-gray-500">
                  {field.label}:
                </Typography.Text>
                <div className="font-medium mt-1">{value}</div>
              </div>
            </Col>
          );
        })}
      </Row>
    );
  };

  const navigate = useNavigate();

  // Set the form fields with employee data if available
  useEffect(() => {
    if (!employeeData) {
      const timer = setTimeout(() => {
        navigate("/employee-onboarding-dashboard");
      }, 5000);

      // Clear the timeout on component unmount
      return () => clearTimeout(timer);
    }
  }, [employeeData, navigate]);

  if (!employeeData) {
    return (
      <div className="flex justify-center items-center h-full">
        <Empty description="No Employee Data Found" />
      </div>
    );
  }

  // Handle form submission and file upload
  const handleFileChange = (info, fieldName) => {
    // Update the file list for the specific field
    setFileList((prev) => ({
      ...prev,
      [fieldName]: info.fileList,
    }));
  };

  // Function to handle onboarding approval
  const handleApproveOnboarding = async () => {
    setIsLoading(true);
    try {
      const formData = form.getFieldsValue();

      // Upload all files to S3 and get their keys
      const uploadPromises = {};
      const documentKeys = {};

      // For each document field, upload the file if it exists
      for (const field in fileList) {
        if (fileList[field].length > 0) {
          const file = fileList[field][0].originFileObj;
          if (file) {
            // Create unique filename with timestamp and field identifier
            const timestamp = new Date().getTime();
            const filename = `${
              employeeData.email || username
            }/${field}_${timestamp}_${file.name}`;
            uploadPromises[field] = uploadFileToS3(file, filename);
          }
        }
      }

      // Wait for all uploads to complete
      for (const field in uploadPromises) {
        try {
          const s3Response = await uploadPromises[field];
          documentKeys[field] = s3Response.key || s3Response;
        } catch (error) {
          console.error(`Failed to upload ${field}:`, error);
          message.error(`Failed to upload ${field}`);
          setIsLoading(false);
          return;
        }
      }
      const input = {
        email: employeeData?.email,
        employee_id: formData?.employee_id,
        york_start_date: formData?.york_start_date?.format(AWSDateFormat),
        career_start_date: formData?.career_start_date?.format(AWSDateFormat),
        employeeTitleId: formData?.employeeTitleId,
        employeeSquadId: formData?.employeeSquadId,
        guildEmployeeId: formData?.employeeGuildId,
        squadEmployeeId: formData?.employeeSquadId,
        employeeReporting_toId: formData?.employeeReporting_toId,
        account_status: "ACTIVE",
        active: true,
        york_appointment: documentKeys.appointment_letter,
        york_agreement: documentKeys.york_agreement,
        salary_slip: documentKeys.salary_slip_1,
        salary_slip_1: documentKeys.salary_slip_2,
        salary_slip_2: documentKeys.salary_slip_3,
        metadata: null,
        country: formData?.country || "IN",
      };
      await updateEmployeeForOnboardingAction(input);
      message.success("Onboarding approved successfully!");
      navigate("/employee-onboarding-dashboard");
    } catch (error) {
      console.error("Failed to approve onboarding:", error);
      message.error("Failed to approve onboarding");
    } finally {
      setIsLoading(false);
    }
  };

  if (loading) {
    return <Loader title="Loading onboarding details, please wait..." />;
  }

  return (
    <>
      <Form
        form={form}
        layout="vertical"
        onFinish={() => handleApproveOnboarding()}
      >
        {/* Page Header */}
        <PageHeader
          title={`Approve Onboarding for  ${employeeData?.first_name || ""} ${
            employeeData?.last_name || ""
          }`}
          isBeta
          action={
            <div className="flex gap-4 ">
              <Button
                type="default"
                onClick={() => {
                  form.resetFields();
                  setFileList({
                    appointment_letter: [],
                    york_agreement: [],
                    salary_slip_1: [],
                    salary_slip_2: [],
                    salary_slip_3: [],
                  });
                }}
                disabled={isLoading}
              >
                Reset
              </Button>
              <Button type="primary" htmlType="submit" loading={isLoading}>
                Submit
              </Button>
            </div>
          }
        />
        {/* Basic Employee Details */}
        <div className="space-y-4 mt-2 p-2">
          <Typography.Title level={5}>Basic Employee Details</Typography.Title>
          <div className={`${boxClass} p-4`}>
            {renderDetailFields(employeeData, personalInfoFields)}
          </div>
        </div>

        {/* Employee Information Fill Up Form */}
        {/* Employee Information */}
        <div className="space-y-4  p-2">
          <Typography.Title level={5}>
            Employee Information Fill Up Form
          </Typography.Title>
          <div className={`${boxClass} p-4`}>
            <Row gutter={[24, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="employee_id"
                  label="Employee ID"
                  rules={[
                    { required: true, message: "Please enter Employee ID!" },
                  ]}
                >
                  <Input placeholder="Enter Employee ID" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="employeeTitleId"
                  label="Designation"
                  rules={[
                    { required: true, message: "Please select a designation!" },
                  ]}
                >
                  <Select placeholder="Select Designation" showSearch>
                    {designations?.map((item, index) => (
                      <Select.Option key={index} value={item?.id}>
                        {item?.name} - {item?.level} - {item?.overall_level}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="country"
                  label="Country"
                  initialValue={employeeData?.country}
                  rules={[
                    { required: true, message: "Please select a country!" },
                  ]}
                >
                  <Radio.Group
                    onChange={(e) => setSelectedCountry(e.target.value)}
                  >
                    <Radio value="IN">India</Radio>
                    <Radio value="US">USA</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
              {selectedCountry !== "US" && (
                <Col xs={24} sm={12} md={8}>
                  <Form.Item
                    name="employeeSquadId"
                    label="Squad"
                    rules={[
                      { required: true, message: "Please select a squad!" },
                    ]}
                  >
                    <Select placeholder="Select Squad" showSearch>
                      {squads?.map((item, index) => (
                        <Select.Option key={index} value={item?.name}>
                          {item?.name} (
                          <RenderEmployeeFullName
                            className="m-0"
                            employee={item?.squad_manager}
                            noRedirect
                            hoverClassName=""
                          />
                          )
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              )}
              {selectedCountry !== "US" && (
                <Col xs={24} sm={12} md={8}>
                  <Form.Item
                    name="employeeGuildId"
                    label="Guild"
                    rules={[
                      { required: true, message: "Please select a guild!" },
                    ]}
                  >
                    <Select placeholder="Select Guild" showSearch>
                      {guilds?.map((item, index) => (
                        <Select.Option key={index} value={item?.name}>
                          {item?.name} (
                          <RenderEmployeeFullName
                            className="m-0"
                            employee={item?.guild_manager}
                            noRedirect
                            hoverClassName=""
                          />
                          )
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              )}
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="employeeReporting_toId"
                  label="Reporting To"
                  rules={[
                    { required: true, message: "Please select reporting to!" },
                  ]}
                >
                  <RenderEmployeeSelect
                    employeesList={reportingTo}
                    placeholder="Select Reporting To"
                    showSearch
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={4}>
                <Form.Item
                  name="york_start_date"
                  label="York Start Date"
                  rules={[{ required: true, message: "Please select a date!" }]}
                >
                  <DatePicker placeholder="Select Date" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={4}>
                <Form.Item
                  name="career_start_date"
                  label="Career Start Date "
                  dependencies={["york_start_date"]}
                  rules={[
                    { required: true, message: "Please select a date!" },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        const yorkStartDate = getFieldValue("york_start_date");
                        if (
                          !value ||
                          !yorkStartDate ||
                          !value.isAfter(yorkStartDate) // valid if before or equal
                        ) {
                          return Promise.resolve();
                        }
                        return Promise.reject(
                          new Error(
                            "Career Start Date cannot be after York Start Date!"
                          )
                        );
                      },
                    }),
                  ]}
                >
                  <DatePicker placeholder="Select Date" />
                </Form.Item>
              </Col>
            </Row>
          </div>
        </div>

        {/* Employee Upload Documents */}
        <div className="space-y-4 p-2">
          <Typography.Title level={5}>Upload Documents</Typography.Title>
          <div className={`${boxClass} p-4`}>
            <Row gutter={[24, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="appointment_letter"
                  label="York Appointment Letter"
                  rules={[
                    {
                      required: true,
                      message: "Please upload appointment letter!",
                    },
                  ]}
                >
                  <Upload
                    accept=".pdf"
                    maxCount={1}
                    fileList={fileList.appointment_letter}
                    onChange={(info) =>
                      handleFileChange(info, "appointment_letter")
                    }
                    beforeUpload={(file) => {
                      const isPDF = file.type === "application/pdf";
                      if (!isPDF) {
                        message.error("You can only upload PDF files!");
                      }
                      return false; // Prevent auto upload
                    }}
                  >
                    <Button icon={<UploadOutlined />}>Upload PDF</Button>
                  </Upload>
                </Form.Item>
              </Col>

              {selectedCountry !== "US" && (
                <Col xs={24} sm={12} md={8}>
                  <Form.Item
                    name="york_agreement"
                    label="York Agreement"
                    rules={[
                      {
                        required: true,
                        message: "Please upload York agreement!",
                      },
                    ]}
                  >
                    <Upload
                      accept=".pdf"
                      maxCount={1}
                      fileList={fileList.york_agreement}
                      onChange={(info) =>
                        handleFileChange(info, "york_agreement")
                      }
                      beforeUpload={(file) => {
                        const isPDF = file.type === "application/pdf";
                        if (!isPDF) {
                          message.error("You can only upload PDF files!");
                        }
                        return false;
                      }}
                    >
                      <Button icon={<UploadOutlined />}>Upload PDF</Button>
                    </Upload>
                  </Form.Item>
                </Col>
              )}
            </Row>

            {/* Previous Employer Salary Slips */}
            <Typography.Title level={5} className="mt-4">
              Previous Employer Salary Slips
            </Typography.Title>
            <Row gutter={[24, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Form.Item name="salary_slip_1" label="Salary Slip 1">
                  <Upload
                    accept=".pdf"
                    maxCount={1}
                    fileList={fileList.salary_slip_1}
                    onChange={(info) => handleFileChange(info, "salary_slip_1")}
                    beforeUpload={(file) => {
                      const isPDF = file.type === "application/pdf";
                      if (!isPDF) {
                        message.error("You can only upload PDF files!");
                      }
                      return false; // Prevent auto upload
                    }}
                  >
                    <Button icon={<UploadOutlined />}>Upload PDF</Button>
                  </Upload>
                </Form.Item>
              </Col>

              <Col xs={24} sm={12} md={8}>
                <Form.Item name="salary_slip_2" label="Salary Slip 2">
                  <Upload
                    accept=".pdf"
                    maxCount={1}
                    fileList={fileList.salary_slip_2}
                    onChange={(info) => handleFileChange(info, "salary_slip_2")}
                    beforeUpload={(file) => {
                      const isPDF = file.type === "application/pdf";
                      if (!isPDF) {
                        message.error("You can only upload PDF files!");
                      }
                      return false; // Prevent auto upload
                    }}
                  >
                    <Button icon={<UploadOutlined />}>Upload PDF</Button>
                  </Upload>
                </Form.Item>
              </Col>

              <Col xs={24} sm={12} md={8}>
                <Form.Item name="salary_slip_3" label="Salary Slip 3">
                  <Upload
                    accept=".pdf"
                    maxCount={1}
                    fileList={fileList.salary_slip_3}
                    onChange={(info) => handleFileChange(info, "salary_slip_3")}
                    beforeUpload={(file) => {
                      const isPDF = file.type === "application/pdf";
                      if (!isPDF) {
                        message.error("You can only upload PDF files!");
                      }
                      return false; // Prevent auto upload
                    }}
                  >
                    <Button icon={<UploadOutlined />}>Upload PDF</Button>
                  </Upload>
                </Form.Item>
              </Col>
            </Row>
          </div>
        </div>
      </Form>
    </>
  );
};

export default HrOnBoardingApprove;
