import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import { Typography } from "antd";
import Background from "Commons/Background";
import Navbar from "Pages/Careers/Navbar";
import { getEmployeeOnboardingAction } from "Pages/OnBoarding/Actions/OnBoardingAction";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getEmployeeDetails,
  SetEmployeeDetails,
} from "store/slices/loginSlice";
import { getEmployeeAction } from "utils/Actions";

const { Title, Text } = Typography;

const HRReviewScreen = () => {
  const [progress, setProgress] = useState(0);
  const [completedSteps, setCompletedSteps] = useState([false, false, false]);
  const currentUser = useSelector(getEmployeeDetails);
  const dispatch = useDispatch();

  useEffect(() => {
    fetchEmployeeDetails();
    // Simulate progress
    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev >= 100 ? 0 : prev + 2;

        // Update completed steps based on progress
        setCompletedSteps([
          newProgress > 30,
          newProgress > 65,
          newProgress > 95,
        ]);

        return newProgress;
      });
    }, 150);

    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const fetchEmployeeDetails = async () => {
    try {
      const employeeData = await getEmployeeAction(currentUser?.email);
      dispatch(SetEmployeeDetails(employeeData));
    } catch (error) {
      console.error("Failed to fetch employee details:", error);
    }
  };

  return (
    <div className="min-h-screen flex flex-col relative">
      <Navbar onlyLogo />
      <Background />
      <div className="flex items-center justify-center flex-1 p-4 md:p-8">
        <div className="w-full max-w-lg bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100 relative">
          {/* Animated header */}
          <div className="relative h-2 bg-gray-100 overflow-hidden">
            <div
              className="absolute top-0 left-0 h-full bg-gradient-to-r from-teal-400 to-[#00b48b] transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            >
              <div className="absolute inset-0 bg-white opacity-20 animate-pulse"></div>
            </div>
          </div>

          {/* Floating icon - moved outside the content area to prevent cutting */}
          <div className="absolute left-1/2 -translate-x-1/2 top-5 z-10">
            <div
              className={`relative p-4 rounded-full bg-white shadow-xl border-4 border-white transition-all duration-300
                ${progress > 10 ? "animate-float" : ""}`}
            >
              <div
                className={`absolute inset-0 rounded-full bg-teal-100 opacity-30 ${
                  progress > 10 ? "animate-ping-slow" : ""
                }`}
              ></div>
              <TeamOutlined className="text-3xl md:text-4xl text-[#00b48b]" />
            </div>
          </div>

          <div className="p-6 md:p-8 space-y-6 pt-24 md:pt-28">
            {/* Content */}
            <div className="text-center space-y-4">
              <Title
                level={3}
                className="!mb-2 !text-gray-800 font-bold !text-xl md:!text-2xl"
              >
                Account Under Review
              </Title>
              <Text
                type="secondary"
                className="block text-gray-600 text-sm md:text-base"
              >
                Our HR team is verifying your account details
              </Text>
            </div>

            {/* Animated checklist */}
            <div className="space-y-4 mt-6">
              {[
                {
                  label: "Profile Verification",
                  icon: <CheckCircleOutlined />,
                },
                { label: "Document Review", icon: <CheckCircleOutlined /> },
                { label: "Final Approval", icon: <CheckCircleOutlined /> },
              ].map((step, index) => (
                <div
                  key={index}
                  className={`flex items-center gap-3 md:gap-4 p-3 md:p-4 rounded-xl transition-all duration-500 
                  ${
                    completedSteps[index]
                      ? "bg-teal-50 border border-teal-100 shadow-inner"
                      : "bg-gray-50 border border-gray-100"
                  }`}
                >
                  <div
                    className={`relative flex items-center justify-center h-7 w-7 md:h-8 md:w-8 rounded-full 
                  ${
                    completedSteps[index]
                      ? "bg-[#00b48b] text-white animate-pop"
                      : "bg-gray-200 text-gray-400"
                  }`}
                  >
                    {step.icon}
                    {completedSteps[index] && (
                      <div className="absolute inset-0 rounded-full bg-[#00b48b] opacity-0 animate-ping-once"></div>
                    )}
                  </div>
                  <Text
                    className={
                      completedSteps[index]
                        ? "text-gray-800 font-medium text-sm md:text-base"
                        : "text-gray-500 text-sm md:text-base"
                    }
                  >
                    {step.label}
                  </Text>
                  {completedSteps[index] && (
                    <div className="ml-auto text-[#00b48b] animate-bounce-slow">
                      ✓
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Footer */}
            <div className="flex items-center justify-center gap-2 pt-4 text-gray-500">
              <ClockCircleOutlined className="text-base" />
              <Text type="secondary" className="text-xs md:text-sm">
                Typically completed within 24-48 hours
              </Text>
            </div>
          </div>
        </div>

        {/* Add these custom animations to your Tailwind config */}
        <style jsx global>{`
          @keyframes float {
            0%,
            100% {
              transform: translateY(0);
            }
            50% {
              transform: translateY(-8px);
            }
          }

          @keyframes ping-slow {
            0% {
              transform: scale(0.8);
              opacity: 0.8;
            }
            100% {
              transform: scale(2);
              opacity: 0;
            }
          }

          @keyframes pop {
            0% {
              transform: scale(0.8);
            }
            50% {
              transform: scale(1.1);
            }
            100% {
              transform: scale(1);
            }
          }

          @keyframes ping-once {
            0% {
              transform: scale(0.8);
              opacity: 0.8;
            }
            100% {
              transform: scale(2);
              opacity: 0;
            }
          }

          @keyframes bounce-slow {
            0%,
            100% {
              transform: translateY(0);
            }
            50% {
              transform: translateY(-5px);
            }
          }

          .animate-float {
            animation: float 3s ease-in-out infinite;
          }

          .animate-ping-slow {
            animation: ping-slow 3s cubic-bezier(0, 0, 0.2, 1) infinite;
          }

          .animate-pop {
            animation: pop 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          }

          .animate-ping-once {
            animation: ping-once 0.8s cubic-bezier(0, 0, 0.2, 1) forwards;
          }

          .animate-bounce-slow {
            animation: bounce-slow 2s ease-in-out infinite;
          }
        `}</style>
      </div>
    </div>
  );
};

export default HRReviewScreen;
