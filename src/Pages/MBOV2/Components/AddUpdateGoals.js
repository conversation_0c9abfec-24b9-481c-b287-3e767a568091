import {
  Button,
  Card,
  Input,
  message,
  Popover,
  Select,
  Tag,
  Modal,
} from "antd";
import React, { useEffect, useState } from "react";
import { RESTPost } from "utils/RESTApi";
import {
  handleFetchMBOGoals,
  fetchMBODetailsPerEmployeeForTable,
} from "../Actions/Actions";
import { useSelector } from "react-redux";
import { getCurrentGroups, getCurrentUserData } from "store/slices/loginSlice";
import {
  DeleteFilled,
  DragOutlined,
  CopyOutlined,
  Loading3QuartersOutlined,
  EditOutlined,
} from "@ant-design/icons";
import {
  formatMonthsIntoYearsAndMonths,
  countYearAndMonthFromDate,
} from "utils/commonMethods";
import { listReportingEmployees } from "Pages/MBOV2/Helper/listReportingEmployees";
import { Storage } from "aws-amplify";

// Fixed dropdown overflow issue by:
// 1. Adding flex-wrap to containers
// 2. Using dropdownMatchSelectWidth={false} for proper dropdown width
// 3. Adding getPopupContainer for proper positioning
// 4. Adding min-w-0 to prevent flex items from overflowing
// 5. Using relative positioning on main container
// 6. Added experience level display in goal dropdown options (e.g., "Goal Name (2 years - 5 years)")
//    - MBOGoalsV2 stores experienceRange as years (single integer)
//    - Legacy MBOGoals stores experienceRange as months (array [min, max])
// 7. Improved weightage totaling with:
//    - Safe number conversion and validation
//    - Real-time validation to prevent exceeding 100%
//    - Visual indicators for remaining weightage
//    - Helper functions for consistent calculations
//    - Input validation to ensure only valid numbers (0-100)

// Helper function to safely format experience range
const formatExperienceRange = (experienceRange) => {
  if (!experienceRange) return null;

  // Handle single integer value (MBOGoalsV2 format - stored as years)
  if (typeof experienceRange === "number" && experienceRange >= 0) {
    // Convert years to months (assuming experienceRange is in years)
    const months = experienceRange * 12;
    const formatted = formatMonthsIntoYearsAndMonths(months);
    if (formatted !== "N/a") {
      return `(${formatted})`;
    }
  }

  // Handle array format [min, max] (legacy MBOGoals format - stored as months)
  if (Array.isArray(experienceRange) && experienceRange.length >= 2) {
    const min = experienceRange[0];
    const max = experienceRange[1];

    // Check if both values are valid numbers
    if (
      min !== null &&
      min !== undefined &&
      max !== null &&
      max !== undefined &&
      typeof min === "number" &&
      typeof max === "number" &&
      min >= 0 &&
      max >= 0
    ) {
      const minFormatted = formatMonthsIntoYearsAndMonths(min);
      const maxFormatted = formatMonthsIntoYearsAndMonths(max);

      // Only show if both formatted values are not "N/a"
      if (minFormatted !== "N/a" && maxFormatted !== "N/a") {
        return `(${minFormatted} - ${maxFormatted})`;
      }
    }
  }

  // Handle string format
  if (typeof experienceRange === "string" && experienceRange.trim()) {
    return `(${experienceRange})`;
  }

  return null;
};

// Helper function to calculate employee experience in months
const calculateEmployeeExperience = (careerStartDate) => {
  if (!careerStartDate) return 0;

  try {
    const { months, years } = countYearAndMonthFromDate(
      new Date(careerStartDate)
    );
    return years * 12 + months;
  } catch (error) {
    console.error("Error calculating employee experience:", error);
    return 0;
  }
};

// Helper function to check if goal matches employee experience
const isGoalMatchingExperience = (goal, employeeExperienceMonths) => {
  if (!goal.experienceRange) return true; // If no experience range specified, show for all

  // Handle single integer value (MBOGoalsV2 format - stored as years)
  if (typeof goal.experienceRange === "number" && goal.experienceRange >= 0) {
    const requiredExperienceMonths = goal.experienceRange * 12;
    return employeeExperienceMonths >= requiredExperienceMonths;
  }

  // Handle array format [min, max] (legacy MBOGoals format - stored as months)
  if (Array.isArray(goal.experienceRange) && goal.experienceRange.length >= 2) {
    const min = goal.experienceRange[0];
    const max = goal.experienceRange[1];

    if (
      min !== null &&
      min !== undefined &&
      max !== null &&
      max !== undefined &&
      typeof min === "number" &&
      typeof max === "number" &&
      min >= 0 &&
      max >= 0
    ) {
      return employeeExperienceMonths >= min && employeeExperienceMonths <= max;
    }
  }

  return true; // Default to showing if experience range is invalid
};

// Helper function to safely calculate total weightage
const calculateTotalWeightage = (goals) => {
  return goals.reduce((sum, goal) => {
    const weightage = Number(goal.weightage);
    return sum + (isNaN(weightage) ? 0 : Math.max(0, weightage));
  }, 0);
};

// Helper function to safely calculate total evaluated weightage
const calculateTotalEvaluatedWeightage = (goals) => {
  return goals.reduce((sum, goal) => {
    const evaluated = Number(goal.evaluated);
    return sum + (isNaN(evaluated) ? 0 : Math.max(0, evaluated));
  }, 0);
};

// Profile Avatar Component with Storage.get
const ProfileAvatar = ({ profilePic, firstName, lastName, email }) => {
  const [avatarUrl, setAvatarUrl] = useState(null);

  useEffect(() => {
    let isMounted = true;
    if (profilePic) {
      Storage.get(profilePic)
        .then((url) => {
          if (isMounted) setAvatarUrl(url);
        })
        .catch(() => {
          if (isMounted) setAvatarUrl(null);
        });
    } else {
      setAvatarUrl(null);
    }
    return () => {
      isMounted = false;
    };
  }, [profilePic]);

  return (
    <div className="w-6 h-6 rounded-full overflow-hidden flex-shrink-0">
      {avatarUrl ? (
        <img
          src={avatarUrl}
          alt={`${firstName} ${lastName}`}
          className="w-full h-full object-cover"
          onError={(e) => {
            e.target.style.display = "none";
            e.target.nextSibling.style.display = "flex";
          }}
        />
      ) : null}
      <div
        className={`w-full h-full flex items-center justify-center text-xs font-medium text-white ${
          avatarUrl ? "hidden" : "flex"
        }`}
        style={{
          backgroundColor: `hsl(${
            (email?.charCodeAt(0) || 0) % 360
          }, 70%, 50%)`,
        }}
      >
        {firstName?.charAt(0)?.toUpperCase() || "U"}
      </div>
    </div>
  );
};

function AddUpdateGoals({
  goalsWithWeightage,
  setGoalsWithWeightage,
  MBODetails,
}) {
  const userGroups = useSelector(getCurrentGroups) ?? [];
  const { email: currentUserEmail } = useSelector(getCurrentUserData);
  const userPermissions =
    userGroups?.includes("SquadLeader") ||
    userGroups?.includes("UnitTeamLeader") ||
    userGroups?.includes("Executive") ||
    userGroups?.includes("TechnicalManager");

  const [isAIFillLoading, setisAIFillLoading] = useState(false);
  const [managerAsk, setmanagerAsk] = useState("");
  const [isAIFillPopupVisible, setIsAIFillPopupVisible] = useState(false);
  const [isCopyModalVisible, setIsCopyModalVisible] = useState(false);
  const [availableEmployees, setAvailableEmployees] = useState([]);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [employeeGoals, setEmployeeGoals] = useState([]);
  const [isLoadingEmployeeGoals, setIsLoadingEmployeeGoals] = useState(false);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState(null);
  const [selectedExperienceFilter, setSelectedExperienceFilter] =
    useState(null);
  const [editingSuccessCriteriaIdx, setEditingSuccessCriteriaIdx] =
    useState(null);

  // Add order field to goals if not present
  const goalsWithOrder = goalsWithWeightage.map((goal, index) => ({
    ...goal,
    order: goal.order || index,
  }));

  // Sort goals by order
  const sortedGoals = [...goalsWithOrder].sort((a, b) => a.order - b.order);

  // Drag and drop handlers
  const handleDragStart = (e, index) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = "move";
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDrop = (e, dropIndex) => {
    e.preventDefault();
    if (draggedIndex === null || draggedIndex === dropIndex) return;

    const newGoals = [...sortedGoals];
    const draggedGoal = newGoals[draggedIndex];

    // Remove dragged item
    newGoals.splice(draggedIndex, 1);

    // Insert at new position
    newGoals.splice(dropIndex, 0, draggedGoal);

    // Update order field for all goals
    const updatedGoals = newGoals.map((goal, index) => ({
      ...goal,
      order: index,
    }));

    setGoalsWithWeightage(updatedGoals);
    setDraggedIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
  };

  // Fetch available employees for copy goals
  const fetchAvailableEmployees = async () => {
    setIsLoadingEmployees(true);
    try {
      const employees = await listReportingEmployees(currentUserEmail);
      setAvailableEmployees(employees);
    } catch (error) {
      message.error("Failed to fetch employees");
      console.error(error);
    } finally {
      setIsLoadingEmployees(false);
    }
  };

  // Copy goals functionality
  const handleCopyGoals = async (employee) => {
    if (!employee) {
      message.error("Please select an employee first");
      return;
    }

    setIsLoadingEmployeeGoals(true);
    try {
      // Fetch employee's MBO data
      const response = await fetchMBODetailsPerEmployeeForTable(employee.email);

      if (response && response.length > 0) {
        // Get the most recent MBO (first item since we sort by DESC)
        const latestMBO = response[0];

        if (latestMBO.goals && latestMBO.goals.length > 0) {
          // Transform goals to match the expected format
          const transformedGoals = latestMBO.goals.map((goal, index) => ({
            id: `${goal.category}-${index}`, // Generate unique ID
            category: goal.category,
            goal: goal.goal,
            weightage: goal.weightage || 0,
            note: goal.note || "",
            evaluated: goal.evaluated,
            purpose: goal.purpose || "",
            customSuccessCriteria: goal.customSuccessCriteria || "",
            order: goal.order || index,
            updatedAt: goal.updatedAt || new Date().toISOString(),
          }));

          setEmployeeGoals(transformedGoals);
          message.success(
            `Found ${transformedGoals.length} goal(s) for ${employee.first_name}`
          );
        } else {
          setEmployeeGoals([]);
          message.info("No goals found for this employee");
        }
      } else {
        setEmployeeGoals([]);
        message.info("No MBO records found for this employee");
      }
    } catch (error) {
      message.error("Failed to fetch employee goals");
      console.error(error);
    } finally {
      setIsLoadingEmployeeGoals(false);
    }
  };

  const handleCopySelectedGoals = (selectedGoalIds) => {
    const goalsToCopy = employeeGoals.filter((goal) =>
      selectedGoalIds.includes(goal.id)
    );

    if (goalsToCopy.length === 0) {
      message.error("Please select at least one goal to copy");
      return;
    }

    // Add copied goals with new order
    const newGoals = goalsToCopy.map((goal, index) => ({
      category: goal.category,
      goal: goal.goal,
      weightage: goal.weightage || 0,
      note: "",
      evaluated: undefined,
      purpose: goal.purpose || "",
      customSuccessCriteria: goal.customSuccessCriteria || "",
      order: sortedGoals.length + index,
      updatedAt: new Date().toISOString(),
    }));

    setGoalsWithWeightage([...sortedGoals, ...newGoals]);
    setIsCopyModalVisible(false);
    setSelectedEmployee(null);
    setEmployeeGoals([]);
    message.success(`Copied ${newGoals.length} goal(s) successfully`);
  };

  const handleAutofillGoals = async () => {
    if (!managerAsk) {
      message.error("Please enter your ask to the AI.");
      return;
    }
    setisAIFillLoading(true);

    try {
      const { goals } = await RESTPost("/api/generate-mbo-goals", {
        managerAsk: managerAsk,
        email: MBODetails?.employeeMbosId,
      });

      // Add order field to generated goals and ensure all required fields are present
      const goalsWithOrder = goals.map((goal, index) => ({
        ...goal,
        order: sortedGoals.length + index,
        note: goal.note || "", // Ensure note field exists
        updatedAt: new Date().toISOString(),
      }));

      setGoalsWithWeightage([...sortedGoals, ...goalsWithOrder]);
      setmanagerAsk("");

      // Show success message and close popup
      message.success(
        `Successfully generated ${goalsWithOrder.length} goal(s)`
      );
      setIsAIFillPopupVisible(false);
    } catch (error) {
      console.log("Error fetching MBO goals:", error);
      message.error("Failed to generate goals. Please try again.");
    } finally {
      setisAIFillLoading(false);
    }
  };

  const [MBOGoals, setMBOGoals] = useState({});
  const [employeeExperience, setEmployeeExperience] = useState(0);

  const handleFetchGoals = async () => {
    try {
      const response = await handleFetchMBOGoals();
      const groupedGoals = response.reduce((acc, goal) => {
        if (!acc[goal.category]) acc[goal.category] = [];
        acc[goal.category].push(goal);
        return acc;
      }, {});
      setMBOGoals(groupedGoals);
    } catch (error) {
      message.error("Failed to fetch goals.");
      console.error(error);
    } finally {
    }
  };

  // Calculate employee experience when MBO details change
  useEffect(() => {
    if (MBODetails?.employee?.career_start_date) {
      const experienceMonths = calculateEmployeeExperience(
        MBODetails.employee.career_start_date
      );
      setEmployeeExperience(experienceMonths);
    }
  }, [MBODetails?.employee?.career_start_date]);

  // Get all available experience ranges from goals
  const getAvailableExperienceRanges = () => {
    const experienceRanges = new Set();

    Object.values(MBOGoals).forEach((goals) => {
      goals.forEach((goal) => {
        if (goal.experienceRange) {
          // Handle single integer value (MBOGoalsV2 format - stored as years)
          if (
            typeof goal.experienceRange === "number" &&
            goal.experienceRange >= 0
          ) {
            experienceRanges.add(goal.experienceRange);
          }

          // Handle array format [min, max] (legacy MBOGoals format - stored as months)
          if (
            Array.isArray(goal.experienceRange) &&
            goal.experienceRange.length >= 2
          ) {
            const min = goal.experienceRange[0];
            if (
              min !== null &&
              min !== undefined &&
              typeof min === "number" &&
              min >= 0
            ) {
              const minYears = Math.floor(min / 12);
              experienceRanges.add(minYears);
            }
          }
        }
      });
    });

    return Array.from(experienceRanges).sort((a, b) => a - b);
  };

  // Filter goals based on employee experience
  const getFilteredGoals = (category) => {
    const goals = MBOGoals[category] || [];
    let filteredGoals;

    if (selectedExperienceFilter === null) {
      filteredGoals = goals.filter((goal) =>
        isGoalMatchingExperience(goal, employeeExperience)
      );
    } else {
      filteredGoals = goals.filter((goal) => {
        if (!goal.experienceRange) return true;

        // Handle single integer value (MBOGoalsV2 format - stored as years)
        if (
          typeof goal.experienceRange === "number" &&
          goal.experienceRange >= 0
        ) {
          return goal.experienceRange >= selectedExperienceFilter;
        }

        // Handle array format [min, max] (legacy MBOGoals format - stored as months)
        if (
          Array.isArray(goal.experienceRange) &&
          goal.experienceRange.length >= 2
        ) {
          const min = goal.experienceRange[0];
          const max = goal.experienceRange[1];

          if (
            min !== null &&
            min !== undefined &&
            typeof min === "number" &&
            min >= 0
          ) {
            // Convert months to years for comparison
            const minYears = Math.floor(min / 12);
            return minYears >= selectedExperienceFilter;
          }
        }

        return true;
      });
    }

    // Sort goals alphabetically by goal name
    return filteredGoals.sort((a, b) => a.goal.localeCompare(b.goal));
  };

  useEffect(() => {
    if (MBODetails) {
      if (
        MBODetails.status === "Target unset" ||
        MBODetails?.status === "Target Set"
      ) {
        handleFetchGoals();
      }
    }
  }, [MBODetails]);

  // Fetch employees when copy modal is opened
  useEffect(() => {
    if (isCopyModalVisible && availableEmployees.length === 0) {
      fetchAvailableEmployees();
    }
  }, [isCopyModalVisible]);

  const isMBOEditingStage =
    currentUserEmail !== MBODetails?.employee?.email &&
    userPermissions &&
    (MBODetails?.status === "" ||
      MBODetails?.status === "Target unset" ||
      MBODetails?.status === "Target Set");

  // Hide goals if same user is viewing and status is "Target unset"
  const shouldHideGoals =
    currentUserEmail === MBODetails?.employee?.email &&
    MBODetails?.status === "Target unset";

  const shouldHideGrades =
    currentUserEmail === MBODetails?.employee?.email &&
    MBODetails?.status === "Target Accepted";

  const canAddMBOGrade =
    currentUserEmail !== MBODetails?.employee?.email &&
    userPermissions &&
    (MBODetails?.status === "" || MBODetails?.status === "Target Accepted");

  return (
    <>
      <style>{`
        .goal-select .ant-select-selector {
          white-space: normal !important;
          word-break: break-word !important;
          min-height: 38px;
          align-items: flex-start;
          display: flex;
          height: auto !important;
          padding-top: 4px !important;
          padding-bottom: 4px !important;
          line-height: 1.4 !important;
        }
        .goal-select .ant-select-selection-item {
          white-space: normal !important;
          word-break: break-word !important;
          line-height: 1.4;
          padding-top: 0;
          padding-bottom: 0;
          height: auto !important;
          display: block !important;
        }
      `}</style>
      <div className="flex flex-col gap-4 overflow-x-auto relative">
        <Card
          title={
            <div className="flex gap-2 items-center">
              Goals{" "}
              {employeeExperience > 0 && (
                <Tag color="blue">
                  Experience:{" "}
                  {formatMonthsIntoYearsAndMonths(employeeExperience)}
                </Tag>
              )}
              <div className="flex gap-2">
                {isMBOEditingStage ? (
                  <>
                    <Select
                      placeholder="Filter by experience"
                      value={selectedExperienceFilter}
                      onChange={setSelectedExperienceFilter}
                      allowClear
                      style={{ width: 200 }}
                      size="small"
                    >
                      <Select.Option value={null}>
                        Default (Candidate Experience)
                      </Select.Option>
                      {getAvailableExperienceRanges().map((years) => (
                        <Select.Option key={years} value={years}>
                          {years}+ years
                        </Select.Option>
                      ))}
                    </Select>
                    <Popover
                      content={
                        <div className="w-80">
                          <Input.TextArea
                            placeholder="Ask AI to generate goals..."
                            value={managerAsk}
                            onChange={(e) => setmanagerAsk(e.target.value)}
                            autoSize={{ minRows: 3, maxRows: 6 }}
                          />
                          <Button
                            type="primary"
                            onClick={handleAutofillGoals}
                            loading={isAIFillLoading}
                            className="mt-2"
                          >
                            Generate Goals
                          </Button>
                        </div>
                      }
                      title="AI Goal Generator"
                      trigger="click"
                      open={isAIFillPopupVisible}
                      onOpenChange={setIsAIFillPopupVisible}
                    >
                      <Button size="small" type="dashed">
                        AI Fill
                      </Button>
                    </Popover>
                    <Button
                      size="small"
                      type="dashed"
                      icon={<CopyOutlined />}
                      onClick={() => setIsCopyModalVisible(true)}
                    >
                      Copy Goals
                    </Button>
                  </>
                ) : null}
              </div>
            </div>
          }
          className="min-w-0"
        >
          <div className="flex flex-col gap-2 min-w-0">
            {shouldHideGoals ? (
              <div className="text-center py-8 text-gray-500">
                <div className="text-lg mb-2">Goals are not yet set</div>
                <div className="text-sm">
                  Your manager will set your MBO goals. Please check back later.
                </div>
              </div>
            ) : (
              sortedGoals.map((goal, idx) => (
                <div
                  key={idx}
                  className={`flex flex-col border p-2 mb-2 rounded bg-gray-50 min-w-0 ${
                    draggedIndex === idx ? "opacity-50" : ""
                  }`}
                  draggable={isMBOEditingStage}
                  onDragStart={(e) => handleDragStart(e, idx)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(e, idx)}
                  onDragEnd={handleDragEnd}
                >
                  <div className="flex gap-2 items-center flex-wrap min-w-0">
                    {isMBOEditingStage && (
                      <div className="flex items-center justify-center w-6 h-6 cursor-move text-gray-400 hover:text-gray-600">
                        <DragOutlined />
                      </div>
                    )}
                    <div className="flex flex-col w-64 min-w-0">
                      <span className="text-xs text-gray-500 mb-1">
                        Category
                      </span>
                      {isMBOEditingStage ? (
                        <Select
                          placeholder="Select Category"
                          value={goal.category}
                          onChange={(value) => {
                            const updated = [...sortedGoals];
                            updated[idx].category = value;
                            updated[idx].goal = undefined; // Reset goal when category changes
                            setGoalsWithWeightage(updated);
                          }}
                          allowClear
                          showSearch
                          dropdownMatchSelectWidth={false}
                          style={{ width: "100%" }}
                          getPopupContainer={(triggerNode) =>
                            triggerNode.parentNode
                          }
                          listHeight={400}
                          maxTagCount={0}
                          filterOption={(input, option) =>
                            option.children
                              .toLowerCase()
                              .indexOf(input.toLowerCase()) >= 0
                          }
                        >
                          {Object.keys(MBOGoals)
                            .sort()
                            .map((cat) => (
                              <Select.Option value={cat} key={cat}>
                                {cat}
                              </Select.Option>
                            ))}
                        </Select>
                      ) : (
                        <div>
                          <Tag>{goal.category}</Tag>
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col flex-1 min-w-0">
                      <span className="text-xs text-gray-500 mb-1">Goal</span>
                      {isMBOEditingStage ? (
                        <Select
                          className="goal-select"
                          placeholder="Select Goal"
                          value={goal.goal}
                          onChange={(value) => {
                            const updated = [...sortedGoals];
                            updated[idx].goal = value;
                            setGoalsWithWeightage(updated);
                          }}
                          style={{ width: "100%" }}
                          allowClear
                          showSearch
                          dropdownMatchSelectWidth={false}
                          getPopupContainer={(triggerNode) =>
                            triggerNode.parentNode
                          }
                          disabled={!goal.category || !MBOGoals[goal.category]}
                          listHeight={400}
                          maxTagCount={0}
                          filterOption={(input, option) =>
                            option.children
                              .toLowerCase()
                              .indexOf(input.toLowerCase()) >= 0
                          }
                        >
                          {getFilteredGoals(goal.category).map((g) => (
                            <Select.Option value={g.goal} key={g.goal}>
                              {g.goal}
                            </Select.Option>
                          ))}
                        </Select>
                      ) : (
                        <div className="break-words whitespace-pre-wrap">
                          {goal.goal}
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col" style={{ width: 100 }}>
                      <span className="text-xs text-gray-500 mb-1">
                        Weightage
                      </span>
                      {isMBOEditingStage ? (
                        <Input
                          placeholder="Weightage"
                          type="number"
                          min={0}
                          max={30}
                          value={goal.weightage}
                          onChange={(e) => {
                            const value = e.target.value;
                            const numValue = Number(value);

                            // Only allow valid numbers between 0 and 30 (individual goal max)
                            if (
                              value === "" ||
                              (numValue >= 0 &&
                                numValue <= 30 &&
                                !isNaN(numValue))
                            ) {
                              // Check if this change would exceed 150% total weightage
                              const currentTotal =
                                calculateTotalWeightage(sortedGoals);
                              const currentGoalWeightage =
                                Number(goal.weightage) || 0;
                              const newTotal =
                                currentTotal - currentGoalWeightage + numValue;

                              if (newTotal > 150) {
                                message.error(
                                  `This would exceed 150% total weightage (would be ${newTotal}%)`
                                );
                                return;
                              }

                              const updated = [...sortedGoals];
                              updated[idx].weightage = value;
                              setGoalsWithWeightage(updated);
                            } else if (numValue > 30) {
                              message.error(
                                "Individual goal weightage cannot exceed 30%"
                              );
                            }
                          }}
                          style={{ width: 100 }}
                        />
                      ) : (
                        goal.weightage + "%"
                      )}
                    </div>
                    <div className="flex flex-col w-64 min-w-0">
                      <span className="text-xs text-gray-500 mb-1">
                        Evaluation
                      </span>
                      {canAddMBOGrade ? (
                        <Select
                          placeholder="Evaluation Weightage"
                          value={goal.evaluated}
                          allowClear
                          dropdownMatchSelectWidth={false}
                          style={{ width: "100%" }}
                          getPopupContainer={(triggerNode) =>
                            triggerNode.parentNode
                          }
                          onChange={(e) => {
                            const updated = [...sortedGoals];
                            updated[idx].evaluated = e;
                            setGoalsWithWeightage(updated);
                          }}
                        >
                          <Select.Option value={goal.weightage}>
                            Fully Achieved
                          </Select.Option>
                          <Select.Option
                            value={Number(parseInt(goal.weightage / 2))}
                          >
                            Partially Achieved
                          </Select.Option>
                          <Select.Option value={0}>Not Achieved</Select.Option>
                        </Select>
                      ) : goal.evaluated && !shouldHideGrades ? (
                        `${goal.evaluated}%`
                      ) : (
                        "-"
                      )}
                    </div>
                    {isMBOEditingStage ? (
                      <Button
                        danger
                        icon={<DeleteFilled />}
                        onClick={() => {
                          const updated = sortedGoals.filter(
                            (_, i) => i !== idx
                          );
                          setGoalsWithWeightage(updated);
                        }}
                      />
                    ) : null}
                  </div>
                  {/* Purpose and Custom Success Criteria - Always show if available */}
                  {(goal.purpose ||
                    goal.customSuccessCriteria ||
                    isMBOEditingStage) && (
                    <div className="flex flex-col gap-2 mt-2 p-2 bg-blue-50 rounded">
                      <div className="flex flex-col">
                        <span className="text-xs text-gray-500 mb-1 font-medium">
                          Purpose
                        </span>
                        <div className="text-sm text-gray-700 break-words">
                          {goal.purpose || "Not specified"}
                        </div>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-xs text-gray-500 mb-1 font-medium flex items-center justify-between">
                          Success Criteria
                          {editingSuccessCriteriaIdx !== idx && (
                            <EditOutlined
                              className="ml-2 cursor-pointer text-gray-400 hover:text-gray-600"
                              onClick={() => setEditingSuccessCriteriaIdx(idx)}
                              style={{ fontSize: 16 }}
                            />
                          )}
                        </span>
                        {editingSuccessCriteriaIdx === idx ? (
                          <Input.TextArea
                            placeholder="Enter success criteria for this goal..."
                            value={goal.customSuccessCriteria || ""}
                            onChange={(e) => {
                              const updated = [...sortedGoals];
                              updated[idx].customSuccessCriteria =
                                e.target.value;
                              setGoalsWithWeightage(updated);
                            }}
                            autoSize={{ minRows: 2, maxRows: 4 }}
                            className="text-sm"
                            onBlur={() => setEditingSuccessCriteriaIdx(null)}
                          />
                        ) : (
                          <div className="text-sm text-gray-700 break-words">
                            {goal.customSuccessCriteria || "Not specified"}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {canAddMBOGrade ||
                  MBODetails.status === "Grade Added" ||
                  MBODetails.status === "Grade Accepted" ? (
                    <>
                      <div className="flex flex-col w-full mt-1">
                        <span className="text-xs text-gray-500 mb-1">
                          Note{" "}
                          {(goal.evaluated || goal.evaluated === 0) &&
                          goal.evaluated !== goal.weightage &&
                          !goal.note ? (
                            <span className="text-red-500 text-smallest">
                              Please document the factors that contributed to
                              the {MBODetails?.employee.first_name} not fully
                              attaining this goal
                            </span>
                          ) : null}
                        </span>
                        {canAddMBOGrade ? (
                          <Input.TextArea
                            autoSize={{ minRows: 1, maxRows: 4 }}
                            value={goal.note}
                            onChange={(e) => {
                              const updated = [...sortedGoals];
                              updated[idx].note = e.target.value;
                              setGoalsWithWeightage(updated);
                            }}
                          />
                        ) : (
                          <span>{goal.note || "-"}</span>
                        )}
                      </div>
                    </>
                  ) : null}
                </div>
              ))
            )}
            {isMBOEditingStage && !shouldHideGoals ? (
              <AddGoalForm
                onAdd={(newGoal) =>
                  setGoalsWithWeightage([...sortedGoals, newGoal])
                }
                MBOGoals={MBOGoals}
                goalsWithWeightage={sortedGoals}
                getFilteredGoals={getFilteredGoals}
              />
            ) : null}
          </div>
        </Card>
        {employeeExperience > 0 && !shouldHideGoals && (
          <div className="text-xs text-gray-500 mt-1">
            {selectedExperienceFilter === null ? (
              <>
                Showing{" "}
                {Object.keys(MBOGoals).reduce(
                  (total, category) =>
                    total + getFilteredGoals(category).length,
                  0
                )}{" "}
                of{" "}
                {Object.keys(MBOGoals).reduce(
                  (total, category) =>
                    total + (MBOGoals[category] || []).length,
                  0
                )}{" "}
                goals based on your experience level
              </>
            ) : (
              <>
                Showing{" "}
                {Object.keys(MBOGoals).reduce(
                  (total, category) =>
                    total + getFilteredGoals(category).length,
                  0
                )}{" "}
                of{" "}
                {Object.keys(MBOGoals).reduce(
                  (total, category) =>
                    total + (MBOGoals[category] || []).length,
                  0
                )}{" "}
                goals for {selectedExperienceFilter}+ years experience
              </>
            )}
          </div>
        )}
        {!shouldHideGoals && (
          <div className="flex flex-col items-end mt-2">
            <span className="font-semibold">
              Total Weightage:{" "}
              {(() => {
                const total = calculateTotalWeightage(sortedGoals);
                return `${total}%`;
              })()}
            </span>

            <span className="font-semibold">
              Evaluated Weightage:{" "}
              {(() => {
                const total = calculateTotalEvaluatedWeightage(sortedGoals);
                return `${total}%`;
              })()}
              {MBODetails?.grade ? (
                <span className="text-lg text-primary-500">
                  {" "}
                  - {MBODetails?.grade}
                </span>
              ) : null}
            </span>

            {/* Validation messages */}
            {(() => {
              const totalWeightage = calculateTotalWeightage(sortedGoals);

              if (totalWeightage < 100 && sortedGoals.length > 0) {
                return (
                  <span className="text-sm text-red-500">
                    ⚠️ Total weightage must be at least 100% to save (currently
                    ${totalWeightage}%)
                  </span>
                );
              } else if (totalWeightage > 150 && sortedGoals.length > 0) {
                return (
                  <span className="text-sm text-red-500">
                    ⚠️ Total weightage exceeds 150% (${totalWeightage}%)
                  </span>
                );
              } else if (
                totalWeightage >= 100 &&
                totalWeightage <= 150 &&
                sortedGoals.length > 0
              ) {
                return (
                  <span className="text-sm text-green-500">
                    ✅ Total weightage is valid ({totalWeightage}%)
                  </span>
                );
              }
              return null;
            })()}
          </div>
        )}
      </div>

      {/* Copy Goals Modal */}
      <Modal
        title="Copy Goals from Other Employee"
        open={isCopyModalVisible}
        onCancel={() => {
          setIsCopyModalVisible(false);
          setSelectedEmployee(null);
          setEmployeeGoals([]);
        }}
        footer={null}
        width={800}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Employee
            </label>
            {isLoadingEmployees ? (
              <div className="border rounded p-4 text-center">
                <div className="text-gray-500 mb-2">Loading employees...</div>
                <Loading3QuartersOutlined spin />
              </div>
            ) : (
              <Select
                placeholder="Search and select employee"
                value={selectedEmployee?.email}
                onChange={async (value) => {
                  const employee = availableEmployees.find(
                    (emp) => emp.email === value
                  );
                  setSelectedEmployee(employee);
                  setEmployeeGoals([]);

                  // Automatically load goals when employee is selected
                  if (employee) {
                    await handleCopyGoals(employee);
                  }
                }}
                showSearch
                filterOption={(input, option) => {
                  const employee = availableEmployees.find(
                    (emp) => emp.email === option.value
                  );
                  if (!employee) return false;

                  const searchText = input.toLowerCase();
                  const name =
                    `${employee.first_name} ${employee.last_name}`.toLowerCase();
                  const email = employee.email.toLowerCase();

                  return (
                    name.includes(searchText) || email.includes(searchText)
                  );
                }}
                style={{ width: "100%" }}
              >
                {availableEmployees.map((employee) => {
                  const experienceMonths = calculateEmployeeExperience(
                    employee.career_start_date
                  );
                  const experienceText =
                    experienceMonths > 0
                      ? ` - ${formatMonthsIntoYearsAndMonths(experienceMonths)}`
                      : "";
                  return (
                    <Select.Option key={employee.email} value={employee.email}>
                      <div className="flex items-center gap-2">
                        <ProfileAvatar
                          profilePic={employee.profile_pic}
                          firstName={employee.first_name}
                          lastName={employee.last_name}
                          email={employee.email}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">
                            {employee.first_name} {employee.last_name}
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            {employee.email}
                            {experienceText}
                          </div>
                        </div>
                      </div>
                    </Select.Option>
                  );
                })}
              </Select>
            )}
          </div>

          {selectedEmployee && (
            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Available Goals
                </label>
                {isLoadingEmployeeGoals && (
                  <div className="text-sm text-gray-500">Loading goals...</div>
                )}
              </div>

              {isLoadingEmployeeGoals ? (
                <div className="border rounded p-6 text-center">
                  <div className="text-gray-500 mb-2">
                    Loading employee goals...
                  </div>
                  <Loading3QuartersOutlined spin />
                </div>
              ) : employeeGoals.length > 0 ? (
                <div>
                  <div className="flex justify-end mb-2">
                    <Button
                      type="primary"
                      size="small"
                      onClick={() =>
                        handleCopySelectedGoals(
                          employeeGoals.map((goal) => goal.id)
                        )
                      }
                    >
                      Copy All ({employeeGoals.length})
                    </Button>
                  </div>
                  <div className="border rounded p-3 max-h-60 overflow-y-auto">
                    {employeeGoals.map((goal, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 border-b last:border-b-0"
                      >
                        <div className="flex-1 min-w-0">
                          <div className="font-medium break-words">
                            {goal.category}
                          </div>
                          <div className="text-sm text-gray-600 break-words">
                            {goal.goal}
                          </div>
                          <div className="text-xs text-gray-500">
                            Weightage: {goal.weightage}%
                          </div>
                        </div>
                        <Button
                          size="small"
                          onClick={() => handleCopySelectedGoals([goal.id])}
                        >
                          Copy
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="border rounded p-4 text-center text-gray-500">
                  No goals found for this employee.
                </div>
              )}
            </div>
          )}
        </div>
      </Modal>
    </>
  );
}

export default AddUpdateGoals;

function AddGoalForm({
  onAdd,
  MBOGoals,
  goalsWithWeightage,
  getFilteredGoals,
}) {
  const [form, setForm] = useState({
    category: "",
    goal: "",
    weightage: "",
    note: "",
    purpose: "",
    customSuccessCriteria: "",
  });
  const [adding, setAdding] = useState(false);

  const handleAdd = () => {
    if (!form.category || !form.goal || !form.weightage) return;

    const weightageNum = Number(form.weightage);
    if (isNaN(weightageNum) || weightageNum < 0 || weightageNum > 30) {
      message.error("Please enter a valid weightage between 0 and 30");
      return;
    }

    // Check if adding this weightage would exceed 150%
    const currentTotal = calculateTotalWeightage(goalsWithWeightage);
    if (currentTotal + weightageNum > 150) {
      message.error(
        `Adding ${weightageNum}% would exceed 150% total weightage (current: ${currentTotal}%)`
      );
      return;
    }

    onAdd({
      ...form,
      weightage: weightageNum,
      order: goalsWithWeightage.length,
      updatedAt: new Date().toISOString(),
    });
    setForm({
      category: "",
      goal: "",
      weightage: "",
      note: "",
      purpose: "",
      customSuccessCriteria: "",
    });
    setAdding(false);
  };

  return adding ? (
    <div className="flex flex-col gap-2 border p-2 rounded bg-gray-100 min-w-0">
      <div className="flex gap-2 flex-wrap min-w-0">
        <div className="flex flex-col w-64 min-w-0">
          <span className="text-xs text-gray-500 mb-1">Category</span>
          <Select
            placeholder="Select Category"
            value={form.category}
            onChange={(e) =>
              setForm((f) => ({ ...f, category: e, goal: undefined }))
            }
            style={{ width: "100%" }}
            allowClear
            showSearch
            dropdownMatchSelectWidth={false}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
            listHeight={400}
            maxTagCount={0}
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {Object.keys(MBOGoals)
              .sort()
              .map((cat) => (
                <Select.Option value={cat} key={cat}>
                  {cat}
                </Select.Option>
              ))}
          </Select>
        </div>
        <div className="flex flex-col flex-1 min-w-0">
          <span className="text-xs text-gray-500 mb-1">Goal</span>
          <Select
            placeholder="Select Goal"
            value={form.goal}
            onChange={(e) => setForm((f) => ({ ...f, goal: e }))}
            style={{ width: "100%" }}
            allowClear
            showSearch
            dropdownMatchSelectWidth={false}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
            disabled={!form.category || !MBOGoals[form.category]}
            listHeight={400}
            maxTagCount={0}
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {(getFilteredGoals
              ? getFilteredGoals(form.category)
              : MBOGoals[form.category] || []
            ).map((g) => (
              <Select.Option value={g.goal} key={g.goal}>
                {g.goal}
              </Select.Option>
            ))}
          </Select>
        </div>
        <div className="flex flex-col" style={{ width: 100 }}>
          <span className="text-xs text-gray-500 mb-1">Weightage</span>
          <Input
            placeholder="Weightage"
            type="number"
            min={0}
            max={30}
            value={form.weightage}
            onChange={(e) => {
              const value = e.target.value;
              const numValue = Number(value);

              // Only allow valid numbers between 0 and 30 (individual goal max)
              if (
                value === "" ||
                (numValue >= 0 && numValue <= 30 && !isNaN(numValue))
              ) {
                setForm((f) => ({ ...f, weightage: value }));
              } else if (numValue > 30) {
                message.error("Individual goal weightage cannot exceed 30%");
              }
            }}
            style={{ width: 100 }}
          />
          {(() => {
            const currentTotal = calculateTotalWeightage(goalsWithWeightage);
            const remaining = 150 - currentTotal;
            return (
              <span className="text-xs text-gray-500 mt-1">
                Remaining: {remaining}%
              </span>
            );
          })()}
        </div>
      </div>

      {/* Purpose and Custom Success Criteria fields */}
      <div className="flex flex-col gap-2 mt-2">
        <div className="flex flex-col">
          <span className="text-xs text-gray-500 mb-1">Purpose (Optional)</span>
          <Input.TextArea
            placeholder="Enter the purpose of this goal..."
            value={form.purpose}
            onChange={(e) =>
              setForm((f) => ({ ...f, purpose: e.target.value }))
            }
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </div>
        <div className="flex flex-col">
          <span className="text-xs text-gray-500 mb-1">
            Success Criteria (Optional)
          </span>
          <Input.TextArea
            placeholder="Enter the success criteria for this goal..."
            value={form.customSuccessCriteria}
            onChange={(e) =>
              setForm((f) => ({ ...f, customSuccessCriteria: e.target.value }))
            }
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </div>
      </div>

      <div className="flex gap-2 mt-2">
        <Button
          type="primary"
          onClick={handleAdd}
          disabled={!form.category || !form.goal || !form.weightage}
        >
          Add Goal
        </Button>
        <Button onClick={() => setAdding(false)}>Cancel</Button>
      </div>
    </div>
  ) : (
    <Button type="dashed" onClick={() => setAdding(true)}>
      + Add Goal
    </Button>
  );
}
