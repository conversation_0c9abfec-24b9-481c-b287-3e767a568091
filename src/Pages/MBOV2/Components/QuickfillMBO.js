import { But<PERSON>, Modal, Select } from "antd";
import React, { useMemo, useState } from "react";
import { createPeriod, getNextQuarterString } from "../Actions/Utility";
import AddUpdateGoals from "./AddUpdateGoals";
import { useSelector } from "react-redux";
import { getCurrentUserData } from "store/slices/loginSlice";
import { handleCreateMBO } from "../Actions/Actions";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import { LeftCircleOutlined, RightCircleOutlined } from "@ant-design/icons";

function QuickfillMBO({ directReportees }) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { email: currentUserEmail } = useSelector(getCurrentUserData);

  const nextQuarter = useMemo(() => getNextQuarterString(), []);

  const currentEmployee = directReportees?.[currentIndex];

  const handleSaveAndNext = async () => {
    let input = {
      ...MBODetails,
      status: "Target Set",
      goals: goalsWithWeightage,
    };

    await handleCreateMBO(input);
    if (currentIndex + 1 < directReportees.length) {
      setCurrentIndex(currentIndex + 1);
      setGoalsWithWeightage([]);
    } else {
      setIsModalOpen(false); // Close when all are done
    }
  };

  // const MBODetails = {
  //   quarter: nextQuarter.quarter,
  //   year: nextQuarter.year,
  //   employeeMbosId: currentEmployee?.email,
  //   mBOAddedById: currentUserEmail,
  //   mBOLastUpdatedById: currentUserEmail,
  //   status: "Target unset",
  // };

  const [goalsWithWeightage, setGoalsWithWeightage] = useState([]);

  // TODO: SKip EMployees with existing MBOs found in DB
  const [selectedQuarter, setSelectedQuarter] = useState(nextQuarter);

  // Helper to get current quarter
  const getCurrentQuarterString = () => {
    const now = new Date();
    const quarter = Math.floor((now.getMonth() + 3) / 3) - 1;
    return { year: now.getFullYear(), quarter };
  };

  const quarterOptions = [
    {
      label: `Current (${createPeriod(
        getCurrentQuarterString().year,
        getCurrentQuarterString().quarter
      )})`,
      value: JSON.stringify(getCurrentQuarterString()),
    },
    {
      label: `Next (${createPeriod(nextQuarter.year, nextQuarter.quarter)})`,
      value: JSON.stringify(nextQuarter),
    },
  ];

  // Update MBODetails to use selectedQuarter
  const MBODetails = {
    quarter: selectedQuarter.quarter,
    year: selectedQuarter.year,
    employeeMbosId: currentEmployee?.email,
    mBOAddedById: currentUserEmail,
    mBOLastUpdatedById: currentUserEmail,
    status: "Target unset",
  };

  console.log(quarterOptions);

  return (
    <div>
      <Button type="primary" onClick={() => setIsModalOpen(true)}>
        Quick Fill MBO
      </Button>

      <Modal
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
        width={"90%"}
        closable={false}
        title={
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-x-2">
              Quick Fill MBO for
              <span style={{ marginLeft: 8 }}>
                <Select
                  value={JSON.stringify(selectedQuarter)}
                  onChange={(value) => setSelectedQuarter(JSON.parse(value))}
                  options={quarterOptions.map((opt) => ({
                    label: opt.label,
                    value: opt.value,
                  }))}
                  size="small"
                />
              </span>
            </div>
            <div className="flex items-center gap-x-2">
              {currentIndex !== 0 && (
                <LeftCircleOutlined
                  className="text-xl"
                  onClick={() => setCurrentIndex(currentIndex - 1)}
                />
              )}
              {currentIndex + 1 !== directReportees.length && (
                <RightCircleOutlined
                  className="text-xl"
                  onClick={() => setCurrentIndex(currentIndex + 1)}
                />
              )}
            </div>
          </div>
        }
      >
        <div>
          <RenderEmployeeFullName
            employee={currentEmployee}
            noRedirect
            avatarSize={32}
            showAvatar
            showPopover
          />

          <AddUpdateGoals
            goalsWithWeightage={goalsWithWeightage}
            setGoalsWithWeightage={setGoalsWithWeightage}
            MBODetails={MBODetails}
          />

          <div className="flex justify-end mt-4">
            <Button type="primary" onClick={handleSaveAndNext}>
              Save & Next
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default QuickfillMBO;
