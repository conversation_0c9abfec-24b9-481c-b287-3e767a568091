import React from "react";
import { But<PERSON>, Result } from "antd";
import { useNavigate } from "react-router-dom";
import { Grid } from "antd";
import logo from "../../assets/YorkIEhub1.svg";
import { primaryColor } from "theme";

const { useBreakpoint } = Grid;

/**
 * 404 Not Found Page Component
 *
 * Displays when users navigate to unknown routes
 */
const NotFound = () => {
  const navigate = useNavigate();
  const screens = useBreakpoint();

  const handleGoHome = () => {
    navigate("/");
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  // Responsive styles based on screen size
  const getResponsiveStyles = () => {
    const isMobile = !screens.md;
    const isTablet = screens.md && !screens.lg;
    const isDesktop = screens.lg;

    return {
      container: {
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        background: "#f5f5f5",
        padding: isMobile ? "16px" : isTablet ? "24px" : "32px",
      },
      logoContainer: {
        marginBottom: isMobile ? "24px" : isTablet ? "32px" : "40px",
        textAlign: "center",
      },
      logo: {
        height: isMobile ? "40px" : isTablet ? "48px" : "56px",
        cursor: "pointer",
        transition: "transform 0.2s ease-in-out",
      },
      resultContainer: {
        maxWidth: isMobile ? "100%" : isTablet ? "500px" : "600px",
        width: "100%",
        padding: isMobile ? "16px" : "24px",
      },
      buttonContainer: {
        display: "flex",
        flexDirection: isMobile ? "column" : "row",
        gap: isMobile ? "12px" : "8px",
        justifyContent: "center",
        alignItems: "center",
      },
      button: {
        width: isMobile ? "100%" : "auto",
        minWidth: isMobile ? "200px" : "120px",
        height: isMobile ? "44px" : "36px",
        fontSize: isMobile ? "16px" : "14px",
      },
    };
  };

  const styles = getResponsiveStyles();

  return (
    <div style={styles.container}>
      <div style={styles.logoContainer}>
        <img
          src={logo}
          alt="Logo"
          style={styles.logo}
          onClick={() => navigate("/")}
          onMouseEnter={(e) => {
            e.target.style.transform = "scale(1.05)";
          }}
          onMouseLeave={(e) => {
            e.target.style.transform = "scale(1)";
          }}
        />
      </div>

      <div style={styles.resultContainer}>
        <Result
          status="404"
          title={
            <span
              style={{
                fontSize: screens.xs ? "48px" : screens.sm ? "64px" : "72px",
                fontWeight: "bold",
                color: primaryColor,
              }}
            >
              404
            </span>
          }
          subTitle={
            <span
              style={{
                fontSize: screens.xs ? "16px" : "18px",
                lineHeight: "1.5",
                color: "#666",
              }}
            >
              Sorry, the page you visited does not exist.
            </span>
          }
          extra={
            <div style={styles.buttonContainer}>
              <Button
                type="primary"
                size={screens.xs ? "large" : "middle"}
                onClick={handleGoHome}
                style={styles.button}
              >
                Go Home
              </Button>
              <Button
                size={screens.xs ? "large" : "middle"}
                onClick={handleGoBack}
                style={styles.button}
              >
                Go Back
              </Button>
            </div>
          }
        />
      </div>
    </div>
  );
};

export default NotFound;
