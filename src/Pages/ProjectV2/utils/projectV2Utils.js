import { message } from "antd";
import moment from "moment";
import { minutesToHoursAndMinutes } from "utils/commonMethods";
import {
  ListProjectsAction,
  listTimeSheetByProjectAction,
} from "Pages/ProjectV2/Actions/ProjectActions";

export const getMostRecentNote = (notes, dateFormat) => {
  if (!Array.isArray(notes) || notes.length === 0) return null;

  let parsedData = [];
  try {
    const firstItem = JSON.parse(notes[0]);
    if (Array.isArray(firstItem)) parsedData = firstItem;
  } catch (error) {
    console.error("Failed to parse notes[0]:", error);
    return null;
  }

  let mostRecentNote = null;
  let mostRecentDate = null;

  for (const item of parsedData) {
    const date = moment(item?.date, dateFormat);
    if (!date.isValid()) continue;
    if (!mostRecentDate || date.isAfter(mostRecentDate)) {
      mostRecentDate = date;
      mostRecentNote = item;
    }
  }

  return mostRecentNote;
};

//Function to get most recent project note
export const getMostRecentProjectNote = (notes, dateFormat = null) => {
  if (!notes || !Array.isArray(notes.items) || notes.items.length === 0) {
    return null;
  }

  const sorted = [...notes.items].sort((a, b) => {
    const dateA = moment(a.date);
    const dateB = moment(b.date);
    return dateB.diff(dateA); // descending
  });

  const mostRecent = sorted[0];
  return mostRecent;
};

/**
 * Fetch timesheet entries for multiple projects in batches.
 */
export async function batchedListTimeSheetAction(
  projectIds,
  startTime,
  endTime
) {
  const allResultStore = [];

  for (const id of projectIds) {
    try {
      const variables = {
        timeSheetProjectId: id,
        filter: {
          and: [
            { start_time: { ge: startTime } },
            { end_time: { le: endTime } },
          ],
        },
      };
      const batchResults = await listTimeSheetByProjectAction(variables);
      allResultStore.push(Array.isArray(batchResults) ? batchResults : []);
    } catch (error) {
      console.error(
        `Error fetching batch results for project ID: ${id}`,
        error
      );
      message.error(`Failed to fetch timesheet data for project ID: ${id}`);
      allResultStore.push([]);
    }
  }

  return allResultStore;
}
/**
 * Calculates weekly time burn and allocation per project over the last timeSheetDays.
 */
export async function computeWeeklyBurnData(projects, timeSheetDays) {
  const projectIds = projects
    ?.filter((project) => project.status !== "INACTIVE")
    ?.map((project) => project.id);

  const startTime = moment()
    .startOf("day")
    .subtract(timeSheetDays, "day")
    .unix();
  const endTime = moment().endOf("day").unix();

  const timeSheetData = await batchedListTimeSheetAction(
    projectIds,
    startTime,
    endTime
  );
  const projectWiseAllocation = projects?.reduce((acc, project) => {
    acc[project.id] =
      project?.employee_project_allocation?.items?.reduce(
        (count, allocationItem) => count + allocationItem?.allocation,
        0
      ) || 0;
    return acc;
  }, {});

  const projectWiseData = timeSheetData.flat().reduce((acc, item) => {
    const totalMinutes = Math.abs(item.end_time - item.start_time) / 60;
    acc[item?.timeSheetProjectId] =
      (acc[item?.timeSheetProjectId] || 0) + totalMinutes;
    return acc;
  }, {});

  Object.keys(projectWiseData).forEach((projectId) => {
    projectWiseData[projectId] = minutesToHoursAndMinutes(
      projectWiseData[projectId]
    );
  });

  return Object.keys(projectWiseAllocation).reduce((acc, id) => {
    acc[id] = {
      allocation: projectWiseAllocation[id],
      burn: projectWiseData[id] || "00:00",
    };
    return acc;
  }, {});
}
export const fetchProjects = async () => {
  const projectList = await ListProjectsAction();

  return projectList;
};
//Function to get hours and minutes to display on website table and form
export const getHoursToMinutesFormat = (hours) => {
  const integerPart = parseInt(hours);
  let decimalPart = hours - integerPart;
  decimalPart = parseFloat(decimalPart.toFixed(2));
  let calculatedTime;
  if ([0.75, 0.5, 0.25].includes(decimalPart)) {
    calculatedTime = minutesToHoursAndMinutes(Math.abs(hours) * 60);
  } else {
    calculatedTime = minutesToHoursAndMinutes(
      Math.abs(integerPart) * 60 + decimalPart * 100
    );
  }

  return calculatedTime;
};

//Function to get hours and minutes to display on pie/column chart
export const getHoursToMinutesTableFormat = (hours) => {
  const calculatedTime = getHoursToMinutesFormat(hours);
  const hoursTableFormat = parseFloat(calculatedTime.replace(":", "."));
  return hoursTableFormat;
};
export const calculateFontSize = () => {
  const screenWidth = window.innerWidth;
  switch (true) {
    case screenWidth <= 1300:
      return 6;
    case screenWidth <= 1400:
      return 10;
    default:
      return 12;
  }
};

//Function to calculate project buckets remaining hours
export const calculateRemainingBucketHours = async (timesheets) => {
  let tableColumnsData = [];
  timesheets.forEach((timesheet) => {
    const start_time = moment.unix(timesheet.start_time);
    const end_time = moment.unix(timesheet.end_time);
    const date = start_time.format("YYYY-MM-DD");
    const workedHours = end_time.diff(start_time, "hours", true);
    const timesheetDataIndex = tableColumnsData?.findIndex(
      (data) =>
        data.date === date && data.bucketName === timesheet?.projectBucket
    );
    const employeeData = {
      workedHours: workedHours,
      name: `${timesheet.employee.first_name} ${timesheet.employee.last_name}`,
      employeeId: timesheet.employeeID,
    };
    if (timesheetDataIndex === -1) {
      tableColumnsData.push({
        date: date,
        totalWorkedHours: workedHours,
        employees: [employeeData],
        bucketName: timesheet?.projectBucket,
      });
    } else {
      const employeeIndex = tableColumnsData[timesheetDataIndex][
        "employees"
      ]?.findIndex((emp) => emp.employeeId === timesheet.employeeID);
      tableColumnsData[timesheetDataIndex]["totalWorkedHours"] += workedHours;
      if (employeeIndex === -1) {
        tableColumnsData[timesheetDataIndex]["employees"].push(employeeData);
      } else {
        tableColumnsData[timesheetDataIndex]["employees"][employeeIndex][
          "workedHours"
        ] += workedHours;
      }
    }
  });
  return tableColumnsData.sort((a, b) => new Date(a.date) - new Date(b.date));
};

export const getProjectInitials = (projectName) => {
  // Remove special characters and digits from words
  const words = projectName
    ?.trim()
    ?.split(" ")
    ?.filter(Boolean)
    ?.map((word) => word.replace(/[^a-zA-Z]/g, "")) // remove non-letter characters
    ?.filter(Boolean); // remove any empty strings after cleanup

  if (words?.length >= 3) {
    return words
      .slice(0, 3)
      .map((w) => w[0])
      .join("")
      .toUpperCase();
  }

  if (words?.length === 2) {
    return words
      .map((w) => w[0])
      .join("")
      .toUpperCase();
  }

  if (words?.length === 1) {
    const word = words[0];

    // Match uppercase letters only
    const camelCaseMatch = word.match(/[A-Z]/g);
    if (camelCaseMatch && camelCaseMatch.length >= 2) {
      return camelCaseMatch.slice(0, 2).join("").toUpperCase();
    }
    return word.slice(0, 2).toUpperCase();
  }
};
