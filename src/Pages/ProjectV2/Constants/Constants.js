export const tagColors = ["gold", "purple", "blue"];

export const STATUS_OPTIONS = [
  { label: "RED", value: "RED" },
  { label: "YELLOW", value: "YELLOW" },
  { label: "G<PERSON><PERSON>", value: "GRE<PERSON>" },
];

// Define status priority
export const statusPriority = {
  RED: 1,
  YELLOW: 2,
  GREEN: 3,
  INACTIVE: 4,
};

// Code Review Checklist
// NOTE: Also change this in backend
export const CODE_REVIEW_CHECKLIST_DETAIL = {
  areCodeChangesOptimized: {
    label: "Are Code Changes Optimized",
  },
  areCodeChangesRelative: {
    label: "Are Code Changes Relative",
  },
  isCodeFormatted: {
    label: "Is Code Formatted",
  },
  isCodeWellWritten: {
    label: "Is Code Well Written",
  },
  areCommentsWritten: {
    label: "Are Comments Written",
  },
  cyclomaticComplexityScore: {
    label: "Cyclomatic Complexity Score",
  },
  missingElements: {
    label: "Missing Elements",
  },
  loopholes: {
    label: "Loopholes",
  },
  isCommitMessageWellWritten: {
    label: "Is Commit Message Well Written",
  },
  isNamingConventionFollowed: {
    label: "Is Naming Convention Followed",
  },
  areThereAnySpellingMistakes: {
    label: "Are There Any Spelling Mistakes",
  },
  securityConcernsAny: {
    label: "Security Concerns Any",
  },
  isCodeDuplicated: {
    label: "Is Code Duplicated",
  },
  areConstantsDefinedCentrally: {
    label: "Are Constants Defined Centrally",
  },
  isCodeModular: {
    label: "Is Code Modular",
  },
  isLoggingDoneProperly: {
    label: "Is Logging Done Properly",
  },
  score: {
    label: "Score",
    disabled: true,
  },
};
export const checklistItems = [
  { key: "tags", label: "Tags not added", section: "Settings" },
  { key: "slack", label: "Slack Channel setup pending", section: "Settings" },
  { key: "devTeam", label: "Dev Team Setup pending", section: "Allocations" },
  { key: "allocation", label: "Allocation pending", section: "Allocations" },
  { key: "dashboard", label: "Dashboard link not added", section: "Settings" },
  { key: "backlog", label: "Backlog link not added", section: "Settings" },
  { key: "clientPOC", label: "Client POC not added", section: "Settings" },
  { key: "meetings", label: "Meetings table empty", section: "Settings" },
];

export const numberPattern = /^\d+(\.\d{1,2})?$/;
