export const listProjectsCustomQuery = /* GraphQL */ `
  query ListProjects(
    $filter: ModelProjectFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listProjects(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        notion_dashboard_link
        notion_backlog_link
        external_slack_channel_id
        internal_slack_channel_id
        promptConfigurations
        project_history {
          items {
            client_POC
            cost
            costType
            createdAt
            employee_project_allocation
            end_time
            id
            ownerID
            projectHistoryDev_principalId
            projectHistoryProduct_managerId
            projectHistoryProduct_strategistId
            projectProject_historyId
            start_time
            dev_principal {
              first_name
              last_name
              email
              profile_pic
            }
            product_manager {
              first_name
              last_name
              email
              profile_pic
            }
            product_strategist {
              first_name
              last_name
              email
              profile_pic
            }
          }
        }

        squad {
          name
          budget
          squadSquad_managerId
          ProductUnit {
            name
            unit_manager {
              first_name
              last_name
            }
          }
        }
        employee_project_allocation {
          items {
            allocation
            title
            employee {
              email
              first_name
              last_name
            }
            id
            employeeEmployee_project_allocationId
            projectEmployee_project_allocationId
          }
          nextToken
        }
        client {
          id
          name
        }
        mrr
        fixed_cost
        budget
        start_time
        end_time
        project_buckets {
          name
          hours
          cost
          isActive
          reminder
        }
        product_strategist {
          email
          employee_id
          first_name
          last_name
          profile_pic
        }
        product_manager {
          email
          employee_id
          first_name
          last_name
          profile_pic
        }
        project_lead {
          email
          employee_id
          first_name
          last_name
          profile_pic
        }
        client_POC
        status
        dev_principal {
          email
          employee_id
          first_name
          last_name
          profile_pic
        }
        ProjectTag {
          items {
            id
            projectTag {
              title
            }
            projectTagID
            updatedAt
            projectID
            createdAt
          }
        }
        project_notes {
          items {
            id
            note
            date
            createdAt
            updatedAt
            projectProject_notesId
            projectNoteAddedbyId
            addedby {
              email
              first_name
              last_name
            }
            __typename
          }
          nextToken
          __typename
        }
        time_tracking_required
        notes
        extra_links
        logo
        domain
        toolsUsed
        techStack
        project_app_type
        design_link
        createdAt
        updatedAt
        squadProjectId
        clientProjectsId
        projectProduct_strategistId
        projectProduct_managerId
        project_type
        projectDev_principalId
        bursts
        potential_downgrade_date
        cancellation_date
        meetings {
          items {
            projectMeetingsNotes {
              items {
                notes
                zoomMeeting {
                  start_time
                  topic
                }
              }
            }
          }
        }
      }
      nextToken
    }
  }
`;
export const getProject = /* GraphQL */ `
  query GetProject($id: ID!) {
    getProject(id: $id) {
      id
      name
      notion_dashboard_link
      notion_backlog_link
      external_slack_channel_id
      internal_slack_channel_id
      promptConfigurations
      project_history {
        items {
          client_POC
          cost
          costType
          createdAt
          employee_project_allocation
          end_time
          id
          ownerID
          projectHistoryDev_principalId
          projectHistoryProduct_managerId
          projectHistoryProduct_strategistId
          projectProject_historyId
          start_time
          dev_principal {
            first_name
            last_name
            email
            profile_pic
          }
          product_manager {
            first_name
            last_name
            email
            profile_pic
          }
          product_strategist {
            first_name
            last_name
            email
            profile_pic
          }
        }
      }

      squad {
        name
        budget
        squadSquad_managerId
        ProductUnit {
          name
          unit_manager {
            first_name
            last_name
          }
        }
      }
      employee_project_allocation {
        items {
          allocation
          title
          employee {
            email
            first_name
            last_name
          }
          id
          employeeEmployee_project_allocationId
          projectEmployee_project_allocationId
        }
        nextToken
      }
      client {
        id
        name
      }
      mrr
      fixed_cost
      budget
      start_time
      end_time
      project_buckets {
        name
        hours
        cost
        isActive
        reminder
      }
      product_strategist {
        email
        employee_id
        first_name
        last_name
        profile_pic
      }
      product_manager {
        email
        employee_id
        first_name
        last_name
        profile_pic
      }
      project_lead {
        email
        employee_id
        first_name
        last_name
        profile_pic
      }
      client_POC
      status
      dev_principal {
        email
        employee_id
        first_name
        last_name
        profile_pic
      }
      ProjectTag {
        items {
          id
          projectTag {
            title
          }
          projectTagID
          updatedAt
          projectID
          createdAt
        }
      }
      project_notes {
        items {
          id
          note
          date
          createdAt
          updatedAt
          projectProject_notesId
          projectNoteAddedbyId
          __typename
          addedby {
            email
            first_name
            last_name
          }
        }

        nextToken
        __typename
      }
      time_tracking_required
      notes
      extra_links
      logo
      createdAt
      updatedAt
      squadProjectId
      clientProjectsId
      projectProduct_strategistId
      projectProduct_managerId
      project_type
      projectDev_principalId
      bursts
      potential_downgrade_date
      cancellation_date
      meetings {
        items {
          projectMeetingsNotes {
            items {
              notes
              zoomMeeting {
                start_time
                topic
              }
            }
          }
        }
      }
    }
  }
`;

export const listProjectHistoriesCustomQuery = /* GraphQL */ `
  query ListProjectHistories(
    $filter: ModelProjectHistoryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listProjectHistories(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        Project {
          id
          name
          notion_dashboard_link
          notion_backlog_link
          mrr
          fixed_cost
          budget
          start_time
          end_time
          client_POC
          status
          time_tracking_required
          notes
          extra_links
          project_type
          internal_slack_channel_id
          external_slack_channel_id
          jiraProjectId
          createdAt
          updatedAt
          squadProjectId
          clientProjectsId
          projectProduct_strategistId
          projectProduct_managerId
          projectDev_principalId
          __typename
        }
        start_time
        end_time
        employee_project_allocation
        costType
        cost
        product_strategist {
          email
          employee_id
          account_status
          first_name
          last_name
          mobile
          reward_points
          slack_id
          blood_group
          emergency_contact_num
          emergency_contact_relation
          married
          spouse_full_name
          pan_card
          aadhar_card
          form12BB
          experience_letter
          passport_photo
          address_proof
          resignation_letter
          salary_slip
          salary_slip_1
          salary_slip_2
          personal_email
          york_appointment
          york_agreement
          reffered_by
          address
          birth_date
          anniversary_date
          career_start_date
          york_start_date
          york_end_date
          profile_pic
          profile_pic_requested
          active
          documents
          gender
          facebook_link
          linkedin_link
          twitter_link
          instagram_link
          usual_starting_time
          epf
          uan
          hidden_profile
          isInterviewer
          sick_leave_balance
          privilege_leave_balance
          day_start_time
          day_end_time
          allocatedDesk
          wishlist
          introduction
          configurations
          metadata
          wfh_balance
          country
          createdAt
          updatedAt
          squadEmployeeId
          guildEmployeeId
          employeeTitleId
          employeeReporting_toId
          employeeSquadId
          employeeGuildId
          __typename
        }
        product_manager {
          email
          employee_id
          account_status
          first_name
          last_name
          mobile
          reward_points
          slack_id
          blood_group
          emergency_contact_num
          emergency_contact_relation
          married
          spouse_full_name
          pan_card
          aadhar_card
          form12BB
          experience_letter
          passport_photo
          address_proof
          resignation_letter
          salary_slip
          salary_slip_1
          salary_slip_2
          personal_email
          york_appointment
          york_agreement
          reffered_by
          address
          birth_date
          anniversary_date
          career_start_date
          york_start_date
          york_end_date
          profile_pic
          profile_pic_requested
          active
          documents
          gender
          facebook_link
          linkedin_link
          twitter_link
          instagram_link
          usual_starting_time
          epf
          uan
          hidden_profile
          isInterviewer
          sick_leave_balance
          privilege_leave_balance
          day_start_time
          day_end_time
          allocatedDesk
          wishlist
          introduction
          configurations
          metadata
          wfh_balance
          country
          createdAt
          updatedAt
          squadEmployeeId
          guildEmployeeId
          employeeTitleId
          employeeReporting_toId
          employeeSquadId
          employeeGuildId
          __typename
        }
        client_POC
        dev_principal {
          email
          employee_id
          account_status
          first_name
          last_name
          mobile
          reward_points
          slack_id
          blood_group
          emergency_contact_num
          emergency_contact_relation
          married
          spouse_full_name
          pan_card
          aadhar_card
          form12BB
          experience_letter
          passport_photo
          address_proof
          resignation_letter
          salary_slip
          salary_slip_1
          salary_slip_2
          personal_email
          york_appointment
          york_agreement
          reffered_by
          address
          birth_date
          anniversary_date
          career_start_date
          york_start_date
          york_end_date
          profile_pic
          profile_pic_requested
          active
          documents
          gender
          facebook_link
          linkedin_link
          twitter_link
          instagram_link
          usual_starting_time
          epf
          uan
          hidden_profile
          isInterviewer
          sick_leave_balance
          privilege_leave_balance
          day_start_time
          day_end_time
          allocatedDesk
          wishlist
          introduction
          configurations
          metadata
          wfh_balance
          country
          createdAt
          updatedAt
          squadEmployeeId
          guildEmployeeId
          employeeTitleId
          employeeReporting_toId
          employeeSquadId
          employeeGuildId
          __typename
        }
        createdAt
        updatedAt
        projectProject_historyId
        projectHistoryProduct_strategistId
        projectHistoryProduct_managerId
        projectHistoryDev_principalId
        ownerID
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const timeSheetByProjectCustomQuery = /* GraphQL */ `
  query TimeSheetByProject(
    $timeSheetProjectId: ID!
    $createdAt: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelTimeSheetFilterInput
    $limit: Int
    $nextToken: String
  ) {
    timeSheetByProject(
      timeSheetProjectId: $timeSheetProjectId
      createdAt: $createdAt
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        start_time
        end_time
        employeeID
        timeSheetProjectId
        projectBucket
        employee {
          email
          employee_id
          account_status
          first_name
          last_name
          mobile
          reward_points
          slack_id
          blood_group
          emergency_contact_num
          emergency_contact_relation
          married
          spouse_full_name
          pan_card
          aadhar_card
          form12BB
          experience_letter
          passport_photo
          address_proof
          resignation_letter
          salary_slip
          salary_slip_1
          salary_slip_2
          personal_email
          york_appointment
          york_agreement
          reffered_by
          address
          birth_date
          anniversary_date
          career_start_date
          york_start_date
          york_end_date
          profile_pic
          profile_pic_requested
          active
          documents
          gender
          facebook_link
          linkedin_link
          twitter_link
          instagram_link
          usual_starting_time
          epf
          uan
          hidden_profile
          isInterviewer
          sick_leave_balance
          privilege_leave_balance
          day_start_time
          day_end_time
          allocatedDesk
          wishlist
          introduction
          configurations
          createdAt
          updatedAt
          squadEmployeeId
          guildEmployeeId
          employeeTitleId
          employeeReporting_toId
          employeeSquadId
          employeeGuildId
          __typename
        }
        project {
          id
          name
          notion_dashboard_link
          notion_backlog_link
          mrr
          fixed_cost
          budget
          start_time
          end_time
          client_POC
          status
          time_tracking_required
          notes
          extra_links
          project_type
          internal_slack_channel_id
          external_slack_channel_id
          createdAt
          updatedAt
          squadProjectId
          clientProjectsId
          projectProduct_strategistId
          projectProduct_managerId
          projectDev_principalId
          __typename
        }
        description
        createdAt
        approval_status
        approved_by {
          email
          employee_id
          account_status
          first_name
          last_name
          mobile
          reward_points
          slack_id
          blood_group
          emergency_contact_num
          emergency_contact_relation
          married
          spouse_full_name
          pan_card
          aadhar_card
          form12BB
          experience_letter
          passport_photo
          address_proof
          resignation_letter
          salary_slip
          salary_slip_1
          salary_slip_2
          personal_email
          york_appointment
          york_agreement
          reffered_by
          address
          birth_date
          anniversary_date
          career_start_date
          york_start_date
          york_end_date
          profile_pic
          profile_pic_requested
          active
          documents
          gender
          facebook_link
          linkedin_link
          twitter_link
          instagram_link
          usual_starting_time
          epf
          uan
          hidden_profile
          isInterviewer
          sick_leave_balance
          privilege_leave_balance
          day_start_time
          day_end_time
          allocatedDesk
          wishlist
          introduction
          configurations
          createdAt
          updatedAt
          squadEmployeeId
          guildEmployeeId
          employeeTitleId
          employeeReporting_toId
          employeeSquadId
          employeeGuildId
          __typename
        }
        comments
        related_notifications
        updatedAt
        employeeTimesheet_entriesId
        timeSheetApproved_byId
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const getProjectMeetingsByProjectIDCustomQuery = /* GraphQL */ `
  query GetProjectMeetingsByProjectID(
    $projectID: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelProjectMeetingsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    getProjectMeetingsByProjectID(
      projectID: $projectID
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        type
        meetingID
        isRecurring
        title
        projectID
        project {
          id
          name
          start_time
          end_time
          status
          createdAt
          updatedAt
        }
        internalEmployees {
          items {
            id
            employee {
              email
              first_name
              last_name
              active
            }
          }
          nextToken
          __typename
        }
        isExternal
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;
export const updateProjectMeetings = /* GraphQL */ `
  mutation UpdateProjectMeetings(
    $input: UpdateProjectMeetingsInput!
    $condition: ModelProjectMeetingsConditionInput
  ) {
    updateProjectMeetings(input: $input, condition: $condition) {
      id
    }
  }
`;
export const createProjectMeetings = /* GraphQL */ `
  mutation CreateProjectMeetings($input: CreateProjectMeetingsInput!) {
    createProjectMeetings(input: $input) {
      id
    }
  }
`;
export const createProjectMeetingEmployees = /* GraphQL */ `
  mutation CreateProjectMeetingEmployees(
    $input: CreateProjectMeetingEmployeesInput!
  ) {
    createProjectMeetingEmployees(input: $input) {
      id
    }
  }
`;
export const deleteProjectMeetingEmployees = /* GraphQL */ `
  mutation DeleteProjectMeetingEmployees(
    $input: DeleteProjectMeetingEmployeesInput!
  ) {
    deleteProjectMeetingEmployees(input: $input) {
      id
    }
  }
`;
export const createProjectHistoryCustom = /* GraphQL */ `
  mutation CreateProjectHistory(
    $input: CreateProjectHistoryInput!
    $condition: ModelProjectHistoryConditionInput
  ) {
    createProjectHistory(input: $input, condition: $condition) {
      id
    }
  }
`;
export const createProjectPasswordEmployeesAccess = /* GraphQL */ `
  mutation CreateProjectPasswordEmployeesAccess(
    $input: CreateProjectPasswordEmployeesAccessInput!
    $condition: ModelProjectPasswordEmployeesAccessConditionInput
  ) {
    createProjectPasswordEmployeesAccess(input: $input, condition: $condition) {
      id
      employee {
        email
        employee_id
        first_name
        last_name
        profile_pic
      }
    }
  }
`;
export const deleteProjectPasswordEmployeesAccess = /* GraphQL */ `
  mutation DeleteProjectPasswordEmployeesAccess(
    $input: DeleteProjectPasswordEmployeesAccessInput!
    $condition: ModelProjectPasswordEmployeesAccessConditionInput
  ) {
    deleteProjectPasswordEmployeesAccess(input: $input, condition: $condition) {
      id
    }
  }
`;

export const updateProjectCustomQuery = /* GraphQL */ `
  mutation UpdateProject(
    $input: UpdateProjectInput!
    $condition: ModelProjectConditionInput
  ) {
    updateProject(input: $input, condition: $condition) {
      id
    }
  }
`;

export const listTimeSheetsCustomQuery = /* GraphQL */ `
  query ListTimeSheets(
    $filter: ModelTimeSheetFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listTimeSheets(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        start_time
        approval_status
        end_time
        employeeID
        project {
          id
          name
        }
        employee {
          email
          first_name
          last_name
          profile_pic
          profile_pic_requested
        }
        description
        createdAt
        updatedAt
        employeeTimesheet_entriesId
        timeSheetProjectId
        projectBucket
        related_notifications
      }
      nextToken
    }
  }
`;

export const getPasswordsByProjectID = /* GraphQL */ `
  query PasswordsByProjectID(
    $projectId: ID!
    $sortDirection: ModelSortDirection
    $filter: ModelProjectPasswordsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    PasswordsByProjectID(
      projectId: $projectId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        projectId
        project {
          id
          name
        }
        passwordKey
        encryptedPassword
        createdAt
        employees {
          items {
            id
            employeeID
            projectPasswordsID
            employee {
              first_name
              last_name
              active
              email
              profile_pic
            }
            createdAt
            updatedAt
            email
            __typename
          }
          nextToken
          __typename
        }
        updatedAt
        projectProject_passwordsId
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const listEmployeesForProjectPassword = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listEmployees(
      email: $email
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        email
        employee_id
        first_name
        last_name
        profile_pic
        active
        employee_project_allocation {
          items {
            allocation
            createdAt
            employeeEmployee_project_allocationId
            id
            projectEmployee_project_allocationId
            title
            updatedAt
          }
        }
      }
      nextToken
      __typename
    }
  }
`;
export const updateProjectPasswordEmployeesAccess = /* GraphQL */ `
  mutation UpdateProjectPasswordEmployeesAccess(
    $input: UpdateProjectPasswordEmployeesAccessInput!
    $condition: ModelProjectPasswordEmployeesAccessConditionInput
  ) {
    updateProjectPasswordEmployeesAccess(input: $input, condition: $condition) {
      id
      employee {
        email
        employee_id
        first_name
        last_name
        profile_pic
      }
    }
  }
`;
export const listEmployeesForSelection = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listEmployees(
      email: $email
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        email
        employee_id
        first_name
        last_name
        active
        hidden_profile
        profile_pic
      }
      nextToken
      __typename
    }
  }
`;
export const listSquadsForProject = /* GraphQL */ `
  query ListSquads(
    $name: String
    $filter: ModelSquadFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listSquads(
      name: $name
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        name
        budget
        squad_manager {
          email
          employee_id
          account_status
          first_name
          last_name
          profile_pic
          __typename
        }
        employee {
          nextToken
          __typename
        }
        project {
          nextToken
          __typename
        }
        active
        ProductUnit {
          name
          active
          createdAt
          updatedAt
          productUnitUnit_managerId
          __typename
        }
        createdAt
        updatedAt
        productUnitSquadId
        squadSquad_managerId
        __typename
      }
      nextToken
      __typename
    }
  }
`;

export const deleteEmployeeProjectAllocationForProject = /* GraphQL */ `
  mutation DeleteEmployeeProjectAllocation(
    $input: DeleteEmployeeProjectAllocationInput!
    $condition: ModelEmployeeProjectAllocationConditionInput
  ) {
    deleteEmployeeProjectAllocation(input: $input, condition: $condition) {
      allocation
      __typename
    }
  }
`;
export const createEmployeeProjectAllocationForProject = /* GraphQL */ `
  mutation CreateEmployeeProjectAllocation(
    $input: CreateEmployeeProjectAllocationInput!
    $condition: ModelEmployeeProjectAllocationConditionInput
  ) {
    createEmployeeProjectAllocation(input: $input, condition: $condition) {
      allocation
    }
  }
`;
export const updateProjectForAllocation = /* GraphQL */ `
  mutation UpdateProject(
    $input: UpdateProjectInput!
    $condition: ModelProjectConditionInput
  ) {
    updateProject(input: $input, condition: $condition) {
      id
      __typename
    }
  }
`;
export const updateEmployeeProjectAllocationForProject = /* GraphQL */ `
  mutation UpdateEmployeeProjectAllocation(
    $input: UpdateEmployeeProjectAllocationInput!
    $condition: ModelEmployeeProjectAllocationConditionInput
  ) {
    updateEmployeeProjectAllocation(input: $input, condition: $condition) {
      allocation
    }
  }
`;
export function buildBulkEmployeeProjectAllocationMutation({
  deletes,
  creates,
  updates,
}) {
  let mutationParts = [];
  let variableDefs = [];
  let variables = {};

  // Deletes
  deletes.forEach((id, i) => {
    const alias = `delete${i}`;
    const varName = `deleteId${i}`;
    variableDefs.push(`$${varName}: ID!`);
    mutationParts.push(`
      ${alias}: deleteEmployeeProjectAllocation(input: { id: $${varName} }) {
        id
      }
    `);
    variables[varName] = id;
  });

  // Creates
  creates.forEach((input, i) => {
    const alias = `create${i}`;
    const varName = `createInput${i}`;
    variableDefs.push(`$${varName}: CreateEmployeeProjectAllocationInput!`);
    mutationParts.push(`
      ${alias}: createEmployeeProjectAllocation(input: $${varName}) {
        id
        allocation
      }
    `);
    variables[varName] = input;
  });

  // Updates
  updates.forEach((input, i) => {
    const alias = `update${i}`;
    const varName = `updateInput${i}`;
    variableDefs.push(`$${varName}: UpdateEmployeeProjectAllocationInput!`);
    mutationParts.push(`
      ${alias}: updateEmployeeProjectAllocation(input: $${varName}) {
        id
        allocation
      }
    `);
    variables[varName] = input;
  });

  const mutation = `
    mutation BulkEmployeeProjectAllocation(${variableDefs.join(", ")}) {
      ${mutationParts.join("\n")}
    }
  `;
  return { mutation, variables };
}

export const getTimesheetByProjectID = /* GraphQL */ `
  query TimeSheetByProject(
    $timeSheetProjectId: ID!
    $createdAt: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelTimeSheetFilterInput
    $limit: Int
    $nextToken: String
  ) {
    timeSheetByProject(
      timeSheetProjectId: $timeSheetProjectId
      createdAt: $createdAt
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        start_time
        end_time
        employeeID
        timeSheetProjectId
        projectBucket
        employee {
          email
          first_name
          last_name
          profile_pic
        }
        description
        createdAt
        approval_status
        comments
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const createProjectNoteCustomQuery = /* GraphQL */ `
  mutation CreateProjectNote($input: CreateProjectNoteInput!) {
    createProjectNote(input: $input) {
      id
      note
      date
      addedby {
        email
        first_name
        last_name
        profile_pic
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const updateProjectNoteCustomQuery = /* GraphQL */ `
  mutation UpdateProjectNote($input: UpdateProjectNoteInput!) {
    updateProjectNote(input: $input) {
      id
      note
      date
      addedby {
        email
        first_name
        last_name
        profile_pic
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;
export const deleteProjectNoteCustomQuery = /* GraphQL */ `
  mutation DeleteProjectNote($input: DeleteProjectNoteInput!) {
    deleteProjectNote(input: $input) {
      id
      __typename
    }
  }
`;
