import {
  createProjectMeetingEmployees,
  createProjectMeetings,
  deleteProjectMeetingEmployees,
  listProjectsCustomQuery,
  updateProjectMeetings,
  createProjectHistoryCustom,
  updateProjectCustomQuery,
  listTimeSheetsCustomQuery,
  timeSheetByProjectCustomQuery,
  createProjectPasswordEmployeesAccess,
  deleteProjectPasswordEmployeesAccess,
  getPasswordsByProjectID,
  listEmployeesForProjectPassword,
  updateProjectPasswordEmployeesAccess,
  listProjectHistoriesCustomQuery,
  listEmployeesForSelection,
  listSquadsForProject,
  deleteEmployeeProjectAllocationForProject,
  createEmployeeProjectAllocationForProject,
  updateProjectForAllocation,
  updateEmployeeProjectAllocationForProject,
  buildBulkEmployeeProjectAllocationMutation,
  getProjectMeetingsByProjectIDCustomQuery,
  getProject,
  getTimesheetByProjectID,
  createProjectNoteCustomQuery,
  updateProjectNoteCustomQuery,
  deleteProjectNoteCustomQuery,
} from "Pages/ProjectV2/Actions/Queries";
import {
  ExecuteDynamicMutation,
  ExecuteMutationV2,
  ExecuteQueryCustomV2,
} from "utils/Api";
import CryptoJS from "crypto-js";
import {
  createProjectPasswords,
  deleteProjectPasswords,
  updateProjectPasswords,
} from "graphql/mutations";

export const ListProjectHistories = (filter) => {
  return ExecuteQueryCustomV2(listProjectHistoriesCustomQuery, { filter });
};

export const ListProjectsAction = (filter) => {
  return ExecuteQueryCustomV2(listProjectsCustomQuery, { filter });
};

export const listTimeSheetByProjectAction = (variables) => {
  return ExecuteQueryCustomV2(timeSheetByProjectCustomQuery, variables);
};

export const ListProjectMeetingsByID = (projectID) => {
  return ExecuteQueryCustomV2(getProjectMeetingsByProjectIDCustomQuery, {
    projectID,
  });
};

export const createUpdateProjectMeeting = (input) => {
  return ExecuteMutationV2(
    input?.id ? updateProjectMeetings : createProjectMeetings,
    { input }
  );
};

export const createProjectmeetingEmployee = (input) => {
  return ExecuteMutationV2(createProjectMeetingEmployees, { input });
};

export const deleteProjectmeetingEmployee = (id) => {
  return ExecuteMutationV2(deleteProjectMeetingEmployees, { input: { id } });
};

export const createProjectHistoryAction = (input) => {
  return ExecuteMutationV2(createProjectHistoryCustom, { input });
};

export const UpdateProjectCustomAction = (input) => {
  return ExecuteMutationV2(updateProjectCustomQuery, { input });
};

export const listTimeSheetProjectAction = (filter) => {
  return ExecuteQueryCustomV2(listTimeSheetsCustomQuery, { filter });
};

export const listTimeSheetProjectPaginated = (id, nextToken) => {
  return ExecuteQueryCustomV2(timeSheetByProjectCustomQuery, {
    timeSheetProjectId: id,
    nextToken,
  });
};
export const decryptPassword = (encryptedPassword) => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedPassword, "SECRET_KEY");
    return bytes.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    return "Decryption Failed";
  }
};

export const deletePassword = (id) => {
  return ExecuteMutationV2(deleteProjectPasswords, { input: { id } });
};
export const deleteProjectPasswordEmployeeAccess = (id) => {
  return ExecuteMutationV2(deleteProjectPasswordEmployeesAccess, {
    input: { id },
  });
};
export const listEmployeesForProjectPasswordAction = (filter) => {
  return ExecuteQueryCustomV2(listEmployeesForProjectPassword, { filter });
};

export const listProjectPasswords = (projectId) => {
  return ExecuteQueryCustomV2(getPasswordsByProjectID, { projectId });
};
export const createUpdatePassword = (input) => {
  return ExecuteMutationV2(
    input?.id ? updateProjectPasswords : createProjectPasswords,
    {
      input,
    }
  );
};
export const createUpdateProjectPasswordEmployeesAccessAction = (input) => {
  return ExecuteMutationV2(
    input?.id
      ? updateProjectPasswordEmployeesAccess
      : createProjectPasswordEmployeesAccess,
    { input }
  );
};

export const deleteEmployeeProjectAllocationCustomAction = (inputData) => {
  return ExecuteMutationV2(
    deleteEmployeeProjectAllocationForProject,
    inputData
  );
};
export const listEmployeesForSelectionAction = (filter) => {
  return ExecuteQueryCustomV2(listEmployeesForSelection, { filter });
};
export const listSquadForProjectAction = (filter) => {
  return ExecuteQueryCustomV2(listSquadsForProject, { filter });
};
export const deleteEmployeeProjectAllocationAction = (input) => {
  return ExecuteMutationV2(deleteEmployeeProjectAllocationForProject, {
    input,
  });
};
export const createUpdateEmployeeProjectAllocationAction = (input) => {
  if (input?.id) {
    return ExecuteMutationV2(updateEmployeeProjectAllocationForProject, {
      input,
    });
  } else {
    return ExecuteMutationV2(createEmployeeProjectAllocationForProject, {
      input,
    });
  }
};
export const UpdateProjecForAllocationtAction = (input) => {
  return ExecuteMutationV2(updateProjectForAllocation, { input });
};

export async function executeBulkEmployeeProjectAllocation({
  deletes,
  creates,
  updates,
}) {
  const { mutation, variables } = buildBulkEmployeeProjectAllocationMutation({
    deletes,
    creates,
    updates,
  });
  return ExecuteDynamicMutation(mutation, variables);
}

export const getProjectDetails = (id) => {
  return ExecuteQueryCustomV2(getProject, { id });
};

export const getTimsheetsPerProject = (timeSheetProjectId) => {
  return ExecuteQueryCustomV2(getTimesheetByProjectID, { timeSheetProjectId });
};
export const createProjectNotes = (input) => {
  return ExecuteMutationV2(createProjectNoteCustomQuery, { input });
};
export const updateProjectNotes = (input) => {
  return ExecuteMutationV2(updateProjectNoteCustomQuery, { input });
};

export const deleteProjectNotes = (id) => {
  return ExecuteMutationV2(deleteProjectNoteCustomQuery, { input: { id } });
};
