/* eslint-disable react-hooks/exhaustive-deps */
import { ArrowLeftOutlined } from "@ant-design/icons";

import { Col, Divider, message, Row, Tabs } from "antd";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { boxClass } from "utils/TailwindCommonClasses";
import { useNavigate, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import ProjectCard from "../Components/ProjectCard";
import { computeWeeklyBurnData } from "Pages/ProjectV2/utils/projectV2Utils";
import NotesTab from "Pages/ProjectV2/Components/ProjectDetailTabs/Notes/NotesTab";
import SettingsTab from "Pages/ProjectV2/Components/ProjectDetailTabs/Settings/SettingsTab";
import CodeQualityTab from "Pages/ProjectV2/Components/ProjectDetailTabs/CodeQuality/CodeQualityTab";
import PasswordsTab from "Pages/ProjectV2/Components/ProjectDetailTabs/Passwords/PasswordsTab";
import ActivityTab from "Pages/ProjectV2/Components/ProjectDetailTabs/Activity/ActivityTab";
import Loader from "Commons/Loader";
import ProjectOnboardingChecklist from "../Components/ProjectOnboardingChecklist";
import { isExecutive, isUnitTeamLeader } from "store/slices/loginSlice";

import AllocationsTab from "Pages/ProjectV2/Components/ProjectDetailTabs/Allocations/AllocationsTab";
import {
  getProjectDetails,
  listEmployeesForSelectionAction,
  listSquadForProjectAction,
} from "Pages/ProjectV2/Actions/ProjectActions";
import ProjectTimesheet from "../Components/ProjectDetailTabs/BucketAndTimesheet/ProjectTimesheet";
import { GetFileFromS3 } from "Pages/Profile/function/uploadFile";
import { setCachedProjects } from "store/slices/projectListSlice";

function ProjectDetails() {
  const params = useParams();
  const navigate = useNavigate();

  const [loading, setIsLoading] = useState(true);
  const [LastWeekHoursBurn, setLastWeekHoursBurn] = useState({});
  const [WeeklyBurnLoader, setWeeklyBurnLoader] = useState(false);
  const [project, setProjectDetails] = useState({});

  // State to hold employee details without hidden profiles and only active employees
  const [employeeDetails, setEmployeeDetails] = useState([]);
  //state to hold active squad list
  const [squadList, setSquadList] = useState([]);

  // State to hold employee details with hidden profiles too
  const [includeHiddenEmployeeDetails, setIncludeHiddenEmployeeDetails] =
    useState([]);
  const [pendingKeys, setPendingKeys] = useState([]); // State to hold pending keys for onboarding checklist
  const [logoUrl, setLogoUrl] = useState(null);

  const isUserExecutive = useSelector(isExecutive);
  const isPUL = useSelector(isUnitTeamLeader);
  const dispatch = useDispatch();

  const isUserAllowed = isUserExecutive || isPUL;
  const hasPendingItems = pendingKeys.length > 0;

  const shouldShowOnboarding = isUserAllowed && hasPendingItems;

  useEffect(() => {
    if (params?.id) {
      getProject(params.id);
      //list employee details for project allocations
      listEmployeeDetails();

      //list active squad
      listSquadDetails();
    }
    //eslint-disable-next-line
  }, [params.id]);

  useEffect(() => {
    if (project?.id) {
      handleGetLastWeekTimeSheets([project]);
    }
  }, [project]);

  const getProject = async (id) => {
    try {
      setIsLoading(true);
      const projectDetails = await getProjectDetails(id);
      setProjectDetails(projectDetails || {});
    } catch (error) {
      console.error("Failed to load projects", error);
      message.error("Failed to load projects. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  const timeSheetDays = useMemo(() => 7, []);

  async function handleGetLastWeekTimeSheets(projects) {
    if (!projects) return;
    if (!Array.isArray(projects)) {
      console.error("Expected projects to be an array, got:", projects);
      return;
    }
    setWeeklyBurnLoader(true);
    try {
      const dataObj = await computeWeeklyBurnData(projects, timeSheetDays);
      setLastWeekHoursBurn(dataObj);
    } catch (error) {
      console.error("Error fetching timesheet data:", error);
      message.error("Failed to fetch timesheet data.");
    } finally {
      setWeeklyBurnLoader(false);
    }
  }
  const handleProjectUpdate = (updatedProject) => {
    setProjectDetails(updatedProject);
  };

  const listEmployeeDetails = useCallback(async () => {
    try {
      // Fetch employees with active status
      let filter = {
        active: { ne: false },
      };
      //Graphql query to fetch employees with active status
      const employeeList = await listEmployeesForSelectionAction(filter);

      // Filter out employees with hidden profiles
      const visibleEmployees = employeeList?.filter(
        (emp) => emp?.hidden_profile === false
      );
      // Set the state with employees that are not hidden and are active
      setEmployeeDetails(visibleEmployees);
      // Set the state with all employees, including hidden profiles
      setIncludeHiddenEmployeeDetails(employeeList);
    } catch (error) {
      // Handle error in fetching employeesList
      console.error("Failed to fetch employees:", error);
      message.error("Failed to fetch employee list.");
    }
  }, []);

  const listSquadDetails = useCallback(async () => {
    try {
      // Fetch squad with active status
      let filter = {
        active: { eq: true },
      };
      //Graphql query to fetch squad with active status
      const squadList = await listSquadForProjectAction(filter);
      setSquadList(squadList);
    } catch (error) {
      console.error("Failed to fetch squad members:", error);
      message.error("Failed to fetch squad .");
    }
  }, []);

  return (
    <div className="flex flex-col gap-2 h-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex gap-2 items-center z-10 relative">
            <div
              className="cursor-pointer flex items-center  gap-1"
              onClick={() => {
                navigate(`/v2/project`);
              }}
            >
              <ArrowLeftOutlined />
              Back
            </div>
            <Divider type="vertical" />
            <div className="text-lg font-semibold">Project view</div>
          </div>
        </div>
      </div>

      {loading ? (
        <Loader title={"Loading Project"} />
      ) : (
        <>
          <div className="min-h-2 flex flex-col sm:flex-row gap-4 ">
            <Row gutter={[16, 16]} align="stretch" className="w-full flex-1">
              <Col
                xs={24}
                sm={24}
                md={shouldShowOnboarding ? 16 : 24}
                lg={shouldShowOnboarding ? 18 : 24}
                className="h-full flex flex-col"
              >
                <ProjectCard
                  project={project}
                  LastWeekHoursBurn={LastWeekHoursBurn}
                  WeeklyBurnLoader={WeeklyBurnLoader}
                  mergeTeamCard={true}
                />
              </Col>
              <Col
                xs={24}
                sm={24}
                md={8}
                lg={6}
                className={`h-full flex flex-col ${
                  pendingKeys.length > 0 && isUserAllowed ? "block" : "hidden"
                }`}
              >
                <ProjectOnboardingChecklist
                  project={project}
                  onPendingKeysChange={setPendingKeys}
                />
              </Col>
            </Row>
          </div>
          {/* <Tabs/> */}
          <div className={`${boxClass}`}>
            <Tabs
              defaultActiveKey="notes"
              items={[
                {
                  label: `Notes`,
                  key: "notes",
                  children: (
                    <div>
                      <NotesTab
                        project={project}
                        projectStatus={project.status}
                        oldNotes={project.notes}
                        onProjectUpdate={handleProjectUpdate}
                      />
                    </div>
                  ),
                },
                project.project_buckets?.length > 0 && {
                  label: "Buckets",
                  key: "buckets-time-spent",
                  children: <ProjectTimesheet project={project} />,
                },
                {
                  label: `Allocations`,
                  key: "3",
                  children: (
                    <AllocationsTab
                      project={project}
                      employeeDetails={employeeDetails}
                      includeHiddenEmployeeDetails={
                        includeHiddenEmployeeDetails
                      }
                      squadList={squadList}
                      setProject={setProjectDetails}
                    />
                  ),
                },
                {
                  label: `Code Quality`,
                  key: "4",
                  children: (
                    <div>
                      <CodeQualityTab />
                    </div>
                  ),
                },
                {
                  label: `Passwords`,
                  key: "5",
                  children: (
                    <div>
                      <PasswordsTab />
                    </div>
                  ),
                },
                {
                  label: `Activity`,
                  key: "6",
                  children: (
                    <div>
                      <ActivityTab />
                    </div>
                  ),
                },
                {
                  label: `Settings`,
                  key: "7",
                  children: (
                    <div>
                      <SettingsTab onProjectUpdate={handleProjectUpdate} />
                    </div>
                  ),
                },
              ].filter(Boolean)}
            />
          </div>
        </>
      )}
    </div>
  );
}
export default ProjectDetails;
