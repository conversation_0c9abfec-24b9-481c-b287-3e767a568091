/* eslint-disable react-hooks/exhaustive-deps */
import { AppstoreOutlined, UnorderedListOutlined } from "@ant-design/icons";
import { message, Segmented, Select } from "antd";
import Search from "antd/lib/input/Search";
import PageHeader from "AtomicComponents/PageHeader";
import React, { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import ProjectCard from "Pages/ProjectV2/Components/ProjectCard";
import ProjectTable from "Pages/ProjectV2/Components/ProjectTable";
import Loader from "Commons/Loader";
import PageRefresh from "Commons/PageRefresh";
import {
  STATUS_OPTIONS,
  statusPriority,
} from "Pages/ProjectV2/Constants/Constants";
import { setCachedProjects, setFilters } from "store/slices/projectListSlice";
import { computeWeeklyBurnData } from "Pages/ProjectV2/utils/projectV2Utils";
import { ListProjectsAction } from "Pages/ProjectV2/Actions/ProjectActions";
import AddProjectModal from "../Components/AddProjectModal";

function ProjectList() {
  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [filterOptions, setFilterOptions] = useState({
    projectManagers: [],
    projectDevPrinicipal: [],
    productStrategists: [],
    projectLead: [],
  });

  const [viewMode, setViewMode] = useState("card");

  const [LastWeekHoursBurn, setLastWeekHoursBurn] = useState({});
  const [WeeklyBurnLoader, setWeeklyBurnLoader] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const filters = useSelector((state) => state.projectListReducer.filters);
  const cachedProjects = useSelector(
    (state) => state.projectListReducer.cachedProjects
  );

  const dispatch = useDispatch();

  useEffect(() => {
    if (cachedProjects.length > 0) {
      setProjects(cachedProjects);
      handleGetLastWeekTimeSheets(cachedProjects, timeSheetDays);
    } else {
      getProjects();
    }
  }, []);

  useEffect(() => {
    const relevantProjects = projects.filter((project) =>
      filters.type === "active"
        ? project.status !== "INACTIVE"
        : project.status === "INACTIVE"
    );

    setFilterOptions({
      projectManagers: extractUniqueOptions(
        relevantProjects,
        "product_manager"
      ),
      projectDevPrinicipal: extractUniqueOptions(
        relevantProjects,
        "dev_principal"
      ),
      productStrategists: extractUniqueOptions(
        relevantProjects,
        "product_strategist"
      ),
      projectLead: extractUniqueOptions(relevantProjects, "project_lead"),
    });
  }, [filters.type, projects]);

  useEffect(() => {
    applyFilters();
  }, [filters, projects]);

  const applyFilters = () => {
    const result = projects.filter((project) => {
      const nameMatch = filters.searchTerm
        ? project.name?.toLowerCase().includes(filters.searchTerm.toLowerCase())
        : true;

      const devMatch = filters.projectDevPrinicipal
        ? project.dev_principal?.email === filters.projectDevPrinicipal
        : true;

      const managerMatch = filters.projectManager
        ? project.product_manager?.email === filters.projectManager
        : true;

      const strategistMatch = filters.productStrategist
        ? project.product_strategist?.email === filters.productStrategist
        : true;

      const statusMatch = filters.status
        ? project.status === filters.status
        : true;

      const typeMatch =
        filters.type === "active"
          ? project.status !== "INACTIVE"
          : project.status === "INACTIVE";

      return (
        nameMatch &&
        devMatch &&
        managerMatch &&
        statusMatch &&
        typeMatch &&
        strategistMatch
      );
    });

    const sortedResult = result.sort((a, b) => {
      return statusPriority[a.status] - statusPriority[b.status];
    });
    setFilteredProjects(sortedResult);
  };

  const getProjects = async () => {
    setIsLoading(true);
    try {
      let projectList = await ListProjectsAction();
      setProjects(projectList);
      dispatch(setCachedProjects(projectList));
      setFilterOptions((prev) => ({
        ...prev,
        projectManagers: extractUniqueOptions(projectList, "product_manager"),
        projectDevPrinicipal: extractUniqueOptions(
          projectList,
          "dev_principal"
        ),
        productStrategists: extractUniqueOptions(
          projectList,
          "product_strategist"
        ),
        projectLead: extractUniqueOptions(projectList, "project_lead"),
      }));
      handleGetLastWeekTimeSheets(projectList, timeSheetDays);
    } catch (error) {
      console.error("Failed to load projects", error);
      message.error("Failed to load projects. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  const extractUniqueOptions = (projects, key) => {
    const seen = new Set();
    const options = projects.reduce((acc, project) => {
      const person = project[key];
      if (person && person.email) {
        if (!seen.has(person.email)) {
          seen.add(person.email);
          const name = `${person.first_name} ${person.last_name}`;
          acc.push({ label: name, value: person.email });
        }
      }
      return acc;
    }, []);
    return options.sort((a, b) => a.label.localeCompare(b.label));
  };

  const timeSheetDays = useMemo(() => 7, []);
  async function handleGetLastWeekTimeSheets(projects) {
    setWeeklyBurnLoader(true);
    try {
      const dataObj = await computeWeeklyBurnData(projects, timeSheetDays);
      setLastWeekHoursBurn(dataObj);
    } catch (error) {
      console.error("Error fetching timesheet data:", error);
      message.error("Failed to fetch timesheet data.");
    } finally {
      setWeeklyBurnLoader(false);
    }
  }
  //Handlers for filters,update filters state
  const updateFilterOption = (key) => (value) => {
    dispatch(setFilters({ ...filters, [key]: value }));
  };

  return (
    <div className="p-3">
      <div className="flex justify-between mb-1">
        <PageHeader title={"Projects"} onlyTitle={true} />
        <PageRefresh
          onRefresh={getProjects}
          cacheKey="projectsLastFetchedTime"
        />
      </div>
      <div className="flex flex-col justify-between items-center gap-4 mb-6">
        {/* All controls in a single row with wrapping */}
        <div className="flex  justify-between items-center w-full   gap-4">
          {/* Left side: Search and dropdowns */}
          <div className="flex flex-wrap gap-4 ">
            <Search
              placeholder="Search projects"
              value={filters.searchTerm}
              onChange={(e) => updateFilterOption("searchTerm")(e.target.value)}
              onSearch={(val) => updateFilterOption("searchTerm")(val)}
              allowClear
              enterButton
              className="w-[80%]"
            />
            <Segmented
              options={[
                {
                  label: (
                    <span
                      className={
                        filters.type === "active"
                          ? " text-primary-500 "
                          : "text-gray-500"
                      }
                    >
                      Active
                    </span>
                  ),
                  value: "Active",
                },
                {
                  label: (
                    <span
                      className={
                        filters.type !== "active"
                          ? " text-primary-500"
                          : "text-gray-500"
                      }
                    >
                      Inactive
                    </span>
                  ),
                  value: "Inactive",
                },
              ]}
              value={filters.type === "active" ? "Active" : "Inactive"}
              onChange={(val) => updateFilterOption("type")(val.toLowerCase())}
              className="bg-gray-100  p-1"
            />

            <Select
              showSearch
              placeholder="Status"
              value={filters.status}
              options={STATUS_OPTIONS}
              onChange={updateFilterOption("status")}
              className="min-w-[10rem] w-full sm:w-auto"
              allowClear
            />
            <Select
              showSearch
              placeholder="Product Dev Prinicipal"
              value={filters.projectDevPrinicipal}
              options={filterOptions.projectDevPrinicipal}
              onChange={updateFilterOption("projectDevPrinicipal")}
              className="min-w-[10rem] w-full sm:w-auto"
              allowClear
            />
            <Select
              showSearch
              placeholder="Project Manager"
              value={filters.projectManager}
              options={filterOptions.projectManagers}
              onChange={updateFilterOption("projectManager")}
              className="min-w-[10rem] w-full sm:w-auto"
              allowClear
            />
            <Select
              showSearch
              placeholder="Project Lead"
              value={filters.projectLead}
              options={filterOptions.projectLead}
              onChange={updateFilterOption("projectLead")}
              className="min-w-[10rem] w-full sm:w-auto"
              allowClear
            />
          </div>

          {/* Right side: Toggle and New Project */}
          <div className="flex flex-col gap-3 ">
            {" "}
            <AddProjectModal
              projectManagersOptions={filterOptions.projectManagers}
              productStrategist={filterOptions.productStrategists}
              projectLeadsOptions={filterOptions.productLeads}
              onSuccess={getProjects}
            />
            <div className="flex justify-end">
              <Segmented
                value={viewMode}
                onChange={setViewMode}
                size="large"
                options={[
                  { value: "card", icon: <UnorderedListOutlined /> },
                  { value: "grid", icon: <AppstoreOutlined /> },
                ]}
                className="w-fit h-10 rounded-md border border-gray-300 shadow-sm"
              />
            </div>
          </div>
        </div>
      </div>
      {isLoading ? (
        <Loader title="Loading projects.." />
      ) : (
        <>
          <div className="mb-2 text-sm text-gray-600 font-medium">
            Showing {filteredProjects.length} project
            {filteredProjects.length !== 1 ? "s" : ""}
          </div>
          {viewMode === "card" ? (
            <div>
              {filteredProjects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  LastWeekHoursBurn={LastWeekHoursBurn}
                  WeeklyBurnLoader={WeeklyBurnLoader}
                />
              ))}
            </div>
          ) : (
            <ProjectTable
              projects={filteredProjects}
              LastWeekHoursBurn={LastWeekHoursBurn}
              WeeklyBurnLoader={WeeklyBurnLoader}
            />
          )}
        </>
      )}
    </div>
  );
}

export default ProjectList;
