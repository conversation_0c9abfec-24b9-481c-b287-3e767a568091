/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect } from "react";
import { Progress } from "antd";
import { CheckCircleFilled } from "@ant-design/icons";
import { checklistItems } from "Pages/ProjectV2/Constants/Constants";
import { boxClass } from "utils/TailwindCommonClasses";

/**
 * Sidebar checklist showing pending project onboarding tasks.
 *
 * @param {Object} props
 * @param {Object} props.project - Project data with tags, links, allocations, etc.
 * @returns {JSX.Element|null} Checklist or null if all tasks are complete.
 */
const ProjectOnboardingChecklist = ({ project, onPendingKeysChange }) => {
  const pendingMap = {
    tags: !(project.ProjectTag?.items?.length > 0),
    slack: !project.internal_slack_channel_id,
    devTeam: !project.dev_principal,
    allocation: !(project.employee_project_allocation?.items?.length > 0),
    dashboard: !project.notion_dashboard_link,
    backlog: !project.notion_backlog_link,
    clientPOC: !project.client_POC,
    meetings: !(project.meetings?.items?.length > 0),
  };

  const pendingKeys = Object.entries(pendingMap)
    .filter(([, isPending]) => isPending)
    .map(([key]) => key);

  useEffect(() => {
    onPendingKeysChange?.(pendingKeys);
  }, [pendingKeys.join(",")]);

  const completedCount = checklistItems.length - pendingKeys.length;

  // Calculate progress percentage
  const progressPercent = Math.round(
    (completedCount / checklistItems.length) * 100
  );

  // If all items are complete, don’t show the checklist
  if (pendingKeys.length === 0) return null;

  return (
    <div
      className={` mt-3 p-2 h-10 flex flex-col flex-grow overflow-hidden ${boxClass} `}
    >
      <div className="flex justify-between items-center mb-1">
        <div className="font-semibold text-base">Project Onboarding</div>
        <div className="text-xs text-gray-500">{`${completedCount} of ${checklistItems.length} complete`}</div>
      </div>

      <Progress
        percent={progressPercent}
        showInfo={false}
        strokeColor="#1677ff"
        className="mb-2"
      />

      {/* Render checklist items */}
      <div className="flex-1 flex flex-col gap-2 overflow-auto ">
        {checklistItems.map((item) => {
          const isPending = pendingMap[item.key];
          return (
            <div
              key={item.key}
              className={`flex items-center justify-between border rounded px-1 py-1 text-xs transition ${
                isPending
                  ? "bg-gray-50 text-gray-600 hover:bg-gray-100"
                  : "bg-green-50 text-green-600 border-green-200"
              }`}
            >
              <div className="flex items-center gap-2">
                {isPending ? (
                  <div className="w-4 h-4 border border-gray-400 rounded-full" />
                ) : (
                  <CheckCircleFilled className="text-green-500 text-sm" />
                )}
                <span>{item.label}</span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ProjectOnboardingChecklist;
