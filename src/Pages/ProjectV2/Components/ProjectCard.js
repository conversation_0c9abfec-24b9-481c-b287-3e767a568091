import { Loading3QuartersOutlined } from "@ant-design/icons";
import { Avatar } from "antd";
import React, { useEffect, useMemo, useState } from "react";
import moment from "moment";
import { boxClass } from "utils/TailwindCommonClasses";
import { colorPallate, DateFormat } from "utils/constants";
import CustomImage from "Commons/CustomImage";
import { useNavigate } from "react-router-dom";
import Jira from "assets/images/apps/Jira-app.png";
import Confluence from "assets/images/apps/Confluence-app.png";
import ProjectTags from "Pages/ProjectV2/Components/Commons/ProjectTags";
import { getMostRecentProjectNote } from "Pages/ProjectV2/utils/projectV2Utils";
import TeamMemberCard from "Pages/ProjectV2/Components/Commons/TeamMemberCard";
import { GetFileFromS3 } from "Pages/Profile/function/uploadFile";

const ProjectCard = ({
  project,
  LastWeekHoursBurn,
  WeeklyBurnLoader,
  mergeTeamCard = false,
}) => {
  const navigate = useNavigate();
  const [recentNote, setRecentNote] = useState(null);
  const [logoUrl, setLogoUrl] = useState(null);

  useEffect(() => {
    const note = getMostRecentProjectNote(project?.project_notes, DateFormat);
    setRecentNote(note);
  }, [project?.project_notes]);

  useEffect(() => {
    const loadLogo = async () => {
      if (project?.logo) {
        try {
          const url = await GetFileFromS3(project.logo);
          setLogoUrl(url);
        } catch (err) {
          console.error("Failed to load logo:", err);
          setLogoUrl(null);
        }
      }
    };
    loadLogo();
  }, [project?.logo]);
  /**
   * Returns initials for the given project name
   * @param {string} projectName - Name of the project
   * @returns {string} Initials for avatar
   */
  const getProjectInitials = (projectName) => {
    // Remove special characters and digits from words
    const words = projectName
      ?.trim()
      ?.split(" ")
      ?.filter(Boolean)
      ?.map((word) => word.replace(/[^a-zA-Z]/g, "")) // remove non-letter characters
      ?.filter(Boolean); // remove any empty strings after cleanup

    if (words?.length >= 3) {
      return words
        .slice(0, 3)
        .map((w) => w[0])
        .join("")
        .toUpperCase();
    }

    if (words?.length === 2) {
      return words
        .map((w) => w[0])
        .join("")
        .toUpperCase();
    }

    if (words?.length === 1) {
      const word = words[0];

      // Match uppercase letters only
      const camelCaseMatch = word.match(/[A-Z]/g);
      if (camelCaseMatch && camelCaseMatch.length >= 2) {
        return camelCaseMatch.slice(0, 2).join("").toUpperCase();
      }
      return word.slice(0, 2).toUpperCase();
    }
  };

  const jiraLink = useMemo(() => {
    return project?.extra_links?.find((link) =>
      link?.url?.toLowerCase().includes("jira")
    )?.url;
  }, [project]);

  const confluenceLink = useMemo(() => {
    return project?.extra_links?.find((link) =>
      link?.url?.toLowerCase().includes("confluence")
    )?.url;
  }, [project]);

  return (
    <div className={`mt-3 p-3 ${boxClass}`}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center">
          <Avatar
            shape="circle"
            size={60}
            className="mr-3 bg-white text-black border border-gray-300 text-[20px] font-semibold"
            src={logoUrl || undefined}
          >
            {!logoUrl && getProjectInitials(project?.name)}
          </Avatar>

          <div>
            <h3
              className="text-lg font-semibold text-gray-800 mb-1 cursor-pointer flex items-center"
              onClick={() => {
                navigate(`/v2/project-view/${project.id}`);
              }}
            >
              <span
                className="w-3 h-3 rounded-full mr-2"
                style={{
                  backgroundColor: colorPallate[project?.status] || "#000",
                }}
              />
              {project?.name} - {project?.client?.name || "-"}
            </h3>

            <div className="flex items-center space-x-1">
              <ProjectTags tags={project?.ProjectTag?.items || []} />
            </div>
          </div>
        </div>

        <div className="text-sm text-gray-500 flex flex-col ">
          <div className="flex items-center space-x-2">
            <span className="font-semibold text-gray-500">Start Date:</span>
            <span>
              {project?.start_time
                ? moment(project?.start_time).format(DateFormat)
                : "-"}
            </span>

            {project?.end_time && (
              <>
                <div className="h-4 border-l border-gray-300 mx-2" />
                <span className="font-semibold text-gray-500">End Date:</span>
                <span>{moment(project?.end_time).format(DateFormat)}</span>
              </>
            )}
          </div>

          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center space-x-1">
              <span className="font-semibold text-gray-500">
                Project Links:
              </span>
              <a
                href={jiraLink || undefined}
                target="_blank"
                rel="noopener noreferrer"
                title="Jira"
                className={!jiraLink ? "pointer-events-none" : ""}
              >
                <CustomImage
                  className={`w-9 h-8 p-1 ${jiraLink ? "" : "opacity-50"}`}
                  src={Jira}
                  alt="Jira"
                />
              </a>

              <a
                href={confluenceLink || undefined}
                target="_blank"
                rel="noopener noreferrer"
                title="Confluence"
                className={!confluenceLink ? "pointer-events-none" : ""}
              >
                <CustomImage
                  className={`w-9 h-7 p-1 ${
                    confluenceLink ? "" : "opacity-50"
                  }`}
                  src={Confluence}
                  alt="Confluence"
                />
              </a>
            </div>

            <div
              className={`ml-4 px-2 py-1 text-sm rounded-md whitespace-nowrap font-semibold border ${
                WeeklyBurnLoader
                  ? "border-gray-300 text-gray-400"
                  : LastWeekHoursBurn?.[project?.id]?.burn <=
                    LastWeekHoursBurn?.[project?.id]?.allocation
                  ? "border-green-300 text-green-600"
                  : "border-red-300 text-red-600"
              }`}
            >
              {WeeklyBurnLoader ? (
                <Loading3QuartersOutlined spin />
              ) : (
                <>
                  {LastWeekHoursBurn?.[project?.id]?.burn ?? "-"}/
                  {LastWeekHoursBurn?.[project?.id]?.allocation ?? "-"}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <div
        className={`grid ${
          mergeTeamCard
            ? "grid-cols-6 gap-1 text-xs"
            : "grid-cols-6 gap-4 text-sm"
        } text-gray-600`}
      >
        <TeamMemberCard
          label="Product Manager"
          employee={project?.product_manager}
          mergeTeamCard={mergeTeamCard}
        />
        <TeamMemberCard
          label="Strategist"
          employee={project?.product_strategist}
          mergeTeamCard={mergeTeamCard}
        />

        <TeamMemberCard
          label="Dev"
          employee={project?.dev_principal}
          mergeTeamCard={mergeTeamCard}
        />
        <TeamMemberCard
          label="Lead"
          employee={project?.project_lead}
          mergeTeamCard={mergeTeamCard}
        />
        <div className="flex flex-col bg-gray-100 rounded-md p-2">
          <span className="font-semibold text-xs text-gray-500">
            Client Name
          </span>
          <div className="text-gray-900 mt-1">
            {project?.client?.name || "-"}
          </div>
        </div>
        <div className="flex flex-col bg-gray-100 rounded-md p-2">
          <span className="font-semibold text-xs text-gray-500">
            Project Type
          </span>
          <div className="text-gray-900">{project?.project_type || "-"}</div>
        </div>
        <div className="col-span-6 flex flex-col border border-gray-200 rounded-md p-2">
          <span className="font-semibold text-xs text-gray-500">Notes:</span>
          <div className="text-gray-900 ">
            {recentNote?.note?.trim() || "-"}
          </div>
        </div>
      </div>

      <div className="text-right mt-2 text-xs text-gray-500">
        Last Updated:{" "}
        {project?.updatedAt
          ? moment(project?.updatedAt).format(DateFormat)
          : "-"}
      </div>
    </div>
  );
};

export default ProjectCard;
