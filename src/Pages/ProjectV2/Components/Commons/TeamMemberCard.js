import React from "react";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";

function TeamMemberCard({ label, employee, mergeTeamCard = false }) {
  return (
    <div className="flex flex-col bg-gray-100 rounded-md p-2">
      <span className="font-semibold text-xs text-gray-500">{label}</span>
      <div className="text-gray-900">
        {employee ? (
          <RenderEmployeeFullName
            employee={employee}
            showPopover={true}
            className="-pl-1 pt-1"
            showAvatar
            avatarSize={mergeTeamCard ? 22 : 30}
          />
        ) : (
          <span className="text-gray-900">-</span>
        )}
      </div>
    </div>
  );
}

export default React.memo(TeamMemberCard);
