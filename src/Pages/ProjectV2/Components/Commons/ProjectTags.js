import React from "react";
import { Tag } from "antd";
import { tagColors } from "Pages/ProjectV2/Constants/Constants";

const ProjectTags = ({ tags = [] }) => {
  if (!tags.length) return <span>-</span>;

  return (
    <div className="flex flex-wrap gap-1 max-w-xs">
      {tags.map((tag, index) => (
        <Tag
          key={tag.id}
          color={tagColors[index % tagColors.length]}
          className="text-xs font-semibold"
        >
          {tag.projectTag?.title}
        </Tag>
      ))}
    </div>
  );
};

export default ProjectTags;
