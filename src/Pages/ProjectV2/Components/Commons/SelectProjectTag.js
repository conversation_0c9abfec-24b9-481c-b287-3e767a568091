import { PlusOutlined } from "@ant-design/icons";
import { Divider, Select, Space, Input, Button } from "antd";
import { useEffect, useRef, useState } from "react";
import {
  CreateProjectTag,
  DeleteTagProjectRelation,
  CreateTagProjectRelation,
  ListProjectTags,
} from "utils/Actions";
import { useParams } from "react-router-dom";

export default function SelectProjectTag({
  value = [],
  existingTags = [],
  onChange,
  onTagMapChange,
  disabled,
}) {
  const [tagItems, setTagItems] = useState([]);
  const [tagName, setTagName] = useState("");
  const [tagExists, setTagExists] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [updatingTags, setUpdatingTags] = useState(false);

  const inputTagRef = useRef(null);
  const params = useParams();
  const projectID = params?.id;

  const existingTagIds = existingTags.map((item) => item.projectTagID);

  // Fetch available tags on mount
  useEffect(() => {
    getProjectTags();
  }, []);

  // useEffect to keep tagExists in sync based on tagName and tagItems
  useEffect(() => {
    const trimmedName = tagName ? tagName.trim().toLowerCase() : "";
    const exists = tagItems.some(
      (tag) =>
        typeof tag.label === "string" &&
        tag.label.toLowerCase() === trimmedName &&
        trimmedName !== ""
    );
    setTagExists(exists);
  }, [tagName, tagItems]);

  const getProjectTags = async () => {
    setUpdateLoading(true);
    const tagList = await ListProjectTags();

    const seenTitles = new Set();
    const seenIds = new Set();
    const uniqueTags = [];

    tagList?.forEach((tag) => {
      const id = tag?.id?.trim();
      const title = tag?.title?.trim();

      if (
        id &&
        title &&
        !seenIds.has(id) &&
        !seenTitles.has(title.toLowerCase())
      ) {
        seenIds.add(id);
        seenTitles.add(title.toLowerCase());
        uniqueTags.push({
          value: id,
          label: title,
        });
      }
    });

    setTagItems(uniqueTags);

    // tagMap for passing to parent
    const tagMap = Object.fromEntries(
      uniqueTags.map((tag) => [tag.value, tag.label])
    );
    onTagMapChange?.(tagMap);
    setUpdateLoading(false);
  };

  const addTag = async (data) => {
    try {
      await CreateProjectTag(data);
      getProjectTags();
      setTagName("");
      setTagExists(false);
      onChange?.(value);
      setTimeout(() => {
        inputTagRef.current?.focus();
      }, 0);
    } catch (error) {
      console.error(error);
    }
  };

  // Handle tag selection
  const handleTagChange = async (newTagIds) => {
    setUpdatingTags(true);

    // compute deletions based on previous value instead of props
    const tagsToDelete = value.filter((id) => !newTagIds.includes(id));
    const tagsToCreate = newTagIds.filter((id) => !value.includes(id));

    try {
      await Promise.all([
        ...tagsToDelete.map((tagID) => {
          const rel = existingTags.find((t) => t.projectTagID === tagID);
          return rel
            ? DeleteTagProjectRelation({ id: rel.id })
            : Promise.resolve();
        }),
        ...tagsToCreate.map((tagID) =>
          CreateTagProjectRelation({ projectTagID: tagID, projectID })
        ),
      ]);

      onChange?.(newTagIds); // update parent value
    } catch (err) {
      console.error("Error updating tags:", err);
    } finally {
      setUpdatingTags(false);
    }
  };

  return (
    <Select
      className="w-full"
      mode="multiple"
      placeholder="Select Project Tags"
      value={value}
      showSearch
      onChange={handleTagChange}
      filterOption={(input, option) =>
        (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
      }
      disabled={disabled}
      dropdownRender={(menu) => (
        <>
          {menu}
          <Divider style={{ margin: "8px 0" }} />
          <Space style={{ padding: "0 8px 4px" }}>
            <Input
              placeholder="Enter tag name"
              value={tagName}
              ref={inputTagRef}
              onChange={(e) => setTagName(e.target.value)}
            />
            <Button
              icon={<PlusOutlined />}
              disabled={!tagName.trim() || tagExists}
              onClick={addTag}
            >
              Add Tag
            </Button>
          </Space>
        </>
      )}
      options={tagItems?.map((item) => ({
        label: item?.label,
        value: item?.value,
      }))}
    />
  );
}
