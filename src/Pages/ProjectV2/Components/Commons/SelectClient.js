import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Divider, Input, message, Select, Space } from "antd";
import { useEffect, useRef, useState } from "react";
import { CreateClient, ListClients } from "utils/Actions";

function SelectClient({ value, onChange, onClientListFetch, disabled }) {
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(false);
  const [name, setName] = useState("");
  const inputRef = useRef(null);

  useEffect(() => {
    getClients();
  }, []);

  const getClients = async () => {
    setLoading(true);
    const clientList = await ListClients();
    const formatted = clientList?.items?.map((client) => ({
      value: client.id,
      label: client.name,
    }));
    setClients(formatted);
    if (onClientListFetch) onClientListFetch(formatted);
    setLoading(false);
  };

  const addClient = async () => {
    if (!name.trim()) return;
    try {
      await <PERSON><PERSON>Client({ name });
      message.success("Client added successfully");
      setName("");
      getClients();
      setTimeout(() => inputRef.current?.focus(), 0);
    } catch (error) {
      message.error("Failed to add client");
      console.error(error);
    }
  };

  return (
    <Select
      placeholder="Select client"
      value={value}
      onChange={onChange}
      showSearch
      loading={loading}
      filterOption={(input, option) =>
        (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
      }
      disabled={disabled}
      dropdownRender={(menu) => (
        <>
          {menu}
          <Divider className="my-2 border-gray-200" />
          <Space className="px-2 pb-1">
            <div className="flex gap-2 ">
              <Input
                placeholder="Enter client name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                ref={inputRef}
                onPressEnter={addClient}
              />
              <Button
                icon={<PlusOutlined />}
                onClick={() => addClient({ name: name.trim() })}
                disabled={!name.trim()}
              >
                Add Client
              </Button>
            </div>
          </Space>
        </>
      )}
      options={clients?.map((item) => ({
        label: item?.label,
        value: item?.value,
      }))}
    />
  );
}

export default SelectClient;
