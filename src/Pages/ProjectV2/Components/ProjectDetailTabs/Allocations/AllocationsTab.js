import {
  Col,
  Form,
  Row,
  Select,
  Typography,
  Input,
  InputNumber,
  Button,
  Divider,
  message,
} from "antd";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import React, { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import {
  isExecutive,
  isHr,
  isOnlySL,
  isUnitTeamLeader,
} from "store/slices/loginSlice";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import {
  executeBulkEmployeeProjectAllocation,
  UpdateProjecForAllocationtAction,
} from "Pages/ProjectV2/Actions/ProjectActions";
import { generateUUID } from "utils/commonMethods";
import RenderEmployeeSelect from "AtomicComponents/RenderEmployeeSelect";

const AllocationsTab = ({
  project,
  employeeDetails,
  includeHiddenEmployeeDetails,
  squadList,
  setProject,
}) => {
  // Form instance
  const [form] = Form.useForm();
  const [devTeamForm] = Form.useForm();

  //selector
  const squadLeader = useSelector(isOnlySL);
  const isAnExecutive = useSelector(isExecutive);
  const isPUL = useSelector(isUnitTeamLeader);
  const isHR = useSelector(isHr);

  const isPrivilegedUser = isAnExecutive || isPUL || isHR;

  //state

  const [loading, setLoading] = useState(false);

  // Initialize form with project dataForTable
  useEffect(() => {
    if (project) {
      console.log("project", project);
      // Set project details in the project form
      form.setFieldsValue({
        product_manager: project?.product_manager?.email,
        product_strategist: project?.product_strategist?.email,
        squad: project?.squad?.name,
        dev_principal: project?.dev_principal?.email,
        project_lead: project?.project_lead?.email,
      });

      // Set dev team members and their allocations
      devTeamForm.setFieldsValue({
        team:
          project?.employee_project_allocation?.items?.map((member) => ({
            id: member?.id,
            title: member?.title,
            employee: member?.employee?.email,
            allocation: member?.allocation,
          })) || [],
      });
    }
  }, [project, form, devTeamForm]);

  // Function to get employee options for Select components
  const getEmployeeOptions = React.useCallback(
    (employees) =>
      employees?.map((emp) => ({
        label: (
          <RenderEmployeeFullName
            employee={emp}
            noRedirect={true}
            className="m-0"
          />
        ),
        value: emp.email,
        searchText: `${emp.first_name || ""} ${
          emp.last_name || ""
        }`.toLowerCase(),
      })),
    []
  );

  // Function to get squad options for Select components
  const getSquadOptions = React.useCallback(
    (squads) =>
      squads?.map((squad) => ({
        value: squad?.name,
        label: (
          <span className="flex">
            {squad?.name} (
            <RenderEmployeeFullName
              className="m-0"
              employee={squad?.squad_manager}
              noRedirect={true}
            />
            )
          </span>
        ),
        searchText: `${squad?.name || ""} ${
          squad?.squad_manager?.first_name || ""
        } ${squad?.squad_manager?.last_name || ""}`.toLowerCase(),
      })),
    []
  );

  // Memoized options for Select components
  const employeeOptions = useMemo(
    () => getEmployeeOptions(employeeDetails),
    [getEmployeeOptions, employeeDetails]
  );
  const hiddenEmployeeOptions = useMemo(
    () => getEmployeeOptions(includeHiddenEmployeeDetails),
    [getEmployeeOptions, includeHiddenEmployeeDetails]
  );
  const squadOptions = useMemo(
    () => getSquadOptions(squadList),
    [getSquadOptions, squadList]
  );

  const updateProjectAllocations = async () => {
    try {
      setLoading(true);
      const projectFields = await form.validateFields();
      const { team } = await devTeamForm.validateFields();

      // Prepare new allocation list from form
      const newAllocations = team.map((item) => ({
        id: item.id,
        email: item.employee,
        allocation: item.allocation,
        title: item.title,
      }));

      // Prepare old allocations map by ID and key (email||title)
      const oldAllocations = project?.employee_project_allocation?.items || [];
      const oldMapById = new Map(oldAllocations.map((a) => [a.id, a]));
      const oldMapByKey = new Map(
        oldAllocations.map((a) => [`${a.employee?.email}||${a.title}`, a])
      );
      const newKeys = new Set(
        newAllocations.map((a) => `${a.email}||${a.title}`)
      );
      const newMapById = new Map(
        newAllocations.filter((a) => a.id).map((a) => [a.id, a])
      );

      // Collect deletes, creates, updates
      const deletes = [];
      const creates = [];
      const updates = [];
      const createUuidMap = new Map();

      // Identify deletions: Allocations in oldAllocations but not in newAllocations
      for (const old of oldAllocations) {
        const key = `${old.employee?.email}||${old.title}`;
        if (!newKeys.has(key) && !newMapById.has(old.id)) {
          deletes.push(old.id);
        }
      }

      // Identify creates and updates
      for (const alloc of newAllocations) {
        const key = `${alloc.email}||${alloc.title}`;
        const old = oldMapById.get(alloc.id) || oldMapByKey.get(key);

        if (old && alloc.id) {
          // Update if allocation exists and has changed
          if (
            old.allocation !== alloc.allocation ||
            old.title !== alloc.title ||
            old.employee?.email !== alloc.email
          ) {
            updates.push({
              id: old.id,
              projectEmployee_project_allocationId: project.id,
              allocation: alloc.allocation,

              employeeEmployee_project_allocationId: alloc.email,
              title: alloc.title,
            });
          }
        } else {
          // Create if no matching allocation or no ID
          const tempUuid = generateUUID();
          creates.push({
            projectEmployee_project_allocationId: project.id,
            allocation: alloc.allocation,
            employeeEmployee_project_allocationId: alloc.email,
            title: alloc.title,
          });
          createUuidMap.set(tempUuid, alloc);
          alloc.tempUuid = tempUuid;
        }
      }

      // Execute bulk mutations if needed
      let mutationResults = { data: {} };
      if (deletes.length || creates.length || updates.length) {
        mutationResults = await executeBulkEmployeeProjectAllocation({
          deletes,
          creates,
          updates,
        });
      }

      // Construct updated allocations
      const updatedAllocations = [];
      let createResultCounter = 0;
      for (const alloc of newAllocations) {
        const key = `${alloc.email}||${alloc.title}`;
        const old = oldMapById.get(alloc.id) || oldMapByKey.get(key);

        // Check if this allocation was updated
        const updateKey = Object.keys(mutationResults).find(
          (k) => k.startsWith("update") && mutationResults[k]?.id === alloc.id
        );

        // Check if this allocation was created
        const createKey = Object.keys(mutationResults).find(
          (k) => k === `create${createResultCounter}`
        );

        if (old && alloc.id && !updateKey) {
          // Unchanged allocation: Preserve existing data
          updatedAllocations.push({
            id: alloc.id,
            allocation: alloc.allocation,
            title: alloc.title,
            employee: employeeDetails.find(
              (emp) => emp.email === alloc.email
            ) || {
              email: alloc.email,
            },
          });
        } else if (updateKey) {
          // Updated allocation
          updatedAllocations.push({
            id: alloc.id,
            allocation: alloc.allocation,
            title: alloc.title,
            employee: employeeDetails.find(
              (emp) => emp.email === alloc.email
            ) || {
              email: alloc.email,
            },
          });
        } else if (createKey && alloc.tempUuid) {
          // Newly created allocation
          updatedAllocations.push({
            id: mutationResults[createKey]?.id,
            allocation: alloc.allocation,
            title: alloc.title,
            employee: employeeDetails.find(
              (emp) => emp.email === alloc.email
            ) || {
              email: alloc.email,
            },
          });
          createResultCounter++;
        }
      }

      // Update project fields
      await UpdateProjecForAllocationtAction({
        id: project.id,
        projectProduct_managerId: projectFields.product_manager,
        projectProduct_strategistId: projectFields.product_strategist,
        squadProjectId: projectFields.squad,
        projectDev_principalId: projectFields.dev_principal,
        projectProject_leadId: projectFields.project_lead,
      });

      // Construct updated project state
      const updatedProject = {
        ...project,
        product_manager: {
          email: projectFields.product_manager,
          first_name: employeeDetails.find(
            (emp) => emp.email === projectFields.product_manager
          )?.first_name,
          last_name: employeeDetails.find(
            (emp) => emp.email === projectFields.product_manager
          )?.last_name,
        },
        product_strategist: {
          email: projectFields.product_strategist,
          first_name: employeeDetails.find(
            (emp) => emp.email === projectFields.product_strategist
          )?.first_name,
          last_name: employeeDetails.find(
            (emp) => emp.email === projectFields.product_strategist
          )?.last_name,
        },
        squad: {
          name: projectFields.squad,
          squad_manager: squadList.find(
            (squad) => squad.name === projectFields.squad
          )?.squad_manager,
        },
        dev_principal: {
          email: projectFields.dev_principal,
          first_name: employeeDetails.find(
            (emp) => emp.email === projectFields.dev_principal
          )?.first_name,
          last_name: employeeDetails.find(
            (emp) => emp.email === projectFields.dev_principal
          )?.last_name,
        },
        project_lead: {
          email: projectFields.project_lead,
          first_name: employeeDetails.find(
            (emp) => emp.email === projectFields.project_lead
          )?.first_name,
          last_name: employeeDetails.find(
            (emp) => emp.email === projectFields.project_lead
          )?.last_name,
        },
        employee_project_allocation: {
          items: updatedAllocations.filter((alloc) => alloc.id),
        },
      };

      // Update state
      setProject(updatedProject);

      // Update form to reflect the latest state
      devTeamForm.setFieldsValue({
        team: updatedAllocations.map((alloc) => ({
          id: alloc.id,
          title: alloc.title,
          employee: alloc.employee.email,
          allocation: alloc.allocation,
        })),
      });

      message.success("Project updated successfully!");
    } catch (error) {
      console.error("Error updating project allocations:", error);
      message.error("Failed to update project.");
    } finally {
      setLoading(false);
    }
  };
  return (
    <>
      <div className="flex items-center justify-between mb-2">
        <Typography.Title level={5}>Dev Team Members</Typography.Title>
        <Button
          type="primary"
          onClick={updateProjectAllocations}
          loading={loading}
        >
          Save Allocations
        </Button>
      </div>

      <Form layout="vertical" form={form}>
        {/* Dev Team Members */}
        <Row gutter={[24, 16]}>
          {/* Product Manager  */}
          <Col span={8}>
            <Form.Item
              label="Product Manager"
              name="product_manager"
              rules={[
                {
                  required: true,
                  message: "Please enter product manager name",
                },
              ]}
            >
              <RenderEmployeeSelect
                employeesList={employeeDetails}
                placeholder="Select Product Manager"
                showSearch
                disabled={!isPrivilegedUser}
              />
            </Form.Item>
          </Col>

          {/* Product Strategist */}
          <Col span={8}>
            <Form.Item
              label="Product Strategist"
              name="product_strategist"
              rules={[
                {
                  required: true,
                  message: "Please enter product strategist name",
                },
              ]}
            >
              <RenderEmployeeSelect
                employeesList={includeHiddenEmployeeDetails}
                placeholder="Select Product Strategist"
                showSearch
                disabled={!isPrivilegedUser}
              />
            </Form.Item>
          </Col>
          {/* Product Strategist */}
          <Col span={8}>
            <Form.Item
              label="Product Lead"
              name="project_lead"
              rules={[
                {
                  required: true,
                  message: "Please enter product lead name",
                },
              ]}
            >
              <RenderEmployeeSelect
                employeesList={includeHiddenEmployeeDetails}
                placeholder="Select Product Lead"
                showSearch
                disabled={!isPrivilegedUser}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={[24, 16]}>
          {/* Dev Principal */}
          <Col span={8}>
            <Form.Item
              label="Dev Principal"
              name="dev_principal"
              rules={[
                {
                  required: true,
                  message: "Please enter dev principal name",
                },
              ]}
            >
              <RenderEmployeeSelect
                employeesList={employeeDetails}
                placeholder="Select Dev Principal"
                showSearch
                disabled={!isPrivilegedUser}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Squad"
              name="squad"
              rules={[
                {
                  required: true,
                  message: "Please enter squad name",
                },
              ]}
            >
              <Select
                showSearch
                placeholder="Select Squad"
                filterOption={(input, option) =>
                  option?.searchText?.includes(input.toLowerCase())
                }
                disabled={!isPrivilegedUser}
                options={squadOptions}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Divider className="m-0 p-0" />

      {/* Dev Team and Allocations */}
      <Row align="middle" justify="space-between" className="my-4">
        <Col>
          <Typography.Title level={5} className="m-0">
            Dev Team and Allocations
          </Typography.Title>
        </Col>
        <Col>
          <Button
            type="default"
            onClick={() => {
              // Add a new team member to the Form.List
              const team = devTeamForm.getFieldValue("team") || [];
              devTeamForm.setFieldsValue({ team: [...team, {}] });
            }}
            icon={<PlusOutlined />}
          >
            Add Team Member
          </Button>
        </Col>
      </Row>

      {/* Dev Team & Allocation Form List */}
      <Form form={devTeamForm} layout="vertical">
        <Form.Item>
          <Form.List name="team">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row gutter={12} key={key} align="middle" className="mb-4">
                    {/* Title */}
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        name={[name, "title"]}
                        rules={[{ required: true, message: "Missing title" }]}
                        className="mb-0"
                      >
                        <Input
                          placeholder="Enter Title"
                          disabled={squadLeader}
                        />
                      </Form.Item>
                    </Col>
                    {/* Employee  Name*/}
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        name={[name, "employee"]}
                        rules={[
                          { required: true, message: "Missing employee" },
                        ]}
                        className="mb-0"
                      >
                        <RenderEmployeeSelect
                          employeesList={employeeDetails}
                          placeholder="Select Employee"
                          showSearch
                          disabled={squadLeader}
                          className="w-full"
                        />
                      </Form.Item>
                    </Col>
                    {/* Hour Allocation */}
                    <Col span={6}>
                      <Form.Item
                        {...restField}
                        name={[name, "allocation"]}
                        rules={[{ required: true, message: "Missing hours" }]}
                        className="mb-0"
                      >
                        <InputNumber
                          addonAfter="hours/week"
                          placeholder="40"
                          disabled={squadLeader}
                          className="w-full"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={2}>
                      {(isAnExecutive || isPUL) && (
                        <MinusCircleOutlined
                          onClick={() => remove(name)}
                          className="text-red-500 text-xl cursor-pointer"
                        />
                      )}
                    </Col>
                  </Row>
                ))}
              </>
            )}
          </Form.List>
        </Form.Item>
      </Form>
    </>
  );
};

export default React.memo(AllocationsTab);
