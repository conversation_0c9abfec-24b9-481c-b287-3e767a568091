/* eslint-disable react-hooks/exhaustive-deps */
import { SettingTwoTone } from "@ant-design/icons";
import { Column, Liquid } from "@ant-design/plots";
import {
  Button,
  Divider,
  Input,
  InputNumber,
  Modal,
  Popover,
  Select,
  Switch,
  Table,
  Typography,
  Form,
} from "antd";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import CustomTable from "Commons/CustomTable";
import Loader from "Commons/Loader";
import moment from "moment";
import {
  getTimsheetsPerProject,
  UpdateProjectCustomAction,
} from "Pages/ProjectV2/Actions/ProjectActions";
import React, { useEffect, useMemo, useState } from "react";
import { primaryColor } from "theme";
import {
  findGapInHours,
  findGapInMinutes,
  minutesToHoursAndMinutes,
} from "utils/commonMethods";
import { DateFormat } from "utils/constants";
import { FormatCurrency } from "utils/SalaryHelper";
import { boxClass } from "utils/TailwindCommonClasses";

function ProjectTimesheet({ project }) {
  const [loading, setLoading] = React.useState(false);
  const [rawTimesheetData, setrawTimesheetData] = useState([]);
  const [tableSummary, settableSummary] = useState({});
  const [bucketsForFilter, setbucketsForFilter] = useState([]);
  const [selectedBucket, setselectedBucket] = useState("All");

  useEffect(() => {
    if (project?.id) {
      fetchTimesheet(project?.id);
    }
    if (project?.project_buckets?.length) {
      setbucketsData(project?.project_buckets);
    }
  }, [project]);

  const [bucketsTimesheetData, setBucketsTimesheetData] = React.useState([]);

  const transformTimesheetData = () => {
    let data = rawTimesheetData;
    if (!Array.isArray(data)) return [];
    const grouped = {};

    let totalMinutes = 0;
    let bucketsSet = new Set();
    data.forEach((entry) => {
      if (selectedBucket !== "All" && entry.projectBucket !== selectedBucket) {
        return;
      }
      const date = moment(entry.start_time, "X").format(DateFormat);
      if (!grouped[date]) {
        grouped[date] = {
          date: date,
          minutes: 0,
          bucket: new Set(),
          employees: [],
        };
      }
      let minutes = findGapInMinutes(entry.start_time, entry.end_time);

      grouped[date].minutes += minutes;
      grouped[date].bucket.add(entry.projectBucket);
      grouped[date].employees.push({
        employee: entry.employee,
        hours: findGapInHours(entry.start_time, entry.end_time),

        description: entry.description,
      });

      totalMinutes += minutes;
      bucketsSet.add(entry.projectBucket);
    });
    let formatedData = Object.values(grouped);
    formatedData.sort(
      (a, b) =>
        moment(b.date, DateFormat).valueOf() -
        moment(a.date, DateFormat).valueOf()
    );

    let summary = {
      totalhours: minutesToHoursAndMinutes(totalMinutes),
      buckets: [...bucketsSet],
    };

    settableSummary(summary);

    return formatedData;
  };

  const [chartData, setchartData] = useState({
    pie: [],
    column: [],
  });
  const generateChartData = () => {
    let data = rawTimesheetData;
    let columnChart = [];
    let pie = [];
    let perBucketUtilisation = {};
    data.forEach((item) => {
      let minutes = findGapInMinutes(item.start_time, item.end_time);
      perBucketUtilisation[item.projectBucket] = perBucketUtilisation[
        item.projectBucket
      ]
        ? perBucketUtilisation[item.projectBucket] + minutes
        : minutes;
    });
    project.project_buckets.forEach((item) => {
      columnChart.push(
        ...[
          {
            bucket: item.name,
            type: "Allocated",
            hours: item.hours,
          },
          {
            bucket: item.name,
            type: "Worked",
            hours: perBucketUtilisation[item.name] / 60,
          },
        ]
      );
      if (item.isActive) {
        pie.push({
          bucket: item.name,
          percent: perBucketUtilisation[item.name] / 60 / item.hours,
        });
      }
    });
    setchartData({ column: columnChart, pie });
  };

  useEffect(() => {
    if (project.project_buckets?.length > 0) {
      const transformed = transformTimesheetData();
      setBucketsTimesheetData(transformed);
    }
  }, [selectedBucket, rawTimesheetData]);

  useEffect(() => {
    if (project.project_buckets?.length > 0) {
      let buckets = new Set();
      rawTimesheetData.map((item) => buckets.add(item.projectBucket));
      setbucketsForFilter([...buckets]);
      generateChartData();
    } else {
      // todo: timesheet for normal projects
    }
  }, [rawTimesheetData]);

  const fetchTimesheet = async (id) => {
    setLoading(true);
    try {
      const rawData = await getTimsheetsPerProject(id);
      setrawTimesheetData(rawData);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const projectColumn = [
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      width: 100,
      fixed: "left",
    },
    {
      title: "Hours",
      dataIndex: "minutes",
      key: "hours",
      width: 80,
      fixed: "left",
      render: (data) => minutesToHoursAndMinutes(data),
    },
    {
      title: "Bucket",
      dataIndex: "bucket",
      key: "bucket",
      fixed: "left",
      width: 130,
      render: (data) => (
        <>
          {[...data].map((item) => (
            <div key={item}>{item}</div>
          ))}
        </>
      ),
    },
    {
      title: "Employees",
      dataIndex: "employees",
      key: "employees",
      width: 200,
      render: (item) => {
        return (
          <div className="flex flex-wrap gap-1">
            {item &&
              item?.map((employee, idx) => (
                <div className="rounded-full border-2 flex p-0 pr-2 items-center ">
                  <RenderEmployeeFullName
                    employee={employee.employee}
                    noRedirect
                    showPopover
                    showAvatar
                    avatarSize={24}
                  />
                  <Popover content={employee.description}>
                    <div className="text-gray-500 text-smallest cursor-help">
                      ({employee?.hours})
                    </div>
                  </Popover>
                </div>
              ))}
          </div>
        );
      },
    },
  ];

  // charts
  const columnChartConfig = useMemo(
    () => ({
      data: chartData.column,
      isGroup: true,

      xField: "bucket",
      yField: "hours",
      seriesField: "type",
      label: {
        position: "middle",
      },
    }),
    [chartData.column]
  );

  // add update bucket
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editBucket, setEditBucket] = useState(null);
  const [form] = Form.useForm();

  const showModal = (bucket = null) => {
    setEditBucket(bucket);
    if (bucket) form.setFieldsValue(bucket);
    else form.resetFields();
    setIsModalOpen(true);
  };

  const handleOk = async () => {
    form
      .validateFields()
      .then(async (values) => {
        let data = [...bucketsData];
        if (editBucket) {
          // Update logic here (e.g., call API or state update)
          setbucketsData((prev) =>
            prev.map((b) =>
              b.name === editBucket.name ? { ...b, ...values } : b
            )
          );
          data = data.map((b) =>
            b.name === editBucket.name ? { ...b, ...values } : b
          );
        } else {
          // Add logic
          data = [...data, values];
          setbucketsData((prev) => [...prev, values]);
        }
        let input = {
          id: project.id,
          project_buckets: data,
        };
        setIsModalOpen(false);
        await UpdateProjectCustomAction(input);
      })
      .catch(() => {});
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const [bucketsData, setbucketsData] = useState([]);
  const [viewActiveBucket, setviewActiveBucket] = useState(true);

  return (
    <>
      {loading ? (
        <Loader
          title={`We’re gathering insights on how time has been distributed.`}
        />
      ) : (
        <>
          {bucketsData?.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-1 gap-2">
              <div className={boxClass}>
                <div className="flex gap-2 items-start justify-between">
                  <div className="flex align-middle items-center gap-2">
                    <Typography.Title level={5} className="mb-0">
                      Buckets
                    </Typography.Title>
                    <Divider type="vertical" />
                    <Switch
                      size="small"
                      onChange={setviewActiveBucket}
                      checked={viewActiveBucket}
                    />
                    &nbsp; View Active Buckets
                  </div>
                  <Button
                    size="small"
                    type="primary"
                    onClick={() => showModal()}
                  >
                    Add Bucket
                  </Button>
                </div>
                <div className={`grid grid-cols-1 md:grid-cols-4 gap-2`}>
                  {bucketsData
                    ?.filter((item) => item.isActive === viewActiveBucket)
                    .map((bucket) => (
                      <div
                        key={bucket.name}
                        className="border rounded-md p-4 flex flex-col shadow-sm bg-white"
                      >
                        <div className="flex items-start justify-between mb-2">
                          <span className="font-semibold">{bucket.name}</span>
                          <SettingTwoTone
                            twoToneColor={primaryColor}
                            className="cursor-pointer hover:rotate-45 duration-100"
                            onClick={() => showModal(bucket)}
                          />
                        </div>
                        <Divider className="my-0" />
                        <div className="text-sm">
                          <div>
                            <span className="font-medium">Hours:</span>{" "}
                            {bucket.hours}
                          </div>
                          <div>
                            <span className="font-medium">Revenue:</span>{" "}
                            {FormatCurrency(bucket.cost, false)}
                          </div>
                          <div>
                            <span className="font-medium">Reminder:</span>{" "}
                            {bucket.reminder ? "Yes" : "No"}
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
              <div className={`grid grid-cols-1 md:grid-cols-2 gap-2`}>
                <div className={boxClass}>
                  <Typography.Title level={5}>
                    Active Buckets Utilisation
                  </Typography.Title>
                  <div className="flex flex-wrap gap-2 pt-2">
                    {chartData.pie.map((item) => (
                      <div
                        key={item.bucket}
                        className={`flex flex-col justify-center items-center rounded-md p-4 ${boxClass}`}
                      >
                        <div>
                          <Liquid
                            {...{
                              percent: item.percent,
                              outline: {
                                distance: 4,
                              },
                              height: 200,
                              width: 200,
                              autoFit: true,
                              theme: {
                                styleSheet: {
                                  brandColor:
                                    item.percent >= 1
                                      ? "red"
                                      : item.percent > 0.8
                                      ? "orange"
                                      : "green",
                                },
                              },
                            }}
                          />
                        </div>
                        {item?.bucket}
                      </div>
                    ))}
                  </div>
                </div>
                <div className={boxClass}>
                  <Typography.Title level={5}>
                    Bucket Wise Utilisation
                  </Typography.Title>
                  <Column {...columnChartConfig} />
                </div>
              </div>
              <div className={boxClass}>
                <CustomTable
                  title={"Timesheet"}
                  Actions={
                    <Select
                      className="w-96 mb-1"
                      defaultValue={selectedBucket}
                      onChange={setselectedBucket}
                      options={[
                        { value: "All", label: "All" },
                        ...bucketsForFilter?.map((bucket) => {
                          return { value: bucket, label: bucket };
                        }),
                      ]}
                    />
                  }
                  columns={projectColumn}
                  dataSource={bucketsTimesheetData}
                  size="small"
                  pagination={false}
                  scroll={{ y: 550 }}
                  sticky={{ offsetHeader: 0 }}
                  summary={(data) => (
                    <Table.Summary>
                      <Table.Summary.Cell colSpan={4}>
                        <div className="p-1 w-full">
                          <div className="">
                            <div>
                              Total Hours Worked:{" "}
                              <span className="font-semibold">
                                {tableSummary.totalhours}
                              </span>
                            </div>
                            <div className="flex gap-1">
                              Buckets:{" "}
                              <span className="font-semibold flex gap-2 flex-wrap">
                                {tableSummary.buckets?.map((item) => (
                                  <span
                                    key={item}
                                    className="rounded-full border-2 px-2"
                                  >
                                    {item}
                                  </span>
                                ))}
                              </span>
                            </div>
                          </div>
                        </div>
                      </Table.Summary.Cell>
                    </Table.Summary>
                  )}
                />
              </div>
            </div>
          ) : // TODO: Incase of retainer project. add timesheets data
          null}
        </>
      )}
      <Modal
        title={editBucket ? "Update Bucket" : "Add Bucket"}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="Save"
        cancelText="Cancel"
        // bodyStyle={{ paddingTop: 12, paddingBottom: 0 }}
        // style={{ top: 80 }}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="Name"
            name="name"
            rules={[
              { required: true, message: "Please input the bucket name" },
            ]}
          >
            <Input size="middle" placeholder="Bucket Name" />
          </Form.Item>
          <div className="grid grid-cols-2 gap-2">
            <Form.Item
              label="Hours"
              name="hours"
              rules={[{ required: true, message: "Please input hours" }]}
            >
              <InputNumber
                min={0}
                className="w-full"
                size="middle"
                placeholder="Bucket Hours"
              />
            </Form.Item>
            <Form.Item
              label="Revenue ($)"
              name="cost"
              rules={[{ required: true, message: "Please input Revenue" }]}
            >
              <InputNumber
                min={0}
                className="w-full"
                size="middle"
                placeholder="Bucket Revenue in $"
              />
            </Form.Item>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <Form.Item label="Active" name="isActive" valuePropName="checked">
              <Switch />
            </Form.Item>
          </div>
        </Form>
      </Modal>
    </>
  );
}

export default ProjectTimesheet;
