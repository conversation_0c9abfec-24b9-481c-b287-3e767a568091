import { Empty } from "antd";
import Loader from "Commons/Loader";
import { ListProjectHistories } from "Pages/ProjectV2/Actions/ProjectActions";
import ProjectHistory from "Pages/ProjectV2/Components/ProjectDetailTabs/Activity/ProjectHistory";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { ListProjects } from "utils/Actions";
import { GetSortOrder } from "utils/commonMethods";

function ActivityTab() {
  const [projectHistory, setProjectHistory] = useState([]);
  const params = useParams();
  const projectID = params?.id;
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (projectID) {
      getProjectHistory();
    }
  }, [projectID]);

  const getProjectHistory = async () => {
    setLoading(true);
    try {
      const filter = {
        projectProject_historyId: { eq: projectID },
      };
      const data = await ListProjectHistories(filter);

      // Sort by createdAt in descending order
      const sortedItems = [...data].sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
      );

      setProjectHistory(sortedItems);
    } catch (error) {
      console.error("Error fetching project history", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="text-base font-semibold mb-3 ml-1">Project Activity</div>
      {loading ? (
        <Loader title={"Loading Project History"} />
      ) : projectHistory.length === 0 ? (
        <Empty description="No project history found" />
      ) : (
        <ProjectHistory history={projectHistory} />
      )}
    </div>
  );
}

export default ActivityTab;
