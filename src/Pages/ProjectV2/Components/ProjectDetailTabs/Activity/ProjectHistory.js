import { Collapse, Descriptions } from "antd";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import moment from "moment";
import React from "react";
import { useParams } from "react-router-dom";
import { DateFormat } from "utils/constants";
import { ToUSD } from "utils/SalaryHelper";

const { Panel } = Collapse;

function ProjectHistory(props) {
  const params = useParams();
  const formatDate = (date) => {
    return moment(date)?.format(DateFormat);
  };

  return (
    <Collapse className="w-full">
      {props?.history?.map((item, index) => (
        <Panel
          header={formatDate(item?.createdAt)}
          key={item?.createdAt || index}
        >
          <Descriptions bordered size="small">
            <Descriptions.Item label="Period" span={3}>
              {formatDate(item?.start_time)} - {formatDate(item?.end_time)}
            </Descriptions.Item>
            <Descriptions.Item label="Amount" span={3}>
              {ToUSD(item?.cost, 1)} ({item?.costType})
            </Descriptions.Item>
            <Descriptions.Item label="Allocations" span={3}>
              {item?.projectAllocations?.map((allocation) => (
                <div className="flex items-center">
                  <RenderEmployeeFullName employee={allocation?.employee} /> -{" "}
                  {allocation?.allocation} hr/week
                </div>
              ))}
            </Descriptions.Item>
            <Descriptions.Item label="Product Manager" span={3}>
              <RenderEmployeeFullName
                employee={item?.product_manager}
                showAvatar
                showPopover
              />
            </Descriptions.Item>
            <Descriptions.Item label="Product Strategist" span={3}>
              <RenderEmployeeFullName
                employee={item?.product_strategist}
                showAvatar
                showPopover
              />
            </Descriptions.Item>
            <Descriptions.Item label="Dev Principle" span={3}>
              <RenderEmployeeFullName
                employee={item?.dev_principal}
                showAvatar
                showPopover
              />
            </Descriptions.Item>
          </Descriptions>
        </Panel>
      ))}
    </Collapse>
  );
}

export default ProjectHistory;
