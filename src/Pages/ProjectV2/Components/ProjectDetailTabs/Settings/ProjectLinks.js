import { Col, Form, Input, Row, <PERSON><PERSON>, Divider } from "antd";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { isExecutive, isHr, isUnitTeamLeader } from "store/slices/loginSlice";
import { useSelector } from "react-redux";

const ProjectLinks = () => {
  const ExecutvieLogin = useSelector(isExecutive);
  const HRLogin = useSelector(isHr);
  const unitTeamLeader = useSelector(isUnitTeamLeader);
  const isPowerUser = ExecutvieLogin || HRLogin || unitTeamLeader;
  const urlPattern =
    /(www\.)?[-a-zA-Z0-9_]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/;

  return (
    <>
      <Divider
        orientation="left"
        orientationMargin={0}
        className="!text-base !font-semibold"
      >
        Projects Links{" "}
      </Divider>

      <Row gutter={16} className="mt-3">
        <Col span={8}>
          <Form.Item
            label="Confluence Space"
            name="notion_dashboard_link"
            rules={[
              {
                pattern: urlPattern,
                message: "Please enter valid URL!",
              },
            ]}
          >
            <Input
              className="w-full input-border"
              placeholder="Enter Confluence Space Link"
              disabled={!isPowerUser}
            />
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item
            label="External Jira Link"
            name="notion_backlog_link"
            rules={[
              {
                pattern: urlPattern,
                message: "Please enter valid URL!",
              },
            ]}
          >
            <Input
              className="w-full input-border"
              placeholder="Enter External Jira Link"
              disabled={!isPowerUser}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Figma Link" name="design_link" className="!mb-1">
            <Input disabled={!isPowerUser} />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label="Client POC" name="client_POC">
            <Input
              className="w-full input-border"
              placeholder="Client POC"
              disabled={!isPowerUser}
            />
          </Form.Item>
        </Col>
        {/* Extra Links Section */}
        <Col span={8}>
          <Form.Item label="Extra Links">
            <Form.List name="extra_links">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <div key={key} className="flex items-start gap-2">
                      <Form.Item
                        {...restField}
                        name={[name, "link"]}
                        className="w-full"
                      >
                        <Input
                          placeholder="Add Link"
                          className="input-border"
                          disabled={!isPowerUser}
                        />
                      </Form.Item>

                      <MinusCircleOutlined
                        onClick={() => remove(name)}
                        className="text-red-500 text-lg mt-2 cursor-pointer"
                      />
                    </div>
                  ))}

                  <Form.Item>
                    <Button
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                      disabled={!isPowerUser}
                    >
                      Add Link
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

export default ProjectLinks;
