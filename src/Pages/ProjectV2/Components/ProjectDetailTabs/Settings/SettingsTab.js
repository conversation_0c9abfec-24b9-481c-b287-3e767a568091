import { Divider, Form, message } from "antd";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { UpdateProjectCustom } from "utils/Actions";
import { RESTGet } from "utils/RESTApi";
import debounce from "lodash.debounce";
import { setCachedProjects } from "store/slices/projectListSlice";
import ProjectMeetingConfig from "Pages/ProjectV2/Components/ProjectMeetingConfig";
import Loader from "Commons/Loader";
import GeneralDetails from "Pages/ProjectV2/Components/ProjectDetailTabs/Settings/GeneralDetails";
import ProjectLinks from "Pages/ProjectV2/Components/ProjectDetailTabs/Settings/ProjectLinks";
import ProjectIntegration from "Pages/ProjectV2/Components/ProjectDetailTabs/Settings/ProjectIntegration";
import DangerZoneSection from "Pages/ProjectV2/Components/ProjectDetailTabs/Settings/DangerZoneSection";
import moment from "moment";
import ProjectBurst from "Pages/ProjectV2/Components/ProjectDetailTabs/Settings/ProjectBurst";
import { DateFormat } from "utils/constants";
import { ListProjectsAction } from "Pages/ProjectV2/Actions/ProjectActions";
// import DangerZoneSection from "Pages/ProjectV2/Components/ProjectDetailTabs/Settings/DangerZoneSection";
import ProjectPromptConfig from "Pages/ProjectV2/Components/ProjectDetailTabs/Settings/ProjectPromptConfig";
import ExitStatusConfig from "Pages/ProjectV2/Components/ProjectDetailTabs/Settings/ExitStatusConfig";
import { isUnitTeamLeader } from "store/slices/loginSlice";

function SettingsTab({ onProjectUpdate }) {
  const [projectForm] = Form.useForm();
  const [projects, setProjects] = useState(null);
  const [loading, setLoading] = useState(true);
  const [updateLoading, setUpdateLoading] = useState(false);
  const [selectedClient, setSelectedClient] = useState(null);
  const [tagMap, setTagMap] = useState({});
  const [jiraProjects, setJiraProjects] = useState([]);
  const dispatch = useDispatch();
  const cachedProjects = useSelector(
    (state) => state.projectListReducer.cachedProjects
  );
  const isAPUL = useSelector(isUnitTeamLeader);

  const params = useParams();
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    if (projects) {
      projectForm.setFieldsValue({
        client: projects?.client?.id,
        name: projects.name,
        projectTag: projects?.ProjectTag?.items.map(
          (item) => item?.projectTagID
        ),
        project_type: projects.project_type === "INTERNAL",
        notion_dashboard_link: projects?.notion_dashboard_link,
        notion_backlog_link: projects?.notion_backlog_link,
        extra_links: projects?.extra_links?.map((str) => {
          return { link: str };
        }),
        client_POC: projects?.client_POC,
        internal_slack_channel_id: projects?.internal_slack_channel_id,
        jira_project_id: projects?.jiraProjectId || null,
        bursts: projects?.bursts
          ? JSON.parse(projects.bursts).map((b) => ({
              start_time: b.start_time
                ? moment(b.start_time, DateFormat)
                : null,
              end_time: b.end_time ? moment(b.end_time, DateFormat) : null,
            }))
          : [],
        potential_downgrade_date: projects?.potential_downgrade_date
          ? moment(projects.potential_downgrade_date, DateFormat)
          : null,
        cancellation_date: projects?.cancellation_date
          ? moment(projects.cancellation_date, DateFormat)
          : null,
        domain: projects?.domain,
        toolsUsed: projects?.toolsUsed,
        techStack: projects?.techStack,
        project_app_type: projects?.project_app_type,
        design_link: projects?.design_link,
      });
    }
  }, [projects, projectForm]);

  useEffect(() => {
    getJiraProject();
  }, []);

  useEffect(() => {
    if (params?.id) {
      getProjects();
    }
  }, [params.id]);

  const getProjects = async () => {
    setLoading(true);
    try {
      const data = await ListProjectsAction({ id: { eq: params.id } });
      const selectedProject = data.find((project) => project.id === params.id);
      if (selectedProject) {
        setProjects(selectedProject);
      } else {
        console.warn("No project found with id:", params.id);
      }
    } catch (error) {
      console.error("Failed to fetch projects:", error);
    } finally {
      setLoading(false);
    }
  };
  // Get All projects from the Jira
  const getJiraProject = async (hubProject) => {
    const response = await RESTGet("/api/get-jira-projects");
    const mappedProjects = await ListProjectsAction({
      jiraProjectId: { attributeExists: true },
    });
    const mappedJiraIds = new Set(mappedProjects.map((mp) => mp.jiraProjectId));
    const data = response?.data?.map((jProject) => {
      return {
        ...jProject,
        disabled: mappedJiraIds.has(jProject.id),
      };
    });
    setJiraProjects(data);
  };
  const handleTagMapChange = (newMap) => {
    setTagMap(newMap);
  };
  const handleClientListFetch = (clientList) => {
    setSelectedClient(clientList);
  };
  const updateProjectData = async (data) => {
    try {
      setUpdateLoading(true);
      const newData = {
        id: params.id,
        name: data.name,
        clientProjectsId: data.client,
        project_type: data?.project_type ? "INTERNAL" : "EXTERNAL",
        notion_dashboard_link: data?.notion_dashboard_link,
        notion_backlog_link: data?.notion_backlog_link,
        extra_links: data?.extra_links?.map((link) => link?.link),
        client_POC: data?.client_POC,
        internal_slack_channel_id: data?.internal_slack_channel_id,
        jiraProjectId: data?.jira_project_id,
        bursts: JSON.stringify(
          (data?.bursts || []).map((burst) => ({
            start_time: burst?.start_time?.format(DateFormat),
            end_time: burst?.end_time?.format(DateFormat),
          }))
        ),
        potential_downgrade_date:
          data?.potential_downgrade_date?.format(DateFormat) || null,
        cancellation_date: data?.cancellation_date?.format(DateFormat) || null,
        domain: data?.domain,
        toolsUsed: data?.toolsUsed,
        techStack: data?.techStack,
        project_app_type: data?.project_app_type,
        design_link: data?.design_link,
      };
      await UpdateProjectCustom(newData);
      messageApi.success("Project updated successfully");
      const foundClient = selectedClient.find(
        (item) => item.value === data.client
      );
      const updatedClient = foundClient
        ? { id: foundClient.value, name: foundClient.label }
        : null;
      const updatedProjectTagItems = data.projectTag.map((tagID) => {
        const existing = projects?.ProjectTag?.items?.find(
          (item) => item.projectTagID === tagID
        );
        return {
          ...existing,
          projectTagID: tagID,
          projectTag: {
            title: tagMap[tagID] || existing?.projectTag?.title || "",
          },
        };
      });
      const updatedProjects = cachedProjects.map((project) =>
        project.id === params.id
          ? {
              ...project,
              ...newData,
              client: updatedClient,
              ProjectTag: {
                items: updatedProjectTagItems,
              },
              meetings: {
                items: project.meetings?.items || [],
              },
            }
          : project
      );
      const updatedProjectObject = updatedProjects.find(
        (project) => project.id === params.id
      );
      setProjects(updatedProjectObject);
      onProjectUpdate(updatedProjectObject);
      dispatch(setCachedProjects(updatedProjects));
    } catch (error) {
      console.error(error);
      message.error("Failed to save changes");
    } finally {
      setUpdateLoading(false);
    }
  };
  const debounceSave = debounce((values) => {
    const {
      name = "",
      client = "",
      projectTag = [],
      extra_links = [],
      notion_dashboard_link = "",
      notion_backlog_link = "",
      bursts = [],
      cancellation_date,
      potential_downgrade_date,
      domain = "",
      toolsUsed = [],
      techStack = [],
      project_app_type = "",
      design_link = "",
    } = values;
    // Early return: if any burst is undefined, null, or partially filled
    const hasInvalidBurst = bursts.some((b) => {
      if (!b || typeof b !== "object") return true;
      const hasStart = moment.isMoment(b.start_time);
      const hasEnd = moment.isMoment(b.end_time);
      return (hasStart || hasEnd) && !(hasStart && hasEnd);
    });

    if (hasInvalidBurst) return;

    // Filter valid bursts
    const validBursts = bursts.filter(
      (b) => moment.isMoment(b?.start_time) && moment.isMoment(b?.end_time)
    );

    const shouldSave =
      name.trim() ||
      client.trim() ||
      projectTag.length > 0 ||
      extra_links.some((l) => l?.link?.trim()) ||
      notion_dashboard_link.trim() ||
      notion_backlog_link.trim() ||
      validBursts.length > 0 ||
      moment.isMoment(cancellation_date) ||
      cancellation_date === null ||
      moment.isMoment(potential_downgrade_date) ||
      potential_downgrade_date === null ||
      domain.trim() ||
      toolsUsed.length > 0 ||
      techStack.length > 0 ||
      project_app_type.trim() ||
      design_link.trim();
    if (shouldSave) {
      const cleanedValues = {
        ...values,
        bursts: validBursts,
      };
      updateProjectData(cleanedValues);
    }
  }, 2000);

  return (
    <div id="settings-top">
      {contextHolder}
      {loading ? (
        <Loader title={"Please wait while we fetch project details"} />
      ) : (
        <Form
          layout="vertical"
          form={projectForm}
          onValuesChange={(changedValues, allValues) => {
            debounceSave(allValues);
          }}
        >
          <GeneralDetails
            logo={projects?.logo}
            projectForm={projectForm}
            project={projects}
            onClientListFetch={handleClientListFetch}
            existingTags={projects?.ProjectTag?.items}
            onTagMapChange={handleTagMapChange}
          />
          <ProjectLinks />
          <ProjectIntegration jiraProjects={jiraProjects} />
          <ProjectBurst project={projects} />

          {isAPUL && (
            <ExitStatusConfig
              potential_downgrade_date={projects?.potential_downgrade_date}
              cancellation_date={projects?.cancellation_date}
              status={projects?.status}
              projectForm={projectForm}
            />
          )}
          <ProjectMeetingConfig projectID={projects.id} />
          <Divider
            orientation="left"
            orientationMargin={0}
            className="!text-base !font-semibold"
          >
            Prompt Configuration
          </Divider>
          <div className="text-gray-500 text-sm -mt-2">
            Configure prompts for project "<b>{projects.name}</b>"
          </div>
          <ProjectPromptConfig project={projects} setProject={setProjects} />
          <Divider
            orientation="left"
            orientationMargin={0}
            className="!text-base !font-semibold"
          >
            Danger Zone
          </Divider>
          <DangerZoneSection project={projects} />
        </Form>
      )}
    </div>
  );
}
export default SettingsTab;
