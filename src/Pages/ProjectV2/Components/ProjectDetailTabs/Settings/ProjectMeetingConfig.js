/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  Select,
  Checkbox,
  Button,
  message,
  Drawer,
  Popover,
  Tag,
} from "antd";
import {
  createProjectmeetingEmployee,
  createUpdateProjectMeeting,
  deleteProjectmeetingEmployee,
  ListProjectMeetingsByID,
} from "Pages/ProjectV2/Actions/ProjectActions";

import CustomTable from "Commons/CustomTable";
import { ProjectMeetingTypes, zoomMeetingPattern } from "utils/constants";
import { formatENUMToNormal } from "utils/commonMethods";
import { listEmployeesForSelection } from "Pages/Hiring/Actions/HiringActions";
import { QuestionCircleOutlined } from "@ant-design/icons";
import zoomMeetingIDImg from "assets/images/zoom-meeting-id-guide.png";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import { useSelector } from "react-redux";
import { getCurrentUserData } from "store/slices/loginSlice";
import RenderEmployeeSelect from "AtomicComponents/RenderEmployeeSelect";

function ProjectMeetingConfig({ projectID }) {
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editData, setEditData] = useState(null);
  const [drawerVisible, setDrawerVisible] = useState(false);

  const currentUser = useSelector(getCurrentUserData);

  useEffect(() => {
    if (projectID) {
      getProjetMeetings();
    }
  }, [projectID]);

  const getProjetMeetings = async () => {
    try {
      const res = await ListProjectMeetingsByID(projectID);
      setDataSource(res);
    } catch (error) {
      console.log(error);
    }
  };

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      const participants = values.participants;
      delete values.participants;
      let input = {
        ...values,
        projectID,
      };
      if (editData) {
        input = { ...input, id: editData?.id };
      }
      const res = await createUpdateProjectMeeting(input);

      if (editData) {
        const deleteParticipantsIds = editData?.internalEmployees?.items?.map(
          (item) => item?.id
        );
        Promise.all(
          deleteParticipantsIds.map(async (id) => {
            return deleteProjectmeetingEmployee(id);
          })
        );
      }

      await Promise.all(
        participants.map(async (participant) => {
          return createProjectmeetingEmployee({
            projectMeetingsID: res?.id,
            employeeID: participant,
          });
        })
      );

      form.resetFields();
      setEditData(null);
      setDrawerVisible(false);
      getProjetMeetings();
    } catch (error) {
      message.error("Failed to save meeting.");
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (record) => {
    fetchEmployees();
    let data = {
      ...record,
      participants: record?.internalEmployees?.items?.map(
        (item) => item?.employee?.email
      ),
    };
    setEditData(data);
    form.setFieldsValue(data);
    setDrawerVisible(true);
  };

  const columns = [
    {
      title: "Title",
      dataIndex: "title",
      key: "title",
    },
    {
      title: "Meeting Type",
      dataIndex: "type",
      key: "type",
      render: (value) => <Tag>{formatENUMToNormal(value)}</Tag>,
    },
    {
      title: "Meeting ID",
      dataIndex: "meetingID",
      key: "meetingID",
    },
    {
      title: "Participants",
      dataIndex: "internalEmployees",
      key: "participants",
      render: (internalEmployees) =>
        internalEmployees?.items?.map((item) => (
          <Tag>
            <RenderEmployeeFullName employee={item?.employee} noRedirect />
          </Tag>
        )),
    },
    {
      title: "Recurring",
      dataIndex: "isRecurring",
      key: "isRecurring",
      render: (value) => (value ? "Yes" : "No"),
    },
    {
      title: "External",
      dataIndex: "isExternal",
      key: "External",
      render: (value) => (value ? "Yes" : "No"),
    },
    {
      title: "Actions",
      key: "actions",
      render: (_, record) => (
        <>
          <Button type="link" onClick={() => handleEdit(record)}>
            Edit
          </Button>
        </>
      ),
    },
  ];
  const [employees, setEmployees] = useState([]);
  const fetchEmployees = async () => {
    try {
      const employeeList = await listEmployeesForSelection();
      setEmployees(employeeList);
    } catch (error) {
      console.error("Failed to fetch employees:", error);
      message.error("Failed to fetch employee list.");
    }
  };

  return (
    <>
      <Drawer
        title={editData ? "Edit Meeting" : "Add Project Meeting"}
        open={drawerVisible}
        onClose={() => {
          form.resetFields();
          setEditData(null);
          setDrawerVisible(false);
        }}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{ participants: [currentUser?.email] }}
        >
          <Form.Item
            name="type"
            label="Meeting Type"
            rules={[
              { required: true, message: "Please select a meeting type." },
            ]}
          >
            <Select
              options={ProjectMeetingTypes?.map((item) => ({
                label: formatENUMToNormal(item),
                value: item,
              }))}
              placeholder="Select Meeting Type"
            />
          </Form.Item>

          <Form.Item
            name="title"
            label="Title"
            rules={[{ required: true, message: "Please enter a title." }]}
          >
            <Input placeholder="Enter Title" />
          </Form.Item>

          <Form.Item
            name="meetingID"
            label={
              <>
                Zoom Meeting ID{" "}
                <Popover
                  placement="right"
                  content={
                    <img
                      alt="You can find it in your google calendar / Zoom application"
                      src={zoomMeetingIDImg}
                      className="h-48"
                    />
                  }
                >
                  <QuestionCircleOutlined className="ml-2" />
                </Popover>
              </>
            }
            rules={[
              { required: true, message: "Please enter a Zoom meeting ID." },
              {
                pattern: zoomMeetingPattern,
                message:
                  "Please enter valid meeting ID, it should be like 85726085866.",
              },
            ]}
          >
            <Input placeholder="eg: 85726085866" />
          </Form.Item>

          <Form.Item
            label="Select Participants from the team"
            name="participants"
            rules={[
              { required: true, message: "Please select the Participants" },
            ]}
          >
            <Select mode="multiple" placeholder="Select Participants">
              {employees.map((employee, index) => (
                <Select.Option key={index} value={employee.email}>
                  {`${employee.first_name} ${employee.last_name}`}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="isExternal" valuePropName="checked">
            <Checkbox>External Meeting</Checkbox>
          </Form.Item>

          <Form.Item name="isRecurring" valuePropName="checked">
            <Checkbox>Recurring Meeting</Checkbox>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              {editData ? "Update Meeting" : "Create Meeting"}
            </Button>
            {editData && (
              <Button
                type="default"
                onClick={() => {
                  form.resetFields();
                  setEditData(null);
                  setDrawerVisible(false);
                }}
                style={{ marginLeft: "8px" }}
              >
                Cancel
              </Button>
            )}
          </Form.Item>
        </Form>
      </Drawer>

      <CustomTable
        dataSource={dataSource}
        columns={columns}
        loading={loading}
        rowKey="id"
        title="Meetings"
        Actions={
          <Button
            type="primary"
            onClick={() => {
              setDrawerVisible(true);
              fetchEmployees();
            }}
          >
            Create New Meeting
          </Button>
        }
      />
    </>
  );
}

export default ProjectMeetingConfig;
