import { Col, DatePicker, Divider, Form, Row } from "antd";
import { DateFormat } from "utils/constants";

function ExitStatusConfig({
  cancellation_date,
  potential_downgrade_date,
  status,
  projectForm,
}) {
  const showDowngrade = status === "POTENTIAL_DOWNGRADE";
  const showCancellation = status === "CANCELLATION";

  if (!showDowngrade && !showCancellation) return null;

  return (
    <>
      <Divider
        orientation="left"
        orientationMargin={0}
        className="!text-base !font-semibold"
      >
        Exit Milestones
      </Divider>
      <Row gutter={16}>
        {showDowngrade && (
          <Col span={12}>
            <Form.Item
              label="Potential Downgrade Date"
              name="potential_downgrade_date"
            >
              <DatePicker
                className="w-full"
                format={DateFormat}
                allowClear
                onChange={(date) =>
                  projectForm.setFieldsValue({
                    potential_downgrade_date: date ? date : null,
                  })
                }
              />
            </Form.Item>
          </Col>
        )}
        {showCancellation && (
          <Col span={12}>
            <Form.Item label="Cancellation Date" name="cancellation_date">
              <DatePicker
                className="w-full"
                format={DateFormat}
                allowClear
                onChange={(date) =>
                  projectForm.setFieldsValue({
                    cancellation_date: date ? date : null,
                  })
                }
              />
            </Form.Item>
          </Col>
        )}
      </Row>
    </>
  );
}

export default ExitStatusConfig;
