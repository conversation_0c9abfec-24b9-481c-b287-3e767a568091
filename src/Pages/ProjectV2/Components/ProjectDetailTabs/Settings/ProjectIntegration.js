import {
  Col,
  Divider,
  Form,
  Image,
  Input,
  Modal,
  Row,
  Select,
  Typography,
} from "antd";
import { SlackOutlined } from "@ant-design/icons";
import { useState } from "react";
import { slackChannelPattern } from "utils/constants";
import slackChanelIDFindImg from "assets/images/channel-id.png";
import inviteHubSlackImg from "assets/images/invite-hub-slack.png";
import { isExecutive, isHr, isUnitTeamLeader } from "store/slices/loginSlice";
import { useSelector } from "react-redux";
const ProjectIntegration = ({ jiraProjects }) => {
  const [slackIntegrationHelpModalOpen, setSlackIntegrationHelpModalOpen] =
    useState(false);
  const ExecutvieLogin = useSelector(isExecutive);
  const HRLogin = useSelector(isHr);
  const unitTeamLeader = useSelector(isUnitTeamLeader);
  const isPowerUser = ExecutvieLogin || HRLogin || unitTeamLeader;
  return (
    <>
      <Col span={24}>
        <div className="mb-4">
          <Divider
            orientation="left"
            orientationMargin={0}
            className="!text-base !font-semibold !-mb-1"
          >
            Integration
          </Divider>
          <small
            className="text-primary-500 cursor-pointer font-normal"
            onClick={() => setSlackIntegrationHelpModalOpen(true)}
          >
            How to integrate?
          </small>
        </div>

        <Row>
          <Col span={12} className="px-1">
            <Form.Item
              label="Internal Slack Channel ID"
              name="internal_slack_channel_id"
              rules={[
                {
                  pattern: slackChannelPattern,
                  message: "Please enter a valid Slack Channel ID",
                },
              ]}
            >
              <Input
                className="input-border w-full"
                placeholder="e.g. C04QD2JDKH6"
                disabled={!isPowerUser}
              />
            </Form.Item>
          </Col>

          <Col span={12} className="px-1">
            <Form.Item label="Select Jira Projects" name="jira_project_id">
              <Select
                className="input-border"
                showSearch
                placeholder="Select Projects"
                options={jiraProjects?.map(({ id, name, disabled }) => ({
                  value: id,
                  label: name,
                  disabled,
                }))}
                disabled={!isPowerUser}
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              />
            </Form.Item>
          </Col>
        </Row>
      </Col>

      {/* Slack Integration Modal */}
      <Modal
        title="Slack Automation Guide"
        open={slackIntegrationHelpModalOpen}
        onCancel={() => setSlackIntegrationHelpModalOpen(false)}
        footer={null}
        width={800}
        destroyOnClose
        bodyStyle={{ height: 500 }}
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <div style={{ marginBottom: 24 }}>
              <Typography.Title level={5}>
                Step 1: Find and add Slack Channel ID
              </Typography.Title>
              <Divider className="my-1" />
              <Typography.Paragraph>
                Navigate to the Slack Channel Configuration, locate the channel
                ID, copy it, and fill in the corresponding fields in the form.
              </Typography.Paragraph>
              <Image
                src={slackChanelIDFindImg}
                alt="Copy Channel ID"
                style={{ marginBottom: 16, height: 300 }}
              />
            </div>
          </Col>
          <Col span={12}>
            <div style={{ marginBottom: 24 }}>
              <Typography.Title level={5}>
                Step 2: Invite York IE hub to channel
              </Typography.Title>
              <Divider className="my-1" />
              <Typography.Paragraph>
                Open the Slack channel and invite York IE Hub by pasting the
                provided command.
                <br />
                <Typography.Text copyable keyboard>
                  /invite @York IE Hub
                </Typography.Text>
              </Typography.Paragraph>
              <Image
                src={inviteHubSlackImg}
                alt="Invite York IE Hub"
                style={{ marginBottom: 16 }}
              />
            </div>
          </Col>
        </Row>
      </Modal>
    </>
  );
};

export default ProjectIntegration;
