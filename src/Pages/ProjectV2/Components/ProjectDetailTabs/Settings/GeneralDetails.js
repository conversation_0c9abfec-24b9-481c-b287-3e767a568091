import {
  Form,
  Input,
  Row,
  Col,
  Divider,
  Switch,
  message,
  Upload,
  Select,
} from "antd";
import SelectClient from "../../Commons/SelectClient";
import SelectProjectTag from "../../Commons/SelectProjectTag";
import { useWatch } from "antd/es/form/Form";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import {
  isExecutive,
  isSquadLeader,
  isUnitTeamLeader,
} from "store/slices/loginSlice";
import { GetFileFromS3 } from "Pages/Profile/function/uploadFile";
import { uploadFileToS3 } from "utils/helperFunction";
import { UpdateProjectCustomAction } from "Pages/ProjectV2/Actions/ProjectActions";
import { getProjectInitials } from "Pages/ProjectV2/utils/projectV2Utils";
import { CameraFilled, LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import ProjectTags from "../../Commons/ProjectTags";
export default function GeneralDetails({
  logo,
  onTagMapChange,
  projectForm,
  onClientListFetch,
  existingTags = [],
  project,
}) {
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState(null);
  const tagValue = useWatch("projectTag", projectForm);

  const ExecutvieLogin = useSelector(isExecutive);
  const unitTeamLeader = useSelector(isUnitTeamLeader);
  const squadLeader = useSelector(isSquadLeader);

  const isPowerUser = ExecutvieLogin || squadLeader || unitTeamLeader;
  const params = useParams();

  useEffect(() => {
    if (params?.id) {
      loadLogo();
    }
  }, []);

  //getting logo from s3
  const loadLogo = async () => {
    setLoading(true);
    try {
      const link = await GetFileFromS3(logo);
      setImageUrl(link);
    } catch (err) {
      console.error("Error loading logo:", err);
      setImageUrl(null);
    } finally {
      setLoading(false);
    }
  };

  const beforeUpload = (file) => {
    const isImage = file.type.startsWith("image/");
    if (!isImage) {
      message.error("Only image files are allowed.");
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error("Image must be smaller than 2MB.");
    }
    return isImage && isLt2M;
  };
  //uploading logo to s3
  const handleCustomRequest = async ({ file, onError, onSuccess }) => {
    try {
      const fileExt = file.name.split(".").pop();
      const fileName = `logo.${fileExt || "png"}`;
      const folderPath = `project-logos/${params.id}`;
      const filePath = `${folderPath}/${fileName}`;
      const finalFile = new File([file], fileName, { type: file.type });
      const { key } = await uploadFileToS3(finalFile, filePath);
      const url = await GetFileFromS3(key);
      setImageUrl(url);
      projectForm.setFieldsValue({ logo: key });
      const newData = {
        id: params.id,
        logo: key,
      };
      await UpdateProjectCustomAction(newData);
      onSuccess("ok");
    } catch (err) {
      console.error("Error uploading logo:", err);
      onError(err);
    }
  };
  const uploadButton = (
    <div>
      {loading ? <LoadingOutlined /> : <PlusOutlined title="Upload" />}
      <div>{loading ? "Uploading" : "Upload"}</div>
    </div>
  );
  return (
    <>
      <Divider
        orientation="left"
        orientationMargin={0}
        className="!text-base !font-semibold"
      >
        General Details
      </Divider>
      <Row gutter={12} align="top">
        <Col xs={24} sm={8} md={6} lg={4} xl={3}>
          <Form.Item label="Project Logo" className="!mb-0">
            <Upload
              name="projectLogo"
              disabled={!isPowerUser}
              listType={imageUrl ? "text" : "picture-card"}
              className={`avatar-uploader m-0 cursor-auto w-full ${
                imageUrl ? "no-border" : ""
              }`}
              customRequest={handleCustomRequest}
              beforeUpload={beforeUpload}
              showUploadList={false}
            >
              {imageUrl ? (
                <div className="relative w-[100px] h-[100px]">
                  <div className="w-full h-full rounded-full overflow-hidden border">
                    <img
                      src={imageUrl}
                      alt="logo"
                      className="w-full h-full object-cover rounded "
                      onError={(e) => {
                        e.target.onerror = null;
                        setImageUrl(null); // Fallback to initials if image fails
                      }}
                    />
                  </div>
                  {!!isPowerUser && (
                    <div className="relative w-4/5 flex items-center  justify-center text-primary-500 bg-white border border-primary-500 rounded-md p-0 ml-2 -mt-2">
                      <CameraFilled className="text-[12px] mr-1" /> Change
                    </div>
                  )}
                </div>
              ) : (
                <div className="relative w-[100px] h-[100px]">
                  {/* Initials circle */}
                  <div className="flex items-center justify-center bg-gray-100 rounded-full w-full h-full text-xl font-semibold text-gray-700 border ">
                    {getProjectInitials(project.name)}
                  </div>
                  {/* Upload button overlay */}
                  {!!isPowerUser && (
                    <div className="absolute bottom-0 left-1/2 -translate-x-1/2 flex items-center justify-center text-primary-500 bg-white border border-primary-500 rounded-md px-2 py-[2px] text-sm font-medium">
                      <CameraFilled className="text-[12px] mr-1" /> Change
                    </div>
                  )}
                </div>
              )}
            </Upload>
          </Form.Item>
          <Form.Item
            label="Is Internal?"
            name="project_type"
            valuePropName="checked"
            className="mt-4 ml-1"
          >
            <Switch disabled={!isPowerUser} />
          </Form.Item>
        </Col>
        {/* Right: Form Fields */}
        <Col span={21}>
          <Row gutter={[12, 12]}>
            {/* Project Name & Client side by side */}
            <Col xs={12} md={8}>
              <Form.Item
                label={<span className="font-semibold">Project Name</span>}
                name="name"
                className="!mb-0 "
                rules={[
                  { required: true, message: "Please enter project name" },
                ]}
              >
                {isPowerUser ? (
                  <Input disabled={!isPowerUser} />
                ) : (
                  <div className="ant-form-text">{project?.name || "-"}</div>
                )}
              </Form.Item>
            </Col>
            <Col xs={12} md={8}>
              <Form.Item
                label={<span className="font-semibold">Project Domain</span>}
                name="domain"
                className="!mb-0"
                rules={[
                  { required: true, message: "Please enter project domain" },
                ]}
              >
                {isPowerUser ? (
                  <Input disabled={!isPowerUser} />
                ) : (
                  <div className="ant-form-text">{project?.domain || "-"}</div>
                )}
              </Form.Item>
            </Col>

            <Col xs={24} md={8}>
              <Form.Item
                label={<span className="font-semibold">Client</span>}
                name="client"
                className="client-dropdown !mb-1"
                rules={[
                  { required: true, message: "Please enter client name" },
                ]}
              >
                {isPowerUser ? (
                  <SelectClient
                    onClientListFetch={onClientListFetch}
                    disabled={!isPowerUser}
                  />
                ) : (
                  <div className="ant-form-text">
                    {project?.client?.name || "-"}
                  </div>
                )}
              </Form.Item>
            </Col>
            {/* Tools Used */}
            <Col xs={24} md={8}>
              <Form.Item
                label={<span className="font-semibold">Tools Used</span>}
                name="toolsUsed"
                className="!mb-1"
              >
                {isPowerUser ? (
                  <Select
                    mode="tags"
                    placeholder="e.g., JIRA, Notion, Trello"
                    disabled={!isPowerUser}
                  />
                ) : (
                  <ProjectTags tags={project?.toolsUsed || []} />
                )}
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                label={<span className="font-semibold">Tech Stack</span>}
                name="techStack"
                className="!mb-1"
              >
                {isPowerUser ? (
                  <Select mode="tags" placeholder="e.g., React, Node.js, AWS" />
                ) : (
                  <ProjectTags tags={project?.techStack || []} />
                )}
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                label={<span className="font-semibold">Project App Type</span>}
                name="project_app_type"
                className="!mb-1"
              >
                {isPowerUser ? (
                  <Select
                    options={[
                      { label: "Web", value: "Web" },
                      { label: "Mobile", value: "Mobile" },
                      { label: "Hybrid", value: "Hybrid" },
                    ]}
                    disabled={!isPowerUser}
                  />
                ) : (
                  <div className="ant-form-text">
                    {project?.project_app_type || "-"}
                  </div>
                )}
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                label={<span className="font-semibold"> Project Tags</span>}
                name="projectTag"
              >
                {isPowerUser ? (
                  <SelectProjectTag
                    value={tagValue}
                    existingTags={existingTags}
                    onTagMapChange={onTagMapChange}
                    disabled={!isPowerUser}
                  />
                ) : (
                  <ProjectTags tags={project?.ProjectTag?.items || []} />
                )}
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>
    </>
  );
}
