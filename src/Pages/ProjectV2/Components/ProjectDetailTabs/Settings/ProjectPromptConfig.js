import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Form, Checkbox, Typography, Space, Button, message } from "antd";
import { CODE_REVIEW_CHECKLIST_DETAIL } from "Pages/ProjectV2/Constants/Constants";
import { UpdateProjecForAllocationtAction } from "Pages/ProjectV2/Actions/ProjectActions";

const { Text } = Typography;

// Constants for better maintainability
const MESSAGES = {
  SAVE_SUCCESS: "Configuration saved successfully!",
  SAVE_ERROR: "Failed to save configuration",
  LOAD_ERROR: "Error loading configuration",
};

const GRID_CONFIG = {
  ENABLED_ITEMS_TEXT: " items enabled",
  OF_TEXT: " of ",
};

/**
 * ProjectPromptConfig Component
 *
 * Manages code review checklist configuration for projects.
 * Allows users to enable/disable checklist items and save configurations.
 *
 * @param {Object} props - Component props
 * @param {Object} props.project - Project object containing prompt configurations
 * @param {Function} props.setProject - Function to update project state
 */
const ProjectPromptConfig = ({ project, setProject }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [checklist, setChecklist] = useState({});

  /**
   * Creates default checklist configuration based on CODE_REVIEW_CHECKLIST_DETAIL
   */
  const defaultChecklist = useMemo(() => {
    const checklist = {};
    Object.keys(CODE_REVIEW_CHECKLIST_DETAIL).forEach((key) => {
      checklist[key] = true;
    });
    return checklist;
  }, []);

  /**
   * Parses and validates project prompt configurations
   * @param {string} promptConfigurations - JSON string of configurations
   * @returns {Object|null} Parsed configuration or null if invalid
   */
  const parseProjectConfigurations = useCallback((promptConfigurations) => {
    if (!promptConfigurations) return null;

    try {
      const parsed = JSON.parse(promptConfigurations);
      // Validate that parsed data has expected structure
      if (parsed && typeof parsed === "object") {
        return parsed;
      }
      return null;
    } catch (error) {
      console.error("Error parsing project configurations:", error);
      return null;
    }
  }, []);

  /**
   * Merges default checklist with project-specific configurations
   * @param {Object} parsedConfig - Parsed project configuration
   * @returns {Object} Merged checklist configuration
   */
  const mergeChecklistConfigurations = useCallback(
    (parsedConfig) => {
      let mergedChecklist = { ...defaultChecklist };

      if (parsedConfig?.codeReviewChecklist) {
        Object.entries(defaultChecklist).forEach(([key, defaultValue]) => {
          if (
            Object.prototype.hasOwnProperty.call(
              parsedConfig.codeReviewChecklist,
              key
            )
          ) {
            mergedChecklist[key] = parsedConfig.codeReviewChecklist[key];
          }
        });
      }

      return mergedChecklist;
    },
    [defaultChecklist]
  );

  /**
   * Initialize checklist configuration from project data
   */
  useEffect(() => {
    const parsedConfig = parseProjectConfigurations(
      project?.promptConfigurations
    );
    const mergedChecklist = mergeChecklistConfigurations(parsedConfig);

    if (!parsedConfig && project?.promptConfigurations) {
      // Show error only if parsing failed for non-empty configuration
      message.error(MESSAGES.LOAD_ERROR);
    }

    setChecklist(mergedChecklist);
    form.setFieldsValue(mergedChecklist);
  }, [
    project?.promptConfigurations,
    parseProjectConfigurations,
    mergeChecklistConfigurations,
    form,
  ]);

  /**
   * Handles individual checklist item changes
   * @param {string} field - Field name to update
   * @param {boolean} checked - New checked state
   */
  const handleChecklistChange = useCallback(
    (field, checked) => {
      const updatedChecklist = {
        ...checklist,
        [field]: checked,
      };
      setChecklist(updatedChecklist);
      form.setFieldValue(field, checked);
    },
    [checklist, form]
  );

  /**
   * Saves the current configuration to the backend
   */
  const handleSave = useCallback(async () => {
    if (!project?.id) {
      message.error("Project ID is required to save configuration");
      return;
    }

    setLoading(true);
    try {
      const values = form.getFieldsValue();
      const configurationData = {
        codeReviewChecklist: values,
      };

      const input = {
        id: project.id,
        promptConfigurations: JSON.stringify(configurationData),
      };

      await UpdateProjecForAllocationtAction(input);

      setProject((prev) => ({
        ...prev,
        promptConfigurations: JSON.stringify(configurationData),
      }));

      message.success(MESSAGES.SAVE_SUCCESS);
    } catch (error) {
      console.error("Error saving configuration:", error);
      message.error(MESSAGES.SAVE_ERROR);
    } finally {
      setLoading(false);
    }
  }, [form, project?.id, setProject]);

  /**
   * Resets form to last saved configuration
   */
  const handleReset = useCallback(() => {
    const parsedConfig = parseProjectConfigurations(
      project?.promptConfigurations
    );

    if (parsedConfig?.codeReviewChecklist) {
      setChecklist(parsedConfig.codeReviewChecklist);
      form.setFieldsValue(parsedConfig.codeReviewChecklist);
    } else {
      // Reset to default if no saved configuration
      setChecklist(defaultChecklist);
      form.setFieldsValue(defaultChecklist);
    }
  }, [
    project?.promptConfigurations,
    parseProjectConfigurations,
    form,
    defaultChecklist,
  ]);

  /**
   * Sorted checklist entries for consistent display order
   */
  const sortedChecklistEntries = useMemo(() => {
    return Object.entries(checklist).sort(([a], [b]) =>
      (CODE_REVIEW_CHECKLIST_DETAIL[a]?.label || a).localeCompare(
        CODE_REVIEW_CHECKLIST_DETAIL[b]?.label || b
      )
    );
  }, [checklist]);

  /**
   * Calculate enabled items statistics
   */
  const checklistStats = useMemo(() => {
    const allEntries = Object.entries(CODE_REVIEW_CHECKLIST_DETAIL);

    return {
      enabledCount: allEntries.filter(([key]) => checklist[key]).length,
      totalCount: allEntries.length,
    };
  }, [checklist]);

  /**
   * Renders individual checklist item
   * @param {string} field - Field name
   * @param {*} value - Field value
   * @returns {JSX.Element} Rendered checklist item
   */
  const renderChecklistItem = useCallback(
    ([field, value]) => {
      const fieldConfig = CODE_REVIEW_CHECKLIST_DETAIL[field];
      if (!fieldConfig) return null;

      return (
        <div key={field}>
          <Form.Item name={field} valuePropName="checked" className="mb-0">
            <Checkbox
              checked={value}
              onChange={(e) => handleChecklistChange(field, e.target.checked)}
              className="text-sm font-medium"
              data-testid={`checklist-item-${field}`}
              disabled={fieldConfig.disabled}
            >
              <span className="text-gray-800">
                {fieldConfig.label || field}
              </span>
            </Checkbox>
          </Form.Item>
        </div>
      );
    },
    [handleChecklistChange]
  );

  return (
    <div className="mt-2" data-testid="project-prompt-config">
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        className="space-y-2"
        initialValues={checklist}
      >
        <div className="grid grid-cols-1">
          {sortedChecklistEntries.map(renderChecklistItem)}
        </div>

        <div className="flex justify-between items-center p-2 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            <Text>
              <span
                className="font-medium text-green-600"
                data-testid="enabled-count"
              >
                {checklistStats.enabledCount}
              </span>
              {GRID_CONFIG.OF_TEXT}
              <span className="font-medium" data-testid="total-count">
                {checklistStats.totalCount}
              </span>
              {GRID_CONFIG.ENABLED_ITEMS_TEXT}
            </Text>
          </div>

          <Space>
            <Button
              onClick={handleReset}
              className="border-gray-300 text-gray-600 hover:border-gray-400"
              disabled={loading}
              data-testid="reset-button"
            >
              Reset
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              Save Configuration
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
};

export default React.memo(ProjectPromptConfig);
