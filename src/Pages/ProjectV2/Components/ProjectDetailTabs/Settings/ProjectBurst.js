import { But<PERSON>, <PERSON>, Divider, <PERSON>, Row, DatePicker } from "antd";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { DateFormat } from "utils/constants";

function ProjectBurst() {
  return (
    <>
      <Divider
        orientation="left"
        orientationMargin={0}
        className="!text-base !font-semibold"
      >
        Project Burst
      </Divider>
      <Form.List name="bursts">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <Row gutter={16} key={key} align="middle" className="mb-3">
                <Col span={10}>
                  <Form.Item
                    {...restField}
                    name={[name, "start_time"]}
                    label="Start Date"
                    rules={[{ required: true, message: "Required" }]}
                  >
                    <DatePicker className="w-full" format={DateFormat} />
                  </Form.Item>
                </Col>
                <Col span={10}>
                  <Form.Item
                    {...restField}
                    name={[name, "end_time"]}
                    label="End Date"
                    rules={[{ required: true, message: "Required" }]}
                  >
                    <DatePicker className="w-full" format={DateFormat} />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <MinusCircleOutlined onClick={() => remove(name)} />
                </Col>
              </Row>
            ))}
            <Form.Item>
              <Button
                type="dashed"
                onClick={() => add()}
                block
                icon={<PlusOutlined />}
              >
                Add Burst Period
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>
    </>
  );
}

export default ProjectBurst;
