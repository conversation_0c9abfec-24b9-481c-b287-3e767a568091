import { But<PERSON>, Mo<PERSON>, message } from "antd";
import { useState } from "react";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import CurrentAllocationsList from "Pages/ProjectV2/Components/ProjectDetailTabs/Settings/CurrentAllocationsList";
import {
  deleteEmployeeProjectAllocationCustomAction,
  UpdateProjectCustomAction,
} from "Pages/ProjectV2/Actions/ProjectActions";
import { createGlobalNotification } from "utils/commonMethods";
import { useNavigate } from "react-router-dom";

const DangerZoneSection = ({ project }) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const navigate = useNavigate();

  const allocations = project?.employee_project_allocation?.items ?? [];

  const handleConfirm = async () => {
    setConfirmLoading(true);
    try {
      await UpdateProjectCustomAction({ id: project.id, status: "INACTIVE" });

      //Remove allocations
      if (allocations.length > 0) {
        await Promise.all(
          allocations.map(async (item) => {
            const inputData = { input: { id: item?.id } };
            return deleteEmployeeProjectAllocationCustomAction(inputData);
          })
        );
      }

      //  Notify relevant team members
      const uniqueEmails = [
        ...new Set(allocations.map((a) => a.employee?.email).filter(Boolean)),
      ];

      await Promise.all(
        uniqueEmails.map(async (email) => {
          const notiData = {
            type: "Project",
            message: `Your project allocations have been updated.`,
            toAccount: email,
            actionText: "View",
            actionPathName: `/v2/project-view/${project.id}`,
          };
          return createGlobalNotification(notiData);
        })
      );

      message.success(
        `${project.name} has been successfully marked as inactive.`
      );
      setModalOpen(false);
    } catch (err) {
      message.error(`Failed to mark ${project.name} inactive`);
      console.error("Failed to mark project inactive:", err);
    } finally {
      setConfirmLoading(false);
      navigate(`/v2/project-view/${project.id}`);
    }
  };

  return (
    <>
      <div className="border p-4 mt-2 bg-red-50 rounded-md">
        <Button danger onClick={() => setModalOpen(true)}>
          Mark Project as Inactive
        </Button>

        <Modal
          open={modalOpen}
          onCancel={() => setModalOpen(false)}
          onOk={handleConfirm}
          confirmLoading={confirmLoading}
          okText="Confirm"
          okButtonProps={{ danger: true }}
          title={
            <span>
              <ExclamationCircleOutlined className="text-red-600 mr-2" />
              Are you sure you want to mark <b>{project.name}</b> as Inactive?
            </span>
          }
          style={{ top: 45 }}
        >
          <p className="mb-2">Marking this project as inactive will:</p>
          <ul className="list-disc ml-6 mb-4">
            <li>Remove it from active project views and dashboards.</li>
            <li>Employees will not be able to add timesheets anymore.</li>
            <li>Notify relevant team members of the status change.</li>
            <li>Move associated team from allocation to bench.</li>
          </ul>

          <CurrentAllocationsList allocations={allocations} />
        </Modal>
      </div>
    </>
  );
};

export default DangerZoneSection;
