import { CodeOutlined } from "@ant-design/icons";
import { Col } from "antd";
import CodeQualityList from "Pages/CodeQuality/Components/CodeQualityList";
import { useParams } from "react-router-dom";
import { boxClass } from "utils/TailwindCommonClasses";

function CodeQualityTab() {
  const params = useParams();
  const projectID = params?.id;
  return (
    <div className="-ml-2.5 -mt-2">
      <CodeQualityList type={"PROJECT"} value={projectID} />
    </div>
  );
}

export default CodeQualityTab;
