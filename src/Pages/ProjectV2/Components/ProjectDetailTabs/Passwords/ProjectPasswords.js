import React, { useEffect, useState } from "react";
import { Dropdown, Menu, message, Tooltip } from "antd";
import CustomTable from "Commons/CustomTable";
import {
  decryptPassword,
  deletePassword,
  deleteProjectPasswordEmployeeAccess,
  listEmployeesForProjectPasswordAction,
  listProjectPasswords,
} from "Pages/ProjectV2/Actions/ProjectActions";
import CreateUpdateProjectPasswords from "./CreateUpdateProjectPasswords";
import moment from "moment";
import { DateFormatWithTimeAndSecond } from "utils/constants";
import {
  DeleteOutlined,
  EditOutlined,
  LinkOutlined,
  MoreOutlined,
} from "@ant-design/icons";

import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import { useSelector } from "react-redux";
import { isExecutive, isHr, isSquadLeader } from "store/slices/loginSlice";
import EventLogsModal from "Commons/EventLog/EventLogModal";
import DeleteConfirmPopover from "AtomicComponents/DeleteConfirmPopOver";
import SecureShareModal from "Commons/SecureShare/SecureShareModal";

const ProjectPasswords = ({ projectID }) => {
  const isAnExecutive = useSelector(isExecutive);
  const isAHR = useSelector(isHr);
  const isASL = useSelector(isSquadLeader);
  const isPowerUser = isAnExecutive || isAHR || isASL;

  const [passwords, setPasswords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [allEmployees, setAllEmployees] = useState([]);
  const [PasswordToEdit, setPasswordToEdit] = useState({});
  const [selectedPasswordForLogs, setSelectedPasswordForLogs] = useState(null);
  const [selectedPasswordForSharing, setSelectedPasswordForSharing] =
    useState(null);
  const projectEmployees = [];
  const otherEmployees = [];

  useEffect(() => {
    fetchPasswords();
  }, []);

  const handleSecureSharing = (record) => () => {
    setSelectedPasswordForSharing(null);
    setTimeout(() => {
      setSelectedPasswordForSharing(record);
    }, 0);
  };

  const handleViewLogs = (record) => {
    setSelectedPasswordForLogs(null);
    setTimeout(() => {
      setSelectedPasswordForLogs(record);
    }, 0);
  };
  const getActiveEmployees = (employees) =>
    employees?.items?.filter(
      (item) => item.employee && item.employee.active !== false
    ) || [];

  const passwordDisplayFields = [
    {
      key: "passwordKey",
      label: "Key",
    },
    {
      key: "sharedWith",
      label: "Shared With",
      render: (_, record) => {
        console.log("record shared with ", record);
        const activeEmployees = getActiveEmployees(record?.employees);
        return (
          <div className="flex flex-wrap gap-1">
            {activeEmployees.length ? (
              activeEmployees.map((item, index) => (
                <RenderEmployeeFullName
                  key={index}
                  employee={item.employee}
                  className="!m-0"
                  noRedirect={true}
                  avatarOnly={true}
                />
              ))
            ) : (
              <span className="text-gray-400">-</span>
            )}
          </div>
        );
      },
    },
    {
      key: "updatedAt",
      label: "Last Updated",
      render: (text) => moment(text).format(DateFormatWithTimeAndSecond),
    },
  ];

  const fetchPasswords = async () => {
    if (projectID === undefined) return;
    setLoading(true);
    try {
      const result = await listProjectPasswords(projectID);
      const filter = {
        and: [{ hidden_profile: { ne: true } }, { active: { ne: false } }],
      };
      const employeeDetails = await listEmployeesForProjectPasswordAction(
        filter
      );

      employeeDetails.forEach((employee) => {
        const { employee_project_allocation } = employee;
        const isInProject = employee_project_allocation?.items?.some(
          (allocation) =>
            allocation.projectEmployee_project_allocationId === projectID
        );

        if (isInProject) {
          projectEmployees.push({
            ...employee,
            allocation: employee_project_allocation.items.find(
              (item) => item.projectEmployee_project_allocationId === projectID
            )?.allocation,
          });
        } else {
          otherEmployees.push(employee);
        }
      });
      setAllEmployees([...projectEmployees, ...otherEmployees]);

      setPasswords(result || []);
    } catch (error) {
      message.error("Error fetching passwords");
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePassword = async (id) => {
    try {
      const passwordToDelete = passwords.find((p) => p.id === id);

      if (passwordToDelete?.employees?.items) {
        for (const employeeAccess of passwordToDelete.employees.items) {
          if (employeeAccess.id) {
            deleteProjectPasswordEmployeeAccess(employeeAccess.id);
          }
        }
      }

      await deletePassword(id);
      message.success("Password deleted successfully");
      UpdateLocalPasswordState({ id }, "delete");
    } catch (error) {
      console.error("Error deleting password:", error);
      message.error("Error deleting password");
    }
  };
  const handleEditPassword = (record) => {
    const passwordWithEmployeeDetails = {
      ...record,
      employees:
        record.employees?.items?.map((item) => ({
          ...item,
          email: item.employeeID,
          employee:
            allEmployees.find((emp) => emp.email === item.employeeID) ||
            item.employee,
        })) || [],
    };
    setPasswordToEdit(passwordWithEmployeeDetails);
  };

  const columns = [
    {
      title: "Key",
      dataIndex: "passwordKey",
      key: "key",
    },
    {
      title: "Password",
      dataIndex: "encryptedPassword",
      key: "encryptedPassword",
      render: (text) => (
        <Tooltip title="Double click to copy password">
          <div
            className="blur-sm hover:blur-none select-none duration-300 w-min"
            onDoubleClick={() => {
              navigator.clipboard.writeText(decryptPassword(text));
              message.success("Password copied to clipboard");
            }}
          >
            {decryptPassword(text)}
          </div>
        </Tooltip>
      ),
    },
    {
      title: "Shared With",
      dataIndex: "sharedWith",
      key: "sharedWith",
      render: (_, record) => {
        const activeEmployees = getActiveEmployees(record?.employees);
        return (
          <div className="flex flex-wrap gap-2">
            {activeEmployees.length ? (
              activeEmployees.map((item, index) => (
                <RenderEmployeeFullName
                  key={index}
                  employee={item.employee}
                  className="gap-1 bg-gray-100 p-1 pr-2 rounded-full"
                  noRedirect
                  showPopover
                  showAvatar
                />
              ))
            ) : (
              <span className="text-gray-400">-</span>
            )}
          </div>
        );
      },
    },
    {
      title: "Last Updated",
      dataIndex: "updatedAt",
      key: "updatedAt",
      render: (text) => moment(text).format(DateFormatWithTimeAndSecond),
      sorter: {
        compare: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt),
      },
      defaultSortOrder: "descend",
    },
    {
      title: "Actions",
      key: "actions",
      render: (text, record) => (
        <Dropdown
          overlay={
            <Menu>
              {isPowerUser && (
                <Menu.Item
                  key={"View Details"}
                  onClick={() => handleViewLogs(record)}
                >
                  View Details
                </Menu.Item>
              )}
              <Menu.Item
                key={"secureSharing"}
                onClick={handleSecureSharing(record)}
              >
                <LinkOutlined /> Share Via Link
              </Menu.Item>
              <Menu.Item
                key={"Edit"}
                onClick={() => handleEditPassword(record)}
              >
                <EditOutlined /> Edit Password
              </Menu.Item>
              <Menu.Item key={"delete"}>
                <DeleteConfirmPopover
                  title="Are you sure you want to delete this project password?"
                  onConfirm={() => handleDeletePassword(record.id)}
                >
                  <DeleteOutlined /> Delete Password
                </DeleteConfirmPopover>
              </Menu.Item>
            </Menu>
          }
        >
          <MoreOutlined className="rotate-90 text-xl text-primary-500 cursor-pointer" />
        </Dropdown>
      ),
    },
  ];

  const UpdateLocalPasswordState = (password, type) => {
    let data = [...passwords];
    switch (type) {
      case "delete":
        data = data.filter((item) => item.id !== password.id);
        break;
      case "create":
        data.push({
          ...password,
          employees: {
            items: password.employees?.items || [],
          },
        });
        break;
      case "update":
        data = data.map((item) => {
          if (item.id === password.id) {
            return {
              ...item,
              ...password,
              employees: {
                items:
                  password.employees?.items?.map((emp) => ({
                    ...emp,
                    id: emp.id,
                  })) || [],
              },
            };
          }
          return item;
        });
        break;
      default:
        break;
    }
    data.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
    setPasswords(data);
    setPasswordToEdit({});
  };

  return (
    <div>
      <CustomTable
        columns={columns}
        title={"Project Passwords"}
        dataSource={passwords}
        rowKey="id"
        loading={loading}
        Actions={
          <CreateUpdateProjectPasswords
            projectId={projectID}
            onDone={(data) =>
              UpdateLocalPasswordState(
                data,
                PasswordToEdit?.id ? "update" : "create"
              )
            }
            PasswordToEdit={PasswordToEdit}
            setPasswordToEdit={setPasswordToEdit}
            allEmployees={allEmployees}
          />
        }
      />
      <EventLogsModal
        relationId={selectedPasswordForLogs?.id}
        recordData={selectedPasswordForLogs}
        displayFields={passwordDisplayFields}
        showDetails={true}
        showLinkHistory={true}
        modalTitle="Password Details"
      />
      <SecureShareModal
        record={selectedPasswordForSharing}
        recordType="password"
      />
    </div>
  );
};

export default ProjectPasswords;
