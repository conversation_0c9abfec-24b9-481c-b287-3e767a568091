import React, { useEffect, useState } from "react";
import { Button, Form, Input, message, Modal, Select } from "antd";
import CryptoJS from "crypto-js";
import {
  createUpdatePassword,
  createUpdateProjectPasswordEmployeesAccessAction,
  decryptPassword,
  deleteProjectPasswordEmployeeAccess,
} from "Pages/ProjectV2/Actions/ProjectActions";
import RenderEmployeeSelect from "AtomicComponents/RenderEmployeeSelect";

const CreateUpdateProjectPasswords = ({
  projectId,
  onDone = () => {},
  PasswordToEdit,
  allEmployees,
  setPasswordToEdit,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setisModalVisible] = useState(false);

  const handleSavePassword = async (values) => {
    setLoading(true);
    try {
      const { key, password, employees } = values;

      const encryptedPassword = CryptoJS.AES.encrypt(
        password,
        "SECRET_KEY"
      ).toString();

      const response = await createUpdatePassword({
        id: PasswordToEdit?.id,
        passwordKey: key,
        projectId,
        encryptedPassword,
      });

      const newEmployeeAccesses = [];

      if (PasswordToEdit?.id) {
        const existingEmployees = PasswordToEdit.employees || [];
        const emailsToRemove = existingEmployees.filter(
          (emp) => !employees.includes(emp.employeeID || emp.email)
        );
        const emailsToAdd = employees.filter(
          (email) =>
            !existingEmployees.some(
              (emp) => (emp.employeeID || emp.email) === email
            )
        );
        for (const email of emailsToAdd) {
          const accessResponse =
            await createUpdateProjectPasswordEmployeesAccessAction({
              projectPasswordsID: response.id,
              employeeID: email,
            });
          newEmployeeAccesses.push({
            employeeID: email,
            id: accessResponse.id,
          });
        }

        for (const empToRemove of emailsToRemove) {
          try {
            const accessId = empToRemove.id;
            if (accessId) {
              await deleteProjectPasswordEmployeeAccess(accessId);
            }
          } catch (error) {
            console.error(`Error removing access:`, error);
          }
        }
      } else {
        for (const email of employees) {
          const accessResponse =
            await createUpdateProjectPasswordEmployeesAccessAction({
              projectPasswordsID: response.id,
              employeeID: email,
            });
          newEmployeeAccesses.push({
            employeeID: email,
            id: accessResponse.id,
          });
        }
      }

      const selectedEmployees = employees.map((email) => {
        const employee = allEmployees.find((emp) => emp.email === email);
        const newAccess = newEmployeeAccesses.find(
          (access) => access.employeeID === email
        );
        const existingAccess = PasswordToEdit?.employees?.find(
          (emp) => (emp.employeeID || emp.email) === email
        );

        return {
          employee,
          employeeID: email,
          id: newAccess?.id || existingAccess?.id,
        };
      });

      const updatedResponse = {
        ...response,
        employees: {
          items: selectedEmployees,
        },
      };

      message.success(
        `Password ${PasswordToEdit ? "updated" : "saved"} successfully!`
      );
      handleOnCreateUpdate(updatedResponse);
    } catch (error) {
      console.error("Error:", error);
      message.error(`Error ${PasswordToEdit ? "updating" : "saving"} password`);
    } finally {
      setLoading(false);
    }
  };
  const handleOnCreateUpdate = (response) => {
    onDone(response);
    setisModalVisible(false);
    form.resetFields();
  };

  const onCancel = () => {
    setisModalVisible(false);
    form.resetFields();
    if (PasswordToEdit?.id) {
      setPasswordToEdit({});
    }
  };

  useEffect(() => {
    if (PasswordToEdit?.id) {
      const allPasswordEmployees = PasswordToEdit.employees
        ?.map((emp) => emp.employeeID || emp.email)
        .filter(Boolean);

      const activeEmployees = allPasswordEmployees.filter((email) =>
        allEmployees.some((emp) => emp.email === email && emp.active !== false)
      );

      form.setFieldsValue({
        key: PasswordToEdit.passwordKey,
        password: decryptPassword(PasswordToEdit.encryptedPassword),
        employees: activeEmployees,
      });

      if (allPasswordEmployees.length !== activeEmployees.length) {
        message.warning(
          "Some previously worked employees has left the organizations"
        );
      }

      setisModalVisible(true);
    }
  }, [PasswordToEdit, form, allEmployees]);

  return (
    <>
      <Button onClick={() => setisModalVisible(true)} type="primary">
        Add Password{" "}
      </Button>
      <Modal
        title={
          PasswordToEdit ? "Edit Project Password" : "Save Project Password"
        }
        open={isModalVisible}
        onCancel={onCancel}
        footer={null}
      >
        <Form form={form} layout="vertical" onFinish={handleSavePassword}>
          <Form.Item
            name="key"
            label="Key"
            rules={[{ required: true, message: "key is required" }]}
          >
            <Input placeholder="Enter Key" />
          </Form.Item>
          <Form.Item
            name="password"
            label="Password"
            rules={[{ required: true, message: "Password is required" }]}
          >
            <Input placeholder="Enter password" />
          </Form.Item>
          <Form.Item
            name="employees"
            label="Employee Names"
            rules={[
              {
                required: true,
                message: "Please select at least one employee",
              },
            ]}
          >
            <RenderEmployeeSelect
              mode="multiple"
              employeesList={allEmployees.filter(
                (employee) => employee.active !== false
              )}
              placeholder="Select employees"
              allowClear
            />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              Save
            </Button>
            <Button type="secondary" onClick={onCancel} className="ml-2">
              Cancel
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default CreateUpdateProjectPasswords;
