import { LockOutlined } from "@ant-design/icons";
import { Col, Empty } from "antd";
import BetaTag from "AtomicComponents/BetaTag";

import { useParams } from "react-router-dom";
import ProjectPasswords from "./ProjectPasswords";

function PasswordsTab() {
  const params = useParams();
  const projectID = params?.id;
  return (
    <>
      <ProjectPasswords projectID={projectID} />
    </>
  );
}

export default PasswordsTab;
