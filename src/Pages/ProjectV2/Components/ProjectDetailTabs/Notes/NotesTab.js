import { DatePicker, Form, message, Modal, Radio } from "antd";
import { useState } from "react";
import moment from "moment";
import MeetingNotes from "Pages/Project/MeetingNotes";
import { useParams } from "react-router-dom";
import { debounce } from "lodash";
import { DateFormat } from "utils/constants";
import { UpdateProjectCustom } from "utils/Actions";
import { useDispatch, useSelector } from "react-redux";
import { setCachedProjects } from "store/slices/projectListSlice";
import NotesSection from "Pages/ProjectV2/Components/ProjectDetailTabs/Notes/NotesSection";

function NotesTab({ projectStatus, oldNotes = [], onProjectUpdate, project }) {
  const [updateLoading, setUpdateLoading] = useState(false);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(null);

  const [form] = Form.useForm();

  const params = useParams();
  const dispatch = useDispatch();
  const cachedProjects = useSelector(
    (state) => state.projectListReducer.cachedProjects
  );
  const [messageApi, contextHolder] = message.useMessage();

  const updateProjectData = async (data) => {
    try {
      setUpdateLoading(true);

      // Don't merge with parsedNotes — treat form data as the source of truth
      const cleanedNotes =
        data.notes?.map((n) => ({
          ...n,
          date: moment(n.date).format(DateFormat),
        })) || [];

      const newData = {
        id: params.id,
        status: data.status,
        notes: [JSON.stringify(cleanedNotes)],
        potential_downgrade_date: data.potential_downgrade_date
          ? moment(data.potential_downgrade_date).format(DateFormat)
          : null,
        cancellation_date: data.cancellation_date
          ? moment(data.cancellation_date).format(DateFormat)
          : null,
      };

      await UpdateProjectCustom(newData);
      messageApi.success("Changes saved");

      const updatedProjects = cachedProjects.map((project) =>
        project.id === params.id ? { ...project, ...newData } : project
      );
      const updatedProjectObject = updatedProjects.find(
        (project) => project.id === params.id
      );
      onProjectUpdate(updatedProjectObject);
      dispatch(setCachedProjects(updatedProjects));
    } catch (error) {
      console.error(error);
      messageApi.error("Failed to save changes");
    } finally {
      setUpdateLoading(false);
    }
  };

  const debounceSave = debounce((values) => {
    const {
      status,
      notes = [],
      potential_downgrade_date,
      cancellation_date,
    } = values;

    const hasValidStatus = typeof status === "string";
    const hasValidNotes =
      Array.isArray(notes) &&
      notes.every(
        (note) =>
          note?.note?.trim() !== "" &&
          moment(note?.date, DateFormat, true).isValid()
      );

    const hasValidDates = potential_downgrade_date || cancellation_date;

    if (!hasValidStatus && !hasValidNotes && !hasValidDates) return;

    updateProjectData(values);
  }, 2000);

  return (
    <div>
      {contextHolder}
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          status: projectStatus,
        }}
        onValuesChange={(changedValues, allValues) => {
          if (
            changedValues.status === "POTENTIAL_DOWNGRADE" ||
            changedValues.status === "CANCELLATION"
          ) {
            setSelectedStatus(changedValues.status);
            setStatusModalVisible(true);
            return;
          }
          debounceSave(allValues);
        }}
      >
        {/* Status Section */}
        <Form.Item
          label={<div className="text-base font-semibold ">Status</div>}
          name="status"
          rules={[{ required: true, message: "Please select a status" }]}
          colon={false}
        >
          <Radio.Group className="flex gap-4 mt-1 !ml-2">
            <Radio value="RED" className="!text-red-600">
              RED
            </Radio>
            <Radio value="YELLOW" className="!text-yellow-600">
              YELLOW
            </Radio>
            <Radio value="GREEN" className="!text-green-600">
              GREEN
            </Radio>
            <Radio value="POTENTIAL_DOWNGRADE" className="!text-gray-500">
              POTENTIAL DOWNGRADE
            </Radio>
            <Radio value="CANCELLATION" className="!text-gray-900">
              CANCELLATION
            </Radio>
          </Radio.Group>
        </Form.Item>

        <NotesSection
          notes={project.project_notes}
          onProjectUpdate={onProjectUpdate}
        />

        <div className="mt-1 !-ml-2">
          <MeetingNotes projectID={params.id} />{" "}
        </div>

        <Modal
          title={`Select ${
            selectedStatus === "POTENTIAL_DOWNGRADE"
              ? "Potential Downgrade Date"
              : "Cancellation Date"
          }`}
          open={statusModalVisible}
          onCancel={() => {
            // Reset status to previous value to avoid ghost change
            form.setFieldsValue({ status: projectStatus });
            setStatusModalVisible(false);
          }}
          onOk={async () => {
            const isDowngrade = selectedStatus === "POTENTIAL_DOWNGRADE";
            const tempField = isDowngrade
              ? "temp_downgrade_date"
              : "temp_cancellation_date";
            const realField = isDowngrade
              ? "potential_downgrade_date"
              : "cancellation_date";

            try {
              // Validate only the temp field
              const values = await form.validateFields([tempField]);
              const selectedDate = values[tempField];

              if (selectedDate) {
                // Update only the real field
                form.setFieldsValue({ [realField]: selectedDate });

                // Manually trigger debounceSave with updated field
                const allValues = form.getFieldsValue();
                debounceSave({
                  ...allValues,
                  [realField]: selectedDate,
                });

                setStatusModalVisible(false);
              }
            } catch (err) {
              // Validation failed
            }
          }}
        >
          <Form.Item
            name={
              selectedStatus === "POTENTIAL_DOWNGRADE"
                ? "temp_downgrade_date"
                : "temp_cancellation_date"
            }
            label="Select Date"
            rules={[{ required: true, message: "Please select a date" }]}
          >
            <DatePicker className="w-full" format={DateFormat} />
          </Form.Item>
        </Modal>
      </Form>
    </div>
  );
}

export default NotesTab;
