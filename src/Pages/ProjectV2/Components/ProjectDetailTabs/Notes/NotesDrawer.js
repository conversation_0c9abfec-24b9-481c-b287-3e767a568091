import React, { useState, useEffect } from "react";
import { Col, Divider, Drawer, Empty, List, Row, Spin, Typography } from "antd";
import InfiniteScroll from "react-infinite-scroll-component";
import SearchTextInput from "AtomicComponents/SearchTextInput";
import { DateFormat } from "utils/constants";
import moment from "moment";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";

const NotesDrawer = ({ visible, onClose, notes = [] }) => {
  const [filteredNotes, setFilteredNotes] = useState(notes);

  useEffect(() => {
    if (visible) {
      setFilteredNotes(notes);
    }
  }, [visible, notes]);

  const renderNoteDescription = (noteText) => (
    <Typography.Paragraph
      ellipsis={{ rows: 3, expandable: true, symbol: "more" }}
      className="text-gray-700"
    >
      {typeof noteText === "string" ? noteText : ""}
    </Typography.Paragraph>
  );

  return (
    <Drawer
      title="All Notes"
      placement="right"
      width={500}
      onClose={onClose}
      open={visible}
    >
      <SearchTextInput
        placeholder="Search Notes"
        dataSource={notes}
        searchKeys={["note"]}
        onSearch={setFilteredNotes}
        className="mb-4"
      />

      {filteredNotes.length === 0 ? (
        <Empty
          description="No Notes Found"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <InfiniteScroll
          dataLength={filteredNotes.length}
          hasMore={false}
          loader={
            <div className="flex justify-center align-middle ">
              <Spin />
            </div>
          }
          height={"70vh"}
          endMessage={<Divider plain>No more notes</Divider>}
        >
          <List
            itemLayout="vertical"
            dataSource={filteredNotes}
            renderItem={(item) => (
              <List.Item key={item.id || item.date || Math.random()}>
                <Row gutter={[0, 2]}>
                  <Col span={24}>
                    <Typography.Text
                      className="text-primary-500 text-sm !-ml-2"
                      strong
                    >
                      {item.addedby ? (
                        <RenderEmployeeFullName
                          employee={item.addedby}
                          showPopover
                        />
                      ) : (
                        "Unknown"
                      )}
                    </Typography.Text>
                  </Col>
                  <Col span={24}>
                    <Typography.Text type="secondary" className="text-xs">
                      {item.date
                        ? moment(item.date).format(DateFormat)
                        : "Unknown date"}
                    </Typography.Text>
                  </Col>
                  <Col span={24}>{renderNoteDescription(item.note)}</Col>
                </Row>
              </List.Item>
            )}
          />
        </InfiniteScroll>
      )}
    </Drawer>
  );
};

export default NotesDrawer;
