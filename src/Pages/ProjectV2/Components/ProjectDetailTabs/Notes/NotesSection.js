import {
  CloseOutlined,
  CopyOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { Button, Input, message, Popconfirm, Tooltip } from "antd";
import moment from "moment";
import { useState } from "react";
import { DateFormat } from "utils/constants";
import { getEmployeeDetails } from "store/slices/loginSlice";
import { useSelector, useDispatch } from "react-redux";
import {
  createProjectNotes,
  updateProjectNotes,
  deleteProjectNotes,
} from "Pages/ProjectV2/Actions/ProjectActions";
import { setCachedProjects } from "store/slices/projectListSlice";
import { useParams } from "react-router-dom";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import NotesDrawer from "./NotesDrawer";

const NotesSection = ({ notes = {}, onProjectUpdate }) => {
  const noteItems = Array.isArray(notes.items) ? notes.items : [];

  const [sortAsc, setSortAsc] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [editContent, setEditContent] = useState("");
  const [newNote, setNewNote] = useState("");
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);

  const currentUser = useSelector(getEmployeeDetails);
  const dispatch = useDispatch();
  const cachedProjects = useSelector(
    (state) => state.projectListReducer.cachedProjects
  );
  const params = useParams();

  const sortedNotes = [...noteItems].sort((a, b) => {
    const dateA = new Date(a.createdAt);
    const dateB = new Date(b.createdAt);
    return sortAsc ? dateA - dateB : dateB - dateA;
  });

  //Copy Note URL
  const handleCopyNote = (id) => {
    const url = `${window.location.href}#note-${id}`;
    navigator.clipboard.writeText(url);
    message.success("URL copied to clipboard!");
  };

  //Update Project Note
  const handleEditNote = async (noteId, updatedContent) => {
    try {
      const noteToUpdate = noteItems.find((note) => note.id === noteId);
      if (!noteToUpdate) return;

      const input = {
        id: noteId,
        note: updatedContent,
        date: new Date().toISOString(),
      };

      await updateProjectNotes(input);
      const updatedProjects = cachedProjects.map((project) =>
        project.id === params.id
          ? {
              ...project,
              project_notes: {
                ...project.project_notes,
                items: project.project_notes.items.map((item) =>
                  item.id === noteId
                    ? { ...item, note: updatedContent, date: input.date }
                    : item
                ),
              },
            }
          : project
      );
      const updatedProjectObject = updatedProjects.find(
        (project) => project.id === params.id
      );
      onProjectUpdate(updatedProjectObject);
      message.success("Note updated successfully!");
    } catch (error) {
      console.error("Error updating note:", error);
      message.error("Failed to update note.");
    }
  };

  //Create Project Note
  const handleAddNote = async () => {
    if (!newNote.trim()) return;
    try {
      setIsAddingNote(true);
      const input = {
        note: newNote.trim(),
        date: new Date().toISOString(),
        projectProject_notesId: params.id,
        projectNoteAddedbyId: currentUser?.email,
      };
      const response = await createProjectNotes(input);

      //update local cache
      const updatedProjects = cachedProjects.map((project) => {
        if (project.id === params.id) {
          const prevNotes = project.project_notes?.items || [];

          return {
            ...project,
            project_notes: {
              ...project.project_notes,
              items: [response, ...prevNotes],
            },
          };
        }
        return project;
      });

      const updatedProjectObject = updatedProjects.find(
        (project) => project.id === params.id
      );
      onProjectUpdate(updatedProjectObject);
      dispatch(setCachedProjects(updatedProjects));
      setNewNote("");
      setIsAddingNote(false);
      message.success("Note added successfully!");
    } catch (error) {
      console.error("Error adding note:", error);
      message.error("Failed to add note.");
    }
  };

  //Delete Project Note
  const handleDeleteNote = async (noteId) => {
    try {
      await deleteProjectNotes(noteId);
      const updatedProjects = cachedProjects.map((project) =>
        project.id === params.id
          ? {
              ...project,
              project_notes: {
                ...project.project_notes,
                items: project.project_notes.items.filter(
                  (item) => item.id !== noteId
                ),
              },
            }
          : project
      );
      const updatedProjectObject = updatedProjects.find(
        (project) => project.id === params.id
      );
      onProjectUpdate(updatedProjectObject);
      dispatch(setCachedProjects(updatedProjects));
      message.success("Note deleted successfully!");
    } catch (error) {
      console.error("Error deleting note:", error);
      message.error("Failed to delete note.");
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-base font-semibold !ml-2">Notes</h3>
        <div className="flex gap-2 items-center">
          <Button
            size="small"
            onClick={() => setSortAsc(!sortAsc)}
            className="bg-gray-100 border-gray-300 ml-2"
          >
            Sort by Date {sortAsc ? "↓" : "↑"}
          </Button>

          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setIsAddingNote(true);
            }}
          >
            Add Note
          </Button>
        </div>
      </div>
      {isAddingNote && (
        <div className="mt-2 ml-2 relative">
          <Input.TextArea
            placeholder="Write your note..."
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            autoSize={{ minRows: 2 }}
            className="border-gray-300"
          />
          <div className="absolute top-1 right-1 flex gap-1">
            {newNote.trim() !== "" && (
              <Tooltip title="Save">
                <Button
                  icon={<SaveOutlined />}
                  type="primary"
                  size="small"
                  onClick={handleAddNote}
                />
              </Tooltip>
            )}
            <Tooltip title="Cancel">
              <Button
                icon={<CloseOutlined />}
                size="small"
                onClick={() => {
                  setNewNote("");
                  setIsAddingNote(false);
                }}
              />
            </Tooltip>
          </div>
        </div>
      )}

      <div className="space-y-2">
        {sortedNotes.slice(0, 3).map((note) => (
          <div
            className="ml-2 bg-white border border-gray-200 rounded-lg p-2 shadow-sm  justify-between items-start flex"
            key={note.id}
            id={`note-${note.id}`}
          >
            <div>
              <div className="font-medium text-sm text-primary-500 !-ml-2 ">
                {(
                  <RenderEmployeeFullName
                    employee={note?.addedby}
                    showPopover
                  />
                ) || "Unknown"}
              </div>
              <div className="text-xs text-gray-500">
                {moment(note.date).format(DateFormat)}
              </div>
              {editingId === note.id ? (
                <Input.TextArea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  autoSize
                  className="mt-2 border-gray-300"
                />
              ) : (
                <p className="mt-2 text-sm text-gray-700 whitespace-pre-wrap">
                  {note.note}
                </p>
              )}
            </div>

            <div className="flex gap-1">
              {editingId === note.id ? (
                <Tooltip title="Save">
                  <Button
                    icon={<SaveOutlined />}
                    onClick={() => {
                      handleEditNote(note.id, editContent);
                      setEditingId(null);
                    }}
                    size="small"
                    className="border-gray-300"
                  />
                </Tooltip>
              ) : (
                <>
                  <Tooltip>
                    <Button
                      icon={<CopyOutlined />}
                      onClick={() => handleCopyNote(note.id)}
                      size="small"
                      className="border-gray-300"
                    />
                  </Tooltip>
                  <Tooltip>
                    <Button
                      icon={<EditOutlined />}
                      onClick={() => {
                        setEditingId(note.id);
                        setEditContent(note.note);
                      }}
                      size="small"
                      className="border-gray-300"
                    />
                  </Tooltip>
                  <Tooltip>
                    <Popconfirm
                      title="Delete this note?"
                      onConfirm={() => handleDeleteNote(note.id)}
                      okText="Yes"
                      cancelText="No"
                    >
                      <Button
                        icon={<DeleteOutlined />}
                        danger
                        size="small"
                        className="border-gray-300"
                      />
                    </Popconfirm>
                  </Tooltip>
                </>
              )}
            </div>
          </div>
        ))}
        <div className="flex justify-end !-mt-1 !-mr-3">
          {sortedNotes.length > 3 && (
            <Button
              onClick={() => setDrawerVisible(true)}
              type="link"
              className="ml-2 text-primary-500"
            >
              View More Notes
            </Button>
          )}
        </div>
      </div>

      <NotesDrawer
        visible={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        notes={sortedNotes}
      />
    </div>
  );
};
export default NotesSection;
