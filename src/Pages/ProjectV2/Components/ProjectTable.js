import React from "react";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import { useNavigate } from "react-router-dom";
import { colorPallate, DateFormat } from "utils/constants";
import CustomTable from "Commons/CustomTable";
import ProjectTags from "Pages/ProjectV2/Components/Commons/ProjectTags";
import { getMostRecentNote } from "Pages/ProjectV2/utils/projectV2Utils";
/**
 * ProjectTable Component
 * Renders a table view of projects with columns for project name, manager, notes, burn status, tags, and lead.
 * @component
 * @param {Object} props
 * @param {Array} props.projects - List of project objects.
 * @param {Object} props.LastWeekHoursBurn - Weekly burn stats per project ID.
 * @param {boolean} props.WeeklyBurnLoader - Loader flag for burn stats.
 */
const ProjectTable = ({ projects, LastWeekHoursBurn, WeeklyBurnLoader }) => {
  const navigate = useNavigate();

  const columns = [
    {
      title: "Project",
      dataIndex: "name",
      key: "name",
      render: (_, project) => {
        const clientName = project.client?.name || "";
        const displayClientName =
          clientName && clientName !== project.name ? clientName : null;
        const handleClick = () => {
          navigate(`/v2/project-view/${project.id}`);
        };

        return (
          <div>
            <div
              className="flex items-center gap-2 font-semibold cursor-pointer"
              onClick={handleClick}
            >
              <span
                className="w-3 h-3 rounded-full mr-2"
                style={{
                  backgroundColor: colorPallate[project?.status] || "#000",
                }}
              />
              {clientName ? `${clientName} - ${project.name}` : project.name}
            </div>
            {project.client?.point_of_contact && (
              <div className="text-xs text-gray-500">
                POC: {project.client.point_of_contact}
              </div>
            )}
            {displayClientName && (
              <div className="text-xs text-gray-400">{clientName}</div>
            )}
          </div>
        );
      },
    },
    {
      title: "Product Manager",
      dataIndex: "product_manager",
      key: "product_manager",
      render: (pm) =>
        pm ? <RenderEmployeeFullName showPopover={true} employee={pm} /> : "-",
    },
    {
      title: "Notes",
      dataIndex: "notes",
      key: "notes",
      render: (notes) => {
        const recentNote = getMostRecentNote(notes, DateFormat);
        const noteText = recentNote?.note?.trim();
        return (
          <div
            className="max-w-xs whitespace-pre-line text-sm text-gray-700 line-clamp-3"
            title={noteText}
          >
            {noteText || "-"}
          </div>
        );
      },
    },

    {
      title: "Weekly Burn",
      key: "weekly_burn",
      render: (_, project) => {
        const burnData = LastWeekHoursBurn[project.id];
        if (WeeklyBurnLoader) return "Loading...";
        return burnData
          ? `${burnData.burn} / ${burnData.allocation}%`
          : "0:00 / 0%";
      },
    },
    {
      title: "Tags",
      dataIndex: "ProjectTag",
      key: "ProjectTag",
      render: (tagObj) => {
        const tags = tagObj?.items || [];
        return <ProjectTags tags={tags} />;
      },
    },
    {
      title: "Product Dev",
      dataIndex: "dev_principal",
      key: "dev_principal",
      render: (employee) =>
        employee ? (
          <RenderEmployeeFullName showPopover={true} employee={employee} />
        ) : (
          "-"
        ),
    },
    {
      title: "Product Lead",
      dataIndex: "project_lead",
      key: "project_lead",
      render: (lead) => (lead ? `${lead.first_name} ${lead.last_name}` : "-"),
    },
  ];

  return (
    <div>
      <CustomTable
        columns={columns}
        dataSource={projects}
        pagination={true}
        title={false}
        tableHeight={700}
        showSearch={false}
        showCount={false}
        scroll={{ x: "max-content" }}
      />
    </div>
  );
};

export default ProjectTable;
