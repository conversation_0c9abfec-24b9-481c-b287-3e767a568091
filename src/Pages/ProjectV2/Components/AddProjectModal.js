import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Button,
  DatePicker,
  Radio,
  Space,
  InputNumber,
  Row,
  Col,
  Tooltip,
  message,
} from "antd";
import { PlusOutlined, MinusCircleOutlined } from "@ant-design/icons";
import moment from "moment";
import {
  CreateProjectCustom,
  CreateTagProjectRelation,
  ListEmployeesCustomAction,
  ListSquads,
} from "utils/Actions";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import {
  numberPattern,
  STATUS_OPTIONS,
} from "Pages/ProjectV2/Constants/Constants";
import RenderEmployeeSelect from "AtomicComponents/RenderEmployeeSelect";
import SelectClient from "Pages/ProjectV2/Components/Commons/SelectClient";
import SelectProjectTag from "Pages/ProjectV2/Components/Commons/SelectProjectTag";

function AddProjectModal({
  projectManagersOptions,
  productStrategist,
  projectLeadsOptions,
  onSuccess,
}) {
  const [projectForm] = Form.useForm();
  const [projectStartDate, setProjectStartDate] = useState(null);
  const [projectEndDate, setProjectEndDate] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [squadsList, setSquadsList] = useState([]);
  const [costType, setCostType] = useState("mrr");
  const [enableBucketing, setEnableBucketing] = useState(false);
  const [allEmployees, setAllEmployees] = useState([]);
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (open) {
      ListSquads().then((data) => {
        setSquadsList(
          data?.items?.map((squad) => ({
            value: squad?.name,
            label: (
              <>
                {squad?.name} (
                <RenderEmployeeFullName
                  employee={squad?.squad_manager}
                  noRedirect
                  className="m-0"
                />
                )
              </>
            ),
          }))
        );
      });
      ListEmployeesCustomAction().then((data) => {
        setAllEmployees(data);
      });
    }
  }, [open]);

  const costTypeChange = (e) => {
    setCostType(e.target.value);
    if (e.target.value !== "project") {
      setEnableBucketing(false);
    }
  };

  const createProjectHandler = async (data) => {
    if (data?.project_buckets && enableBucketing) {
      data.project_buckets.forEach((bucket) => {
        bucket.isActive = true;
        bucket.reminder = true;
      });
    }

    const addProjectData = {
      clientProjectsId: data.client,
      name: data.name,
      status: data.status,
      projectProduct_managerId: data.product_manager,
      projectProduct_strategistId: data.product_strategiest,
      squadProjectId: data.squad,
      projectDev_principalId: data.dev_principal,
      projectProject_leadId: data.project_lead,
      client_POC: data.client_POC,
      mrr: costType === "mrr" ? data.retainer_cost : 0,
      fixed_cost:
        costType === "project"
          ? enableBucketing
            ? data.project_buckets?.reduce(
                (sum, bucket) => sum + (bucket.cost || 0),
                0
              )
            : data.project_revenue || 0
          : 0,
      start_time: data.start_time?.toISOString(),
      end_time: data.end_time?.toISOString(),
      budget: 0,
      time_tracking_required: false,
      project_buckets: enableBucketing ? data.project_buckets : [],
      project_type:
        projectForm.getFieldValue("isInternal") === true
          ? "INTERNAL"
          : "EXTERNAL",
      notes: data.notes || [],
    };
    setIsLoading(true);
    try {
      const response = await CreateProjectCustom(addProjectData);

      for (const tag of data.projectTag || []) {
        const tagProjectRelationRequest = {
          projectTagID: tag,
          projectID: response?.id,
        };
        await CreateTagProjectRelation(tagProjectRelationRequest);
      }

      message.success("Project created successfully!");
      setOpen(false);
      projectForm.resetFields();
      onSuccess();
    } catch (error) {
      const gqlError = error?.response?.errors?.[0]?.message || error?.message;
      message.error(gqlError || "Failed to create project. Please try again.");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* Trigger button inside modal */}
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={() => setOpen(true)}
      >
        Create Project
      </Button>

      <Modal
        title="Add Project"
        open={open}
        onCancel={() => setOpen(false)}
        onOk={() => projectForm.submit()}
        confirmLoading={isLoading}
        okText="Create Project"
        width={800}
        destroyOnClose
        style={{ top: 20 }}
      >
        <div className="max-h-[70vh] overflow-y-auto pr-[10px]">
          <Form
            form={projectForm}
            onFinish={createProjectHandler}
            layout="vertical"
            className="notification-form"
            initialValues={{ isInternal: false }}
          >
            <Form.Item
              label="Client"
              name="client"
              className="client-dropdown"
              rules={[
                {
                  required: true,
                  message: "Please enter client name",
                },
              ]}
            >
              <SelectClient />
            </Form.Item>
            <Form.Item
              label="Project Name"
              name="name"
              rules={[
                {
                  required: true,
                  message: "Please enter project name",
                },
              ]}
            >
              <Input className="input-border" />
            </Form.Item>
            <Form.Item
              label="ProjectTag"
              name="projectTag"
              className="project-tag-dropdown"
            >
              <SelectProjectTag />
            </Form.Item>
            <Form.Item
              label="Status"
              name="status"
              className="status-dropdown"
              rules={[
                {
                  required: true,
                  message: "Please enter status",
                },
              ]}
            >
              <Select
                className="input-border"
                showSearch
                options={STATUS_OPTIONS}
              />
            </Form.Item>
            <Form.Item
              label="Product Manager"
              name="product_manager"
              rules={[
                {
                  required: true,
                  message: "Please enter product manager name",
                },
              ]}
            >
              <RenderEmployeeSelect
                employeesList={allEmployees}
                placeholder="Select Product Manager"
                showSearch
                className="input-border"
              />
            </Form.Item>
            <Form.Item
              label="Product Strategist"
              name="product_strategiest"
              rules={[
                {
                  required: true,
                  message: "Please enter product strategist",
                },
              ]}
            >
              <RenderEmployeeSelect
                employeesList={allEmployees}
                placeholder="Select Product Strategist"
                showSearch
                className="input-border"
              />
            </Form.Item>
            <Form.Item
              label="Squad"
              name="squad"
              rules={[
                {
                  required: true,
                  message: "Please enter squad",
                },
              ]}
            >
              <Select
                className="input-border"
                showSearch
                options={squadsList}
              />
            </Form.Item>
            <Form.Item
              label="Dev Principal"
              name="dev_principal"
              rules={[
                {
                  required: true,
                  message: "Please enter dev principal",
                },
              ]}
            >
              <RenderEmployeeSelect
                employeesList={allEmployees}
                placeholder="Select Dev Principal"
                showSearch
                className="input-border"
              />
            </Form.Item>
            <Form.Item
              label="Project Lead"
              name="project_lead"
              rules={[
                {
                  required: true,
                  message: "Please enter project lead",
                },
              ]}
            >
              <RenderEmployeeSelect
                employeesList={allEmployees}
                placeholder="Select Project Lead"
                showSearch
                className="input-border"
              />
            </Form.Item>
            <Form.Item label="Client POC" name="client_POC">
              <Input className="input-border" />
            </Form.Item>
            <Form.Item
              name="isInternal"
              label="Is Internal Project?"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              label={
                <Radio.Group
                  onChange={costTypeChange}
                  className="cost-type-radio"
                  value={costType}
                >
                  <Radio value="mrr">Retainer</Radio>
                  {/* <Radio value="fixedCost">Fixed Cost</Radio> */}
                  <Radio value="project">Project</Radio>
                </Radio.Group>
              }
              name={
                costType === "project"
                  ? enableBucketing
                    ? "project_buckets"
                    : "project_revenue"
                  : "retainer_cost"
              }
              rules={[
                {
                  required: true,
                  message: `Please enter ${
                    costType === "project"
                      ? enableBucketing
                        ? "project details"
                        : "project revenue"
                      : "retainer cost"
                  }`,
                },
              ]}
            >
              {costType === "mrr" && (
                <Form.Item name="retainer_cost" className="mb-0">
                  <InputNumber
                    className="w-full"
                    formatter={(value) =>
                      `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                    }
                    parser={(value) => value.replace(/\$\s?|(,*)/g, "")}
                  />
                </Form.Item>
              )}
              {/* Project Bucket Fields */}
              {/*  For Non-Bucket Show Project Revenue*/}
              {costType === "project" && !enableBucketing && (
                <Form.Item name="project_revenue" className="mb-0">
                  <InputNumber
                    className="input-border w-full"
                    formatter={(value) =>
                      value
                        ? `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                        : ""
                    }
                    parser={(value) => value.replace(/\$\s?|(,*)/g, "")}
                    placeholder="Enter Project Revenue"
                  />
                </Form.Item>
              )}
              {/* show buckets when project type and enable bucketing are both selected */}
              {costType === "project" && enableBucketing && (
                <Form.List name="project_buckets" className="w-full">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <Space
                          className="w-full mb-2 flex items-baseline link-section"
                          key={`project-bucket-${key}`}
                        >
                          <Row
                            className="mt-0 mx-0 allocation-row"
                            gutter={10}
                            align="top"
                          >
                            <Col span={10} md={10} sm={24} xs={24}>
                              <Form.Item
                                {...restField}
                                name={[name, "name"]}
                                rules={[
                                  {
                                    required: true,
                                    message: "Required",
                                  },
                                  () => ({
                                    async validator(_, name) {
                                      if (name && name?.trim() === "") {
                                        return Promise.reject(
                                          new Error("Bucket Required")
                                        );
                                      }
                                      const buckets =
                                        name &&
                                        projectForm
                                          .getFieldValue("project_buckets")
                                          ?.filter(
                                            (bucket) =>
                                              bucket?.name?.trim() ===
                                              name?.trim()
                                          );
                                      let errorMessage = null;
                                      if (buckets?.length > 1) {
                                        errorMessage = "Bucket already exists";
                                      }
                                      if (errorMessage) {
                                        return Promise.reject(
                                          new Error(errorMessage)
                                        );
                                      } else return Promise.resolve();
                                    },
                                  }),
                                ]}
                              >
                                <Input
                                  placeholder="Name"
                                  className="input-border"
                                />
                              </Form.Item>
                            </Col>
                            <Col span={7} md={7} sm={12} xs={12}>
                              <Form.Item
                                {...restField}
                                name={[name, "hours"]}
                                rules={[
                                  {
                                    required: true,
                                    message: "Required",
                                  },
                                  {
                                    message: "Enter valid hours",
                                    pattern: numberPattern,
                                  },
                                ]}
                              >
                                <InputNumber
                                  placeholder="Hours"
                                  className="input-border w-full"
                                  formatter={(value, input) => {
                                    const inputValue = input.userTyping
                                      ? value
                                      : value
                                      ? `${value} hours`
                                      : null;
                                    return inputValue;
                                  }}
                                />
                              </Form.Item>
                            </Col>
                            <Col span={7} md={7} sm={12} xs={12}>
                              <Form.Item
                                {...restField}
                                name={[name, "cost"]}
                                rules={[
                                  {
                                    required: true,
                                    message: "Required",
                                  },
                                  {
                                    message: "Enter valid cost",
                                    pattern: numberPattern,
                                  },
                                ]}
                              >
                                <InputNumber
                                  placeholder="Cost"
                                  className="input-border w-full"
                                  formatter={(value) => value && `$ ${value}`}
                                />
                              </Form.Item>
                            </Col>
                          </Row>

                          <MinusCircleOutlined
                            onClick={() => remove(name)}
                            className="text-red-500 fs-12"
                          />
                        </Space>
                      ))}
                      <div className="justify-end">
                        <Button onClick={() => add()}>
                          <PlusOutlined /> Add Bucket
                        </Button>
                      </div>
                    </>
                  )}
                </Form.List>
              )}
            </Form.Item>
            <Form.Item
              name="enableBucketing"
              label="Enable Bucketing"
              className="mb-2"
              hidden={costType !== "project"}
            >
              <Tooltip
                title={
                  enableBucketing ? "Disable Bucketing" : "Enable Bucketing"
                }
              >
                <Switch
                  checked={enableBucketing}
                  onChange={(checked) => setEnableBucketing(checked)}
                />
              </Tooltip>
            </Form.Item>
            <Form.Item
              label="Start Date"
              name="start_time"
              rules={[
                {
                  required: true,
                  message: "Please enter Start Date",
                },
              ]}
            >
              <DatePicker
                className="input-border w-full"
                onChange={(date) => setProjectStartDate(date)}
                disabledDate={(current) =>
                  current && current > moment(projectEndDate).startOf("day")
                }
              />
            </Form.Item>
            <Form.Item
              label="End Date"
              name="end_time"
              className="mb-20"
              rules={[
                {
                  required: true,
                  message: "Please enter End Date",
                },
              ]}
            >
              <DatePicker
                className="input-border w-full"
                onChange={(date) => setProjectEndDate(date)}
                disabledDate={(current) =>
                  current && current < moment(projectStartDate).endOf("day")
                }
              />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  );
}

export default AddProjectModal;
