import React, { Suspense, useEffect, useRef, useState } from "react";
import Background from "../../Commons/Background";
import HelicopterIcon from "assets/game/yorkGame.png";

import NotSupportedIcon from "assets/game/notSupportedIcon.svg";
import { Button, message, Modal, Row, Spin, Typography } from "antd";
import useCustomBreakpointsForGame from "Pages/Game/useCustomBreakpointsForGame";
import { RESTPost } from "utils/RESTApi";
import { useSelector } from "react-redux";
import { getEmployeeDetails } from "store/slices/loginSlice";
import { uploadFileToS3 } from "utils/helperFunction";
import { generateUUID, getGameScores } from "utils/commonMethods";
import { CloseOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";

export default function Game() {
  // Canvas variables
  let cnv;
  let ctx;
  const screens = useCustomBreakpointsForGame();
  const getCurrentEmployeesData = useSelector(getEmployeeDetails);
  const screenshotInterval = useRef(null);
  const gameSessionId = useRef(null);
  const isProcessingScreenshot = useRef(false);
  const screenshotKeys = useRef([]);
  const gameStartTimeRef = useRef(null);
  const resizeObserver = useRef(null);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [gameState, setGameState] = useState("start");
  const [gameData, setGameData] = useState(null);
  const [isButtonLoading, setIsButtonLoading] = useState(false);

  let state;
  state = gameState;

  const captureScreenshot = async () => {
    if (isProcessingScreenshot.current) return;

    isProcessingScreenshot.current = true;
    try {
      const gameCanvas = document.getElementById("my-canvas");
      if (!gameCanvas) {
        console.error("Game canvas not found");
        isProcessingScreenshot.current = false;
        return;
      }

      // Create a new canvas for the screenshot
      const screenshotCanvas = document.createElement("canvas");
      const screenshotCtx = screenshotCanvas.getContext("2d");

      screenshotCanvas.width = gameCanvas.width;
      screenshotCanvas.height = gameCanvas.height;

      // Copy the game canvas content
      screenshotCtx.drawImage(gameCanvas, 0, 0);

      // Convert to blob
      screenshotCanvas.toBlob(
        async (blob) => {
          if (blob) {
            const timestamp = Date.now();
            const userEmail = getCurrentEmployeesData?.email || "SYSTEM";
            const folderPath = `game-sessions/${userEmail}/${gameSessionId.current}`;
            const fileName = `${timestamp}.png`;
            const filePath = `${folderPath}/${fileName}`;

            const file = new File([blob], fileName, { type: "image/png" });

            try {
              const { key } = await uploadFileToS3(file, filePath);
              screenshotKeys.current.push(key);
            } catch (error) {
              console.error("Failed to upload screenshot:", error);
            }
          }
          isProcessingScreenshot.current = false;
        },
        "image/png",
        0.8
      );
    } catch (error) {
      console.error("Failed to capture screenshot:", error);
      isProcessingScreenshot.current = false;
    }
  };

  const startScreenshotCapture = () => {
    gameSessionId.current = generateUUID();
    screenshotKeys.current = [];

    if (screenshotInterval.current) {
      clearInterval(screenshotInterval.current);
    }

    // Start capturing immediately, then every 10 seconds
    captureScreenshot();
    screenshotInterval.current = setInterval(() => {
      captureScreenshot();
    }, 10000);
  };

  const stopScreenshotCapture = () => {
    if (screenshotInterval.current) {
      clearInterval(screenshotInterval.current);
      screenshotInterval.current = null;
    }
  };

  useEffect(() => {
    if (screens.sm || screens.xs) {
      setIsModalOpen(true);
    }
  }, [screens]);

  let topScoresDrawn = false;

  useEffect(() => {
    if (gameData && ctx) {
      drawTopScoresTopLeft();
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gameData]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      if (gameData && ctx && !topScoresDrawn && gameState === "start") {
        drawTopScoresTopLeft();
      }
    }, 1000);

    return () => clearInterval(intervalId);
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gameData]);

  const handleOk = () => {
    setIsModalOpen(false);
    window.history.back();
  };

  const saveScore = async () => {
    try {
      await RESTPost("/api/upload-game-score", {
        email: getCurrentEmployeesData?.email,
        score: score,
        startTime: gameStartTimeRef.current,
        endTime: new Date().toISOString(),
      });
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    window.addEventListener("resize", updateCanvasSize);

    return () => {
      // Clear the fullscreen flag when game component unmounts
      window.removeEventListener("resize", updateCanvasSize);

      document.removeEventListener("mousedown", mousedownHandler);
      document.removeEventListener("mouseup", mouseupHandler);
      document.removeEventListener("keydown", keydownHandler);
      document.removeEventListener("keyup", keyupHandler);

      stopScreenshotCapture();
    };
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const fetchGameData = async () => {
      try {
        const gameData = await getGameScores();
        if (gameData) {
          setGameData(gameData);
        }
      } catch (error) {
        console.log(error);
      }
    };

    fetchGameData();
  }, []);
  useEffect(() => {
    //eslint-disable-next-line react-hooks/exhaustive-deps
    cnv = document.getElementById("my-canvas");

    if (cnv) {
      cnv.width = window.innerWidth;
      cnv.height = window.innerHeight - 64;
      ctx = cnv.getContext("2d");

      if (gameData) {
        drawTopScoresTopLeft();
      }
      resizeObserver.current = new ResizeObserver(updateCanvasSize);

      resizeObserver.current.observe(document.documentElement);
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  function updateCanvasSize() {
    if (!cnv) return;

    const viewportWidth = document.documentElement.clientWidth;
    const viewportHeight = document.documentElement.clientHeight;

    cnv.width = viewportWidth;
    cnv.height = viewportHeight;

    const wallHeightRatio = 0.4;
    const newWallHeight = cnv.height * wallHeightRatio;
    const wallWidth = cnv.width * 0.02;

    if (wall1) {
      wall1.h = newWallHeight;
      wall1.w = wallWidth;
    }
    if (wall2) {
      wall2.h = newWallHeight;
      wall2.w = wallWidth;
    }
    if (wall3) {
      wall3.h = newWallHeight;
      wall3.w = wallWidth;
    }

    if (heli) {
      const heliWidthRatio = 0.05;
      const heliHeightRatio = 0.05;
      heli.w = cnv.width * heliWidthRatio;
      heli.h = cnv.height * heliHeightRatio;
    }

    if (ctx) drawMainComponents();
  }

  let mouseIsPressed = false;
  let keyIsPressed = false;

  let heli;
  let wall1, wall2, wall3;
  let score;
  reset();

  useEffect(() => {
    // Draw Function
    setTimeout(() => {
      draw();
    }, 1000);
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [heli.x]);

  function draw() {
    const canvas = document.getElementById("my-canvas");
    if (!canvas) {
      console.error("Canvas element not found in draw function");
      return;
    }

    const ctx = canvas.getContext("2d");
    if (!ctx) {
      console.error("Could not get 2D context in draw function");
      return;
    }

    // Clear the entire canvas with transparent pixels before each draw
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (state === "start") {
      drawStart();
      // Try to draw scores in start screen
      if (gameData && ctx && !topScoresDrawn) {
        drawTopScoresTopLeft();
      }
    } else if (state === "gameon") {
      runGame();
    } else if (state === "gameover") {
      drawGameOver();
    }

    // Request Animation Frame
    requestAnimationFrame(draw);
  }

  // EVENT STUFF
  document.addEventListener("mousedown", mousedownHandler);
  document.addEventListener("mouseup", mouseupHandler);

  document.addEventListener("keydown", (e) => keydownHandler(e));
  document.addEventListener("keyup", (e) => keyupHandler(e));

  function startGame() {
    if (state === "start") {
      setIsButtonLoading(true);

      const showCountdown = (number) => {
        message.success({
          content: ` Get ready! Game starting in ${number}...`,
          duration: 0.8,
          icon: (
            <span role="img" aria-label="countdown">
              {number === 3 ? "🚁" : number === 2 ? "✈️" : "🚀"}
            </span>
          ),
          style: {
            marginTop: "20px",
            fontSize: "20px",
            fontWeight: "bold",
          },
        });
      };
      showCountdown(3);
      setTimeout(() => showCountdown(2), 900);
      setTimeout(() => showCountdown(1), 1800);
      setTimeout(async () => {
        cnv = document.getElementById("my-canvas");
        cnv.width = window.innerWidth;
        cnv.height = window.innerHeight - 64;
        ctx = cnv.getContext("2d");
        if (!cnv) {
          console.error("Canvas element not found");
          setIsButtonLoading(false);
          return;
        }

        ctx = cnv.getContext("2d");
        if (!ctx) {
          console.error("Could not get 2D context from canvas");
          setIsButtonLoading(false);
          return;
        }
        gameStartTimeRef.current = new Date().toISOString();

        // Update game state and start the game
        setGameState("gameon");
        state = "gameon";
        draw();

        // Start capturing screenshots
        await startScreenshotCapture();
      }, 2000);
    }
  }

  function mousedownHandler() {
    mouseIsPressed = true;
  }

  function mouseupHandler() {
    mouseIsPressed = false;
  }

  function keydownHandler(e) {
    if (e.which === 32) {
      keyIsPressed = true;
    }
  }

  function keyupHandler(e) {
    if (e.which === 32) {
      keyIsPressed = false;
    }
  }

  // FUNCTIONS
  // Draw Start Screen
  function runGame() {
    //Logic
    moveHeli();
    moveWalls();
    checkCollisions();

    //Draw
    drawGame();
  }

  function moveHeli() {
    // Accelerte upward if mouse pressed
    if (mouseIsPressed || keyIsPressed) {
      heli.speed += -1;
    }

    // Apply gravity (accel)
    heli.speed += heli.accel;

    // Constrain speed (max/min)
    if (heli.speed > 5) {
      heli.speed = 5;
    } else if (heli.speed < -5) {
      heli.speed = -5;
    }

    // Move Helicopter by its speed
    heli.y += heli.speed;
    heli.x =
      state === "gameon"
        ? screens.helicopterPosition
          ? 100
          : 300
        : screens.helicopterPosition
        ? 350
        : 650;
  }

  function moveWalls() {
    const canvasWidth = cnv.width;
    const canvasHeight = cnv.height;
    const wallSpacing = canvasWidth * 0.5;

    // Wall 1
    if (score > 20) {
      wall1.x += -8;
    } else {
      wall1.x += -5;
    }

    if (wall1.x + wall1.w < 0) {
      wall1.x = wall3.x + wallSpacing;
      wall1.y = Math.random() * (canvasHeight * 0.4) + canvasHeight * 0.05;
      score = score + 1;
    }

    // Wall 2
    if (score > 20) {
      wall2.x += -8;
    } else {
      wall2.x += -5;
    }
    if (wall2.x + wall2.w < 0) {
      wall2.x = wall1.x + wallSpacing;
      wall2.y = Math.random() * (canvasHeight * 0.4) + canvasHeight * 0.05;
      score = score + 1;
    }

    // Wall 3
    if (score > 20) {
      wall3.x += -8;
    } else {
      wall3.x += -5;
    }
    if (wall3.x + wall3.w < 0) {
      wall3.x = wall2.x + wallSpacing;
      // Use proportional positioning for Y coordinate
      wall3.y = Math.random() * (canvasHeight * 0.4) + canvasHeight * 0.05;
      score = score + 1;
    }
  }

  let blinkTimer = 0;

  function drawStart() {
    drawMainComponents();

    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;

    // Main title
    ctx.textAlign = "center";
    ctx.fillStyle = "#083443";
    ctx.font = screens.xs ? "48px angled" : "64px angled";
    ctx.shadowColor = "rgba(0, 0, 0, 0.3)";
    ctx.shadowBlur = 5;
    ctx.shadowOffsetX = 2;
    ctx.shadowOffsetY = 4;
    ctx.fillText("HELICOPTER GAME", centerX, centerY - 180);

    //Best Score
    // Display highest score of current employee
    const currentEmployeeEmail = getCurrentEmployeesData?.email;
    let highestScore = 0;

    if (gameData && currentEmployeeEmail) {
      const employeeData = gameData[currentEmployeeEmail];
      if (employeeData && employeeData.maxScore) {
        highestScore = employeeData.maxScore;
      }
    }
    if (highestScore > 0) {
      ctx.font = screens.xs ? "16px angled" : "26px angled";
      ctx.fillStyle = "#083443";
      ctx.textAlign = "right";

      // instead of window.innerWidth or cnv.width, use the real, on‐screen width:
      const canvasWidth = cnv?.getBoundingClientRect().width || 0;
      const topMargin = 80;
      const rightMargin = 320;

      ctx.fillText(
        `Your Best: ${highestScore}`,
        canvasWidth - rightMargin,
        topMargin
      );
    }

    // Instructions container background
    const instructionsX = centerX - 250;
    const instructionsY = centerY - 120;
    const instructionsWidth = 500;
    const instructionsHeight = 200;

    // Semi-transparent background for instructions
    ctx.fillStyle = "rgba(255, 255, 255, 0.9)";
    ctx.fillRect(
      instructionsX,
      instructionsY,
      instructionsWidth,
      instructionsHeight
    );

    // Border for instructions box
    ctx.strokeStyle = "#083443";
    ctx.lineWidth = 2;
    ctx.strokeRect(
      instructionsX,
      instructionsY,
      instructionsWidth,
      instructionsHeight
    );

    // "HOW TO PLAY" header
    ctx.font = screens.xs ? "28px angled" : "36px angled";
    ctx.fillStyle = "#083443";
    ctx.fontWeight = "bold";
    ctx.shadowColor = "rgba(0, 0, 0, 0.2)";
    ctx.shadowBlur = 2;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 2;
    ctx.textAlign = "center";
    ctx.fillText("HOW TO PLAY", centerX, instructionsY + 40);

    // Reset shadow for instructions text
    ctx.shadowColor = "transparent";
    ctx.font = screens.xs ? "18px space" : "22px space";
    ctx.fillStyle = "#333333";
    ctx.fontWeight = "normal";
    ctx.textAlign = "left";

    // Mouse controls
    ctx.fillText(
      "🖱️  Click and hold left mouse button to go up",
      instructionsX + 30,
      instructionsY + 80
    );
    ctx.fillText(
      "      Release to go down",
      instructionsX + 30,
      instructionsY + 105
    );

    // Keyboard controls
    ctx.fillText(
      "⌨️  Hold SPACEBAR to go up",
      instructionsX + 30,
      instructionsY + 140
    );
    ctx.fillText(
      "      Release to go down",
      instructionsX + 30,
      instructionsY + 165
    );

    // Start instruction at bottom
    ctx.font = screens.xs ? "20px angled" : "24px angled";
    ctx.fillStyle = "#FF0000";
    ctx.fontWeight = "bold";
    ctx.shadowColor = "rgba(0, 0, 0, 0.3)";
    ctx.shadowBlur = 3;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 2;
    ctx.textAlign = "center";
    blinkTimer++;
    if (Math.floor(blinkTimer / 45) % 2 === 0) {
      ctx.fillText(
        "CLICK START GAME TO BEGIN",
        centerX,
        window.innerHeight - 150
      );
    }
  }

  function checkCollisions() {
    // Collision with Top and Bottom bars
    if (heli.y < 0) {
      gameOver();
    } else if (heli.y + heli.h > cnv?.height - 10) {
      gameOver();
    }

    // Collisions with walls
    if (
      wallCollision(heli, wall1) ||
      wallCollision(heli, wall2) ||
      wallCollision(heli, wall3)
    ) {
      gameOver();
    }
  }

  function wallCollision(heli, wall1) {
    var x1 = heli.x;
    var y1 = heli.y;
    var h1 = heli.h;
    var w1 = heli.w;
    var b1 = y1 + h1;
    var r1 = x1 + w1;

    var x2 = wall1.x;
    var y2 = wall1.y;
    var h2 = wall1.h;
    var w2 = wall1.w;
    var b2 = y2 + h2;
    var r2 = x2 + w2;

    if (b1 < y2 || y1 > b2 || r1 < x2 || x1 > r2) {
      return false;
    } else {
      return true;
    }
  }

  function gameOver() {
    setGameState("gameover");
    state = "gameover";
    saveScore();

    // Stop capturing screenshots
    stopScreenshotCapture();
    setIsButtonLoading(false);

    setTimeout(() => {
      reset();
      setGameState("start");
    }, 4000);
  }

  function reset() {
    state = "start";
    const canvasWidth = cnv?.width || window.innerWidth;
    const canvasHeight = cnv?.height || window.innerHeight - 64;
    const wallSpacing = canvasWidth * 0.5; // Consistent with moveWalls

    heli = {
      x: screens.helicopterPosition
        ? canvasWidth / 2 - 70
        : canvasWidth / 2 - 50,
      y: canvasHeight * 0.2,
      w: canvasWidth * 0.08,
      h: canvasHeight * 0.08,
      speed: 0,
      accel: 0.7,
    };

    const wallWidth = canvasWidth * 0.02;
    const wallHeight = canvasHeight * 0.4;

    wall1 = {
      x: canvasWidth + wallWidth * 2,
      y: Math.random() * (canvasHeight * 0.4) + canvasHeight * 0.05,
      w: wallWidth,
      h: wallHeight,
    };

    wall2 = {
      x: canvasWidth + wallSpacing + wallWidth * 2,
      y: Math.random() * (canvasHeight * 0.4) + canvasHeight * 0.05,
      w: wallWidth,
      h: wallHeight,
    };

    wall3 = {
      x: canvasWidth + wallSpacing * 2 + wallWidth * 2,
      y: Math.random() * (canvasHeight * 0.4) + canvasHeight * 0.05,
      w: wallWidth,
      h: wallHeight,
    };

    score = 0;
  }

  // Draw Game Elements
  function drawGame() {
    drawMainComponents();
    drawWalls();
  }

  // Draw Game Over Screen
  function drawGameOver() {
    drawMainComponents();
    drawWalls();

    // Circle around Helicopter
    ctx.strokeStyle = "red";
    ctx.lineWidth = 5;
    ctx.beginPath();
    ctx.arc(heli.x + heli.w / 2, heli.y + heli.h / 2, 80, 0, 2 * Math.PI);
    ctx.stroke();

    // Game Over Text
    ctx.font = "40px angled";
    ctx.fillStyle = "red";
    ctx.textAlign = "center";
    ctx.shadowColor = "rgba(0, 0, 0, 0.25)";
    ctx.shadowBlur = 3;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 3;
    ctx.fillText("GAME OVER", window.innerWidth / 2, 90);

    // document.addEventListener("keydown", (e) => keydownHandler(e));
    // document.addEventListener("mousedown", mousedownHandler);
  }

  // Helper functions
  function drawWalls() {
    // Draw Walls
    ctx.fillStyle = "#083442";
    ctx.fillRect(wall1.x, wall1.y, wall1.w, wall1.h);
    ctx.fillRect(wall2.x, wall2.y, wall2.w, wall2.h);
    ctx.fillRect(wall3.x, wall3.y, wall3.w, wall3.h);
  }

  // Global Variables (Once)
  // Helicopter GIF
  let heliImg = document.createElement("img");

  // Canvas Background Image
  // let background = document.createElement("img");
  // background.src = bgImage;
  function drawTopScoresTopLeft() {
    if (!ctx) {
      return;
    }

    if (!gameData) {
      return;
    }

    try {
      const containerWidth = 280;
      const containerHeight = 130;

      // Position in top-left corner with some margin
      const startX = 20;
      const startY = 20;

      // Create a more attractive background with rounded corners effect
      ctx.fillStyle = "rgba(8, 52, 67, 0.95)";
      ctx.fillRect(startX, startY, containerWidth, containerHeight);

      // Add a border
      ctx.strokeStyle = "#5AACC9";
      ctx.lineWidth = 3;
      ctx.strokeRect(startX, startY, containerWidth, containerHeight);

      // Add inner border for more depth
      ctx.strokeStyle = "#FFD700";
      ctx.lineWidth = 1;
      ctx.strokeRect(
        startX + 5,
        startY + 5,
        containerWidth - 10,
        containerHeight - 10
      );

      ctx.font = screens.xs ? "18px angled" : "20px angled";
      ctx.fillStyle = "#FFD700";
      ctx.textAlign = "center";
      ctx.shadowColor = "rgba(0, 0, 0, 0.8)";
      ctx.shadowBlur = 2;
      ctx.shadowOffsetX = 1;
      ctx.shadowOffsetY = 1;
      ctx.fillText("🏆 TOP 3 SCORES", startX + containerWidth / 2, startY + 25);

      // Add decorative line under title
      ctx.strokeStyle = "#FFD700";
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(startX + 20, startY + 35);
      ctx.lineTo(startX + containerWidth - 20, startY + 35);
      ctx.stroke();

      // Reset shadow
      ctx.shadowBlur = 0;
      ctx.shadowColor = "transparent";

      // Convert object to array and sort by maxScore
      const scoresArray = [];
      for (const email in gameData) {
        if (Object.prototype.hasOwnProperty.call(gameData, email)) {
          scoresArray.push({
            email,
            ...gameData[email],
          });
        }
      }

      // Sort by maxScore in descending order
      scoresArray.sort((a, b) => b.maxScore - a.maxScore);

      // Draw top 3 scores with better formatting
      if (scoresArray.length > 0) {
        scoresArray.slice(0, 3).forEach((scoreData, index) => {
          const yPosition = startY + 55 + index * 30;
          const medals = ["🥇", "🥈", "🥉"];
          const medalColors = ["#FFD700", "#C0C0C0", "#CD7F32"];

          ctx.fillStyle = "rgba(255, 255, 255, 0.1)";
          ctx.fillRect(startX + 10, yPosition - 18, containerWidth - 20, 25);

          // Medal
          ctx.font = screens.xs ? "16px space" : "18px space";
          ctx.fillStyle = medalColors[index];
          ctx.textAlign = "left";
          ctx.fillText(medals[index], startX + 15, yPosition);

          ctx.font = screens.xs ? "14px space" : "16px space";
          ctx.fillStyle = "#FFFFFF";
          const playerName =
            scoreData.firstName + " " + scoreData.lastName ||
            scoreData.email?.split("@")[0] ||
            "Anonymous";
          const displayName =
            playerName.length > 12
              ? playerName.substring(0, 12) + "..."
              : playerName;
          ctx.fillText(displayName, startX + 40, yPosition);

          ctx.font = screens.xs ? "14px space" : "16px space";
          ctx.fillStyle = "#00FF88";
          ctx.textAlign = "right";
          ctx.fillText(
            `${scoreData.maxScore}`,
            startX + containerWidth - 15,
            yPosition
          );

          ctx.font = screens.xs ? "12px space" : "14px space";
          ctx.fillStyle = "#CCCCCC";
          ctx.textAlign = "left";
          // ctx.fillText(`#${index + 1}`, startX + 15, yPosition - 5);
        });
      } else {
        ctx.font = screens.xs ? "14px space" : "16px space";
        ctx.fillStyle = "#FFFFFF";
        ctx.textAlign = "center";
        ctx.fillText(
          "No scores yet!",
          startX + containerWidth / 2,
          startY + 100
        );

        ctx.font = screens.xs ? "12px space" : "14px space";
        ctx.fillStyle = "#CCCCCC";
        ctx.fillText(
          "Be the first to score!",
          startX + containerWidth / 2,
          startY + 120
        );
      }
    } catch (error) {
      console.error("Error in drawTopScoresTopLeft:", error);
    }
  }

  function drawMainComponents() {
    if (!ctx) {
      const canvas = document.getElementById("my-canvas");
      if (!canvas) {
        console.error("Canvas element not found in drawMainComponents");
        return;
      }
      ctx = canvas.getContext("2d");
      if (!ctx) {
        console.error("Could not get 2D context in drawMainComponents");
        return;
      }
    }

    ctx.strokeStyle = "#000000";
    ctx.lineWidth = 1;
    if (state === "gameon" || state === "gameover") {
      ctx.beginPath();
      ctx.moveTo(0, 0);
      ctx.lineTo(cnv.width, 0);
      ctx.stroke();
      ctx.beginPath();
      ctx.moveTo(0, cnv.height - cnv.height * 0.01); // 10% from bottom
      ctx.lineTo(cnv.width, cnv.height - cnv.height * 0.01);
      ctx.stroke();
    }
    ctx.font = "30px angled";
    ctx.fillStyle = "black";

    if (state === "gameon" || state === "gameover") {
      // Blue Bar Text
      ctx.font = `${Math.max(20, cnv.width * 0.02)}px angled`;
      ctx.fillStyle = "black";
      ctx.fillText(
        `SCORE : ${score}`,
        cnv.width - cnv.width * 0.2,
        cnv.height * 0.05
      );
      heliImg.src = HelicopterIcon;
      ctx.drawImage(heliImg, heli.x, heli.y, heli.w, heli.h);
      // Draw top scores in top left corner during gameplay
      if (gameData && gameData.length > 0) {
        // drawTopScoresTopLeft();
      }
    }

    if (gameState === "gameon" || gameState === "gameover") {
    }
  }

  const navigate = useNavigate();
  return (
    <>
      <Suspense fallback={<Spin />}>
        <div
          id="game-container"
          className="fixed top-0 left-0 w-full h-full !bg-white z-60"
        >
          <Background />

          {/* Canvas on top of the background */}
          <canvas
            id="my-canvas"
            style={{
              position: "absolute",
              top: 0,
              left: 0,
            }}
            className=" h-full w-full "
          ></canvas>
          {gameState === "start" && (
            <div className="absolute top-[70%] left-[50%] transform -translate-x-1/2 -translate-y-1/2 z-[3] -mt-[10px] -ml-[25px]">
              <Button
                type="primary"
                size="large"
                onClick={startGame}
                loading={isButtonLoading}
              >
                START GAME
              </Button>
            </div>
          )}
          <div className="absolute top-20 right-5 transform -translate-x-1/2 -translate-y-1/2 z-[3] text-5xl cursor-pointer">
            <CloseOutlined onClick={() => navigate(-1)} />
          </div>
        </div>
      </Suspense>

      <Modal
        visible={isModalOpen}
        onSubmit={handleOk}
        onCancel={false}
        width="100%"
      >
        <Row justify="center">
          <img
            src={NotSupportedIcon}
            alt="delete"
            style={{ display: "flex", margin: "15px auto" }}
          />
          <Typography.Title level={4} className="text-center space">
            This game is not supported in the device you are currently using.
            Please choose a different device.
          </Typography.Title>
        </Row>
      </Modal>
    </>
  );
}
