import { useLayoutEffect, useState } from "react";

const useCustomBreakpointsForGame = () => {
  const [dimensions, setDimensions] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  useLayoutEffect(() => {
    function updateDimensions() {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }
    window.addEventListener("resize", updateDimensions);
    updateDimensions();
    return () => window.removeEventListener("resize", updateDimensions);
  }, []);

  const size = dimensions.width;

  return {
    SmallMobile: size <= 320,
    Mobile: size > 320 && size <= 480,
    Tab: size > 480 && size <= 600,
    LargeTab: size > 600 && size <= 768,
    Laptop: size > 768 && size <= 992,
    LargeLaptop: size > 900 && size <= 1024,
    Desktop: size > 1024 && size <= 1200,
    LargeDesktop: size > 1200,

    helicopterPosition: size < 850,

    isMobile: size <= 480,
    isTab: size > 480 && size <= 768,
    isDesktop: size > 768,
    isSmallDevice: size <= 768,

    // Add responsive sizing helpers
    containerWidth: `${Math.min(size, 1200)}px`,
    gameWidth: `${Math.min(size * 0.95, 1100)}px`,
    contentWidth: `${Math.min(size - 80, 1180)}px`,

    // Add viewport dimensions
    viewportWidth: dimensions.width,
    viewportHeight: dimensions.height,

    // Add scaling factor for responsive elements
    scale: Math.max(0.5, Math.min(1, size / 1200)),
  };
};

export default useCustomBreakpointsForGame;
