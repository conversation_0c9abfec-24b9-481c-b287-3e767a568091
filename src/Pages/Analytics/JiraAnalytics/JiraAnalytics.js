import React, { useState, useEffect, useCallback, useMemo } from "react";
import { Card, Row, Col, Button, Spin, Table, Avatar } from "antd";
import { API, Storage } from "aws-amplify";
import { Pie, Column } from "@ant-design/plots";
import { ReloadOutlined, UserOutlined } from "@ant-design/icons";
import Loader from "Commons/Loader";
import moment from "moment";
import AnalyticsCard from "Pages/Analytics/Components/AnalyticsCard";
import { DateFormatWithTime } from "utils/constants";
import { boxClass } from "utils/TailwindCommonClasses";
import AnalyticsHeader from "../Components/AnalyticsHeader";
import { JiraAnalyticsPath } from "Pages/Hiring/Constants/Constants";
import { ExecuteQueryCustomV2 } from "utils/Api";
import { getEmployeeDataForCustomProfile } from "graphql/customQueries";

export default function JiraAnalytics() {
  const [isLoading, setIsLoading] = useState(true);
  const [jiraData, setJiraData] = useState({});
  const [lastFetched, setLastFetched] = useState(null);
  const [employeeDetails, setEmployeeDetails] = useState({});
  const [profilePics, setProfilePics] = useState({});

  const handleFetchJiraData = useCallback(async () => {
    try {
      setIsLoading(true);
      const url = await Storage.get(JiraAnalyticsPath, {
        level: "public",
      });
      const response = await fetch(url);
      if (response?.status === 404) {
        console.log("error");
        return;
      } else {
        const data = await response.json();
        setLastFetched(data.generated_at);
        let analyticsData = data?.data ?? {};

        // If the data is an array (old format), aggregate it to the expected object format
        if (Array.isArray(analyticsData)) {
          // Aggregate issueTypesData
          const issueTypeCounts = analyticsData.reduce((acc, emp) => {
            if (emp.issueTypeDistribution) {
              Object.entries(emp.issueTypeDistribution).forEach(
                ([type, count]) => {
                  acc[type] = (acc[type] || 0) + count;
                }
              );
            }
            return acc;
          }, {});
          const issueTypesData = Object.entries(issueTypeCounts).map(
            ([type, count]) => ({ type, count })
          );

          // Aggregate issue types per project
          const projectIssueTypes = analyticsData.reduce((acc, emp) => {
            if (emp.projectId && emp.issueTypeDistribution) {
              if (!acc[emp.projectId]) {
                acc[emp.projectId] = {
                  projectId: emp.projectId,
                  projectName: emp.projectName || "Unknown Project",
                  issueTypes: {},
                };
              }
              Object.entries(emp.issueTypeDistribution).forEach(
                ([type, count]) => {
                  acc[emp.projectId].issueTypes[type] =
                    (acc[emp.projectId].issueTypes[type] || 0) + count;
                }
              );
            }
            return acc;
          }, {});

          const projectIssueTypesData = Object.values(projectIssueTypes).map(
            (project) => ({
              projectId: project.projectId,
              projectName: project.projectName,
              ...project.issueTypes,
            })
          );

          // Aggregate summary metrics
          const totalStories = analyticsData.reduce(
            (sum, emp) => sum + (emp.totalStories || 0),
            0
          );
          const totalBugs = analyticsData.reduce(
            (sum, emp) => sum + (emp.totalBugs || 0),
            0
          );

          analyticsData = {
            totalStories,
            totalBugs,
            issueTypesData,
            projectIssueTypesData,
            employeeAnalytics: analyticsData,
          };
        }
        setJiraData(analyticsData);

        // Fetch employee details for each employee in the analytics data
        const employeeEmails =
          analyticsData.employeeAnalytics
            ?.filter((emp) => emp.email) // Filter out null/undefined emails
            ?.map((emp) => emp.email) || [];

        const employeeDetailsPromises = employeeEmails.map((email) =>
          ExecuteQueryCustomV2(getEmployeeDataForCustomProfile, { email })
            .then((data) => ({ email, data }))
            .catch((error) => {
              console.error(
                `Error fetching details for employee ${email}:`,
                error
              );
              return { email, data: null };
            })
        );

        const employeeDetailsResults = await Promise.all(
          employeeDetailsPromises
        );
        const employeeDetailsMap = employeeDetailsResults.reduce(
          (acc, { email, data }) => {
            acc[email] = data;
            return acc;
          },
          {}
        );
        setEmployeeDetails(employeeDetailsMap);

        // Fetch profile pictures for employees
        const profilePicPromises = employeeDetailsResults
          .filter(({ data }) => data?.profile_pic)
          .map(({ email, data }) =>
            Storage.get(data.profile_pic)
              .then((url) => ({ email, url }))
              .catch((error) => {
                console.error(
                  `Error fetching profile picture for ${email}:`,
                  error
                );
                return { email, url: null };
              })
          );

        const profilePicResults = await Promise.all(profilePicPromises);
        const profilePicMap = profilePicResults.reduce(
          (acc, { email, url }) => {
            acc[email] = url;
            return acc;
          },
          {}
        );
        setProfilePics(profilePicMap);
      }
    } catch (error) {
      console.error("Error fetching Jira analytics data", error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleRefreshReportData = useCallback(async () => {
    try {
      setIsLoading(true);
      await API.get("RestHRMS", "/reports/jira-analytics");
      handleFetchJiraData();
    } catch (error) {
      console.log(error);
    }
  }, [handleFetchJiraData]);

  useEffect(() => {
    handleFetchJiraData();
  }, [handleFetchJiraData]);

  // Memoized Chart Configurations
  const issueTypePieChartConfig = useMemo(
    () => ({
      data: jiraData?.issueTypesData ?? [],
      angleField: "count",
      colorField: "type",
      radius: 0.9,
      label: {
        type: "inner",
        offset: "-30%",
        content: "{name}",
        style: { fontSize: 10, textAlign: "center" },
      },
      interactions: [{ type: "element-active" }],
      legend: { position: "bottom", offsetY: 10 },
    }),
    [jiraData]
  );

  const projectIssueTypesChartConfig = useMemo(
    () => ({
      data: jiraData?.projectIssueTypesData ?? [],
      xField: "projectName",
      yField: "value",
      seriesField: "type",
      isGroup: true,
      columnStyle: {
        radius: [20, 20, 0, 0],
      },
      label: {
        position: "middle",
        layout: [
          { type: "interval-adjust-position" },
          { type: "interval-hide-overlap" },
          { type: "adjust-color" },
        ],
      },
      interactions: [{ type: "active-region", enable: false }],
      xAxis: {
        title: { text: "Project" },
        label: {
          autoRotate: true,
          autoHide: false,
        },
      },
      yAxis: { title: { text: "Number of Issues" } },
      legend: { position: "bottom", offsetY: 10 },
    }),
    [jiraData]
  );

  // Prepare data for the stories by type per employee table
  const storiesByTypeTableData = useMemo(() => {
    if (!jiraData?.employeeAnalytics) return [];

    return jiraData.employeeAnalytics
      .filter((emp) => emp.email) // Filter out null/undefined emails
      .map((emp) => {
        const issueTypes = emp.issueTypeDistribution || {};
        const employeeDetail = employeeDetails[emp.email];
        const typeCounts = Object.fromEntries(
          Object.entries(issueTypes).map(([type, count]) => [
            type.toLowerCase(),
            count,
          ])
        );

        // Calculate total as sum of all issue types
        const total = Object.values(typeCounts).reduce(
          (sum, count) => sum + (count || 0),
          0
        );

        return {
          key: emp.email,
          employee: emp.email,
          employeeName: employeeDetail
            ? `${employeeDetail.first_name} ${employeeDetail.last_name}`
            : "Unknown",
          employeeTitle: employeeDetail?.title?.name || "N/A",
          employeeAvatar: profilePics[emp.email],
          ...typeCounts,
          total,
        };
      })
      .filter((item) => item.employeeName !== "Unknown") // Filter out Unknown employees
      .sort((a, b) => b.total - a.total); // Sort by total in descending order
  }, [jiraData, employeeDetails, profilePics]);

  // Define columns for the stories by type table
  const storiesByTypeColumns = useMemo(() => {
    const baseColumns = [
      {
        title: "Employee",
        dataIndex: "employee",
        key: "employee",
        fixed: "left",
        width: 250,
        render: (_, record) => {
          const emailWithoutDomain = record.employee.split("@")[0];
          const profileUrl = `https://hub.york.ie/employee-view/${emailWithoutDomain}`;

          return (
            <div className="flex items-center gap-2">
              <Avatar
                src={record.employeeAvatar}
                icon={<UserOutlined />}
                size="small"
              />
              <div className="flex flex-col">
                <a
                  href={profileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-medium hover:text-blue-600"
                >
                  {record.employeeName}
                </a>
                <span className="text-xs text-gray-500">
                  {record.employeeTitle}
                </span>
              </div>
            </div>
          );
        },
      },
    ];

    // Add dynamic columns for each issue type
    const issueTypes = ["epic", "story", "task", "bug", "subtask", "other"];
    issueTypes.forEach((type) => {
      baseColumns.push({
        title: type.charAt(0).toUpperCase() + type.slice(1),
        dataIndex: type,
        key: type,
        width: 100,
        render: (value) => value || 0,
      });
    });

    // Add total column
    baseColumns.push({
      title: "Total",
      dataIndex: "total",
      key: "total",
      fixed: "right",
      width: 100,
      sorter: (a, b) => a.total - b.total,
      defaultSortOrder: "descend",
    });

    return baseColumns;
  }, []);

  if (isLoading) {
    return <Loader />;
  }

  return (
    <div className={boxClass}>
      <AnalyticsHeader
        title="Jira Analytics"
        lastFetched={lastFetched}
        onRefresh={handleRefreshReportData}
        refreshIcon={<ReloadOutlined />}
      />

      <Row gutter={[16, 16]}>
        {/* Issue Types Distribution */}
        <Col xs={24} lg={12}>
          <Card title="Issue Types Distribution">
            <Pie {...issueTypePieChartConfig} />
          </Card>
        </Col>

        {/* Issue Types per Project */}
        <Col xs={24} lg={12}>
          <Card title="Issue Types per Project">
            <Column {...projectIssueTypesChartConfig} />
          </Card>
        </Col>

        {/* Stories by Type per Employee Table */}
        <Col xs={24}>
          <Card title="Stories by Type per Employee">
            <Table
              dataSource={storiesByTypeTableData}
              columns={storiesByTypeColumns}
              scroll={{ x: "max-content" }}
              pagination={{ pageSize: 10 }}
              size="middle"
            />
          </Card>
        </Col>

        {/* Summary Cards */}
        <Col xs={24} sm={12} md={6}>
          <AnalyticsCard
            title="Total Stories"
            value={jiraData?.totalStories ?? 0}
          />
        </Col>
        <Col xs={24} sm={12} md={6}>
          <AnalyticsCard title="Total Bugs" value={jiraData?.totalBugs ?? 0} />
        </Col>
      </Row>
    </div>
  );
}
