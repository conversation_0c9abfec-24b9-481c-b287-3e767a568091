import React from "react";
import { Card, Divider, Tag, Typography, Space } from "antd";
import {
  CheckCircleFilled,
  CloseCircleFilled,
  UserOutlined,
  RobotOutlined,
} from "@ant-design/icons";
import { boxClass } from "utils/TailwindCommonClasses";

const { Title, Text, Paragraph } = Typography;

const InterviewFeedbackCard = ({ aiFeedback, candidateFeedback }) => {
  const hasAIFeedback =
    aiFeedback && (aiFeedback.candidate || aiFeedback.interviewer);

  const hasInterviewerFeedback =
    candidateFeedback && Object.keys(candidateFeedback).length > 0;

  if (!hasAIFeedback && !hasInterviewerFeedback) return null;

  // Parse feedback data
  const parseData = (data) => {
    if (!data) return null;
    if (typeof data === "object") return data;
    return data;
  };

  const aiCandidateData = parseData(aiFeedback?.candidate);
  const aiInterviewerData = parseData(aiFeedback?.interviewer);
  const interviewerFeedback = parseData(candidateFeedback);
  const candidateFeedbackBlock = parseData(aiFeedback?.candidateFeedback);

  // Helper to check if candidate feedback block is available
  const hasCandidateFeedbackBlock =
    candidateFeedbackBlock && Object.keys(candidateFeedbackBlock).length > 0;

  //check Boolean Field for feedback
  const renderBooleanField = (label, value) => {
    // Convert Yes/No to boolean
    let boolValue = value;
    if (typeof value === "string") {
      boolValue = value.toLowerCase() === "yes";
    }

    return (
      <div className="flex items-center gap-2 mb-2">
        {boolValue === true ? (
          <CheckCircleFilled className="text-green-500" />
        ) : (
          <CloseCircleFilled className="text-red-500" />
        )}
        <Text>{label}</Text>
      </div>
    );
  };

  // Render text field
  const renderTextField = (label, value) => {
    if (!value) return null;
    return (
      <div className="mb-3">
        <Text strong className="block mb-1">
          {label}:
        </Text>
        <Paragraph className="ml-2 text-gray-700">{value}</Paragraph>
      </div>
    );
  };
  //Render Rating field
  const renderRatingField = (label, value) => {
    if (!value && value !== 0) return null;
    const numValue = typeof value === "string" ? parseInt(value) : value;
    return (
      <div className="mb-2">
        <Space>
          <Text strong>{label}:</Text>
          <Tag
            color={numValue >= 3 ? "green" : numValue >= 2 ? "orange" : "red"}
          >
            {numValue}/5
          </Tag>
        </Space>
      </div>
    );
  };
  //Check Hiring Status
  const getHiringStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "hire":
        return "green";
      case "reject":
        return "red";
      case "another_round_needed":
        return "blue";
      default:
        return "default";
    }
  };

  return (
    <div className="flex flex-col md:grid md:grid-cols-2 gap-4 w-full p-3">
      {/* AI Candidate Assessment */}
      {aiCandidateData && (
        <Card
          title={
            <div className="flex items-center gap-2">
              <RobotOutlined />
              <span>AI Candidate Assessment</span>
            </div>
          }
          className={`${boxClass} w-full`}
          bordered
        >
          <div className="mb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 ">
              {aiCandidateData["Technical Skills"] !== undefined &&
                renderBooleanField(
                  "Technical Skills",
                  aiCandidateData["Technical Skills"]
                )}
              {aiCandidateData["Concept Clarity"] !== undefined &&
                renderBooleanField(
                  "Concept Clarity",
                  aiCandidateData["Concept Clarity"]
                )}
              {aiCandidateData["Domain Knowledge"] !== undefined &&
                renderBooleanField(
                  "Domain Knowledge",
                  aiCandidateData["Domain Knowledge"]
                )}
              {aiCandidateData["Good to Hire"] !== undefined &&
                renderBooleanField(
                  "Good to Hire",
                  aiCandidateData["Good to Hire"]
                )}
              {aiCandidateData["Eligible for Next Round"] !== undefined &&
                renderBooleanField(
                  "Eligible for Next Round",
                  aiCandidateData["Eligible for Next Round"]
                )}
            </div>
            <div className="bg-slate-100 p-1 rounded-md">
              {renderTextField(
                "Communication",
                aiCandidateData["Communication"]
              )}
            </div>
            <div className="bg-slate-100 p-1 rounded-md mt-2">
              {renderTextField(
                "Overall Feedback",
                aiCandidateData["Overall Feedback"]
              )}
            </div>
          </div>
        </Card>
      )}
      {/* AI Interviewer Assessment */}
      {aiInterviewerData && (
        <Card
          title={
            <div className="flex items-center gap-2">
              <RobotOutlined />
              <span>AI Interviewer Assessment</span>
            </div>
          }
          className={`${boxClass} w-full`}
          bordered
        >
          <div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4">
              {aiInterviewerData["Confident"] !== undefined &&
                renderBooleanField("Confident", aiInterviewerData["Confident"])}
              {aiInterviewerData["Job Description Considered"] !== undefined &&
                renderBooleanField(
                  "Job Description Considered",
                  aiInterviewerData["Job Description Considered"]
                )}
              {aiInterviewerData["Recommended for Future"] !== undefined &&
                renderBooleanField(
                  "Recommended for Future",
                  aiInterviewerData["Recommended for Future"]
                )}
            </div>
            <div className="bg-slate-100 p-1 rounded-md ">
              {renderTextField(
                "Communication",
                aiInterviewerData["Communication"]
              )}
            </div>
            <div className="bg-slate-100 p-1 rounded-md mt-2">
              {renderTextField("Tone", aiInterviewerData["Tone"])}
            </div>
            <div className="bg-slate-100 p-1 rounded-md mt-2">
              {renderTextField(
                "Healthy Dialog",
                aiInterviewerData["Healthy Dialog"]
              )}
            </div>
            <div className="bg-slate-100 p-1 rounded-md mt-2">
              {renderTextField(
                "Overall Feedback",
                aiInterviewerData["Overall Feedback"]
              )}
            </div>
          </div>
        </Card>
      )}
      {/* Candidate Feedback (feedback given by candidate) */}
      {hasCandidateFeedbackBlock && (
        <Card
          title={
            <div className="flex items-center gap-2">
              <UserOutlined />
              <span>Candidate Feedback</span>
            </div>
          }
          className={`${boxClass} w-full`}
          bordered
        >
          <div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4">
              {candidateFeedbackBlock.profileRelevance !== undefined &&
                renderBooleanField(
                  "Profile Relevance",
                  candidateFeedbackBlock.profileRelevance
                )}
              {candidateFeedbackBlock.companySummary !== undefined &&
                renderBooleanField(
                  "Company Summary",
                  candidateFeedbackBlock.companySummary
                )}
              {candidateFeedbackBlock.interviewerIntro !== undefined &&
                renderBooleanField(
                  "Interviewer Intro",
                  candidateFeedbackBlock.interviewerIntro
                )}
              {candidateFeedbackBlock.comfortLevel !== undefined &&
                renderBooleanField(
                  "Comfort Level",
                  candidateFeedbackBlock.comfortLevel
                )}
            </div>
            <div className="bg-slate-100 p-1 rounded-md mt-2">
              {renderTextField(
                "Additional Comments",
                candidateFeedbackBlock.additionalComments
              )}
            </div>
            <div className="bg-slate-100 p-1 rounded-md mt-2">
              {renderRatingField(
                "Overall Experience",
                candidateFeedbackBlock.overallExperience
              )}
            </div>
            <div className="bg-slate-100 p-1 rounded-md mt-2">
              {renderRatingField(
                "Difficulty Level",
                candidateFeedbackBlock.difficultyLevel
              )}
            </div>
          </div>
        </Card>
      )}
      {/* Interviewer Feedback (feedback given by interviewer) */}
      {interviewerFeedback && Object.keys(interviewerFeedback).length > 0 && (
        <Card
          title={
            <div className="flex items-center gap-2">
              <UserOutlined />
              <span>Interviewer Feedback</span>
            </div>
          }
          className={`${boxClass} w-full`}
          bordered
        >
          <div>
            {interviewerFeedback.hiring_status && (
              <div className="mb-4">
                <Tag
                  color={getHiringStatusColor(
                    interviewerFeedback.hiring_status
                  )}
                  className="text-base py-1 px-3"
                >
                  {interviewerFeedback.hiring_status === "another_round_needed"
                    ? "Another Round Needed"
                    : interviewerFeedback.hiring_status
                        .charAt(0)
                        .toUpperCase() +
                      interviewerFeedback.hiring_status.slice(1)}
                </Tag>
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4">
              {renderBooleanField(
                "Technically Strong",
                interviewerFeedback.technically_strong
              )}
              {renderBooleanField(
                "Concept Clarity",
                interviewerFeedback.concept_clarity
              )}
              {renderBooleanField(
                "Leadership Skills",
                interviewerFeedback.leadership_skills
              )}
              {renderBooleanField(
                "Culture Fit",
                interviewerFeedback.culture_fit
              )}
              {renderBooleanField(
                "Clarity in Answers",
                interviewerFeedback.clarity_in_answers
              )}
            </div>
            <div className="my-3">
              <div className="bg-slate-100 p-1 rounded-md mt-2">
                {renderRatingField(
                  "Domain Knowledge",
                  interviewerFeedback.domain_knowledge
                )}
              </div>
              <div className="bg-slate-100 p-1 rounded-md mt-2">
                {renderRatingField(
                  "Communication",
                  interviewerFeedback.communication_rating
                )}
              </div>
            </div>
            {interviewerFeedback.additional_feedback && (
              <div className="bg-slate-100 p-1 rounded-md mt-2">
                {renderTextField(
                  "Additional Feedback",
                  interviewerFeedback.additional_feedback
                )}
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  );
};

export default InterviewFeedbackCard;
