import { Tooltip } from "antd";

export const renderStatusDot = (
  result,
  tooltipText,
  anotherRoundNeeded = false
) => {
  if (result === null) return null;

  return (
    <Tooltip
      title={`${tooltipText}: ${
        anotherRoundNeeded ? "Another Round Needed" : result ? "Hire" : "Reject"
      }`}
    >
      <div
        className={`w-5 h-5 rounded-full inline-block mx-1 ${
          anotherRoundNeeded
            ? "bg-yellow-500"
            : result
            ? "bg-green-500"
            : "bg-red-500"
        }`}
      />
    </Tooltip>
  );
};
