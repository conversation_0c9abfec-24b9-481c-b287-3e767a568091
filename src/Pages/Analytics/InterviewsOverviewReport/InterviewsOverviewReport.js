import {
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  DragOutlined,
  EditOutlined,
  ExportOutlined,
  ReloadOutlined,
  SendOutlined,
  TeamOutlined,
  UserOutlined,
  CheckCircleFilled,
  CloseCircleFilled,
} from "@ant-design/icons";
import {
  Badge,
  Button,
  Drawer,
  message,
  Modal,
  Tag,
  Tooltip,
  Typography,
  Avatar,
} from "antd";
import TextArea from "antd/lib/input/TextArea";
import { Responsive, WidthProvider } from "react-grid-layout";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";
import CustomTable from "Commons/CustomTable";
import Loader from "Commons/Loader";
import moment from "moment";
import AnalyticsHeader from "Pages/Analytics/Components/AnalyticsHeader";
import {
  getHiringPositionForAnalyticsDataAction,
  listInterviewForAnalyticsDataAction,
} from "Pages/Analytics/reportActions";
import RenderInterviewFeedback from "Pages/Hiring/Components/Atoms/RenderInterviewFeedback";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getInterviewOverviewReportsFromReducer,
  setInterviewOverviewReportsInReducer,
} from "store/slices/analyticsSlice";
import { createGlobalNotification } from "utils/commonMethods";
import { DateFormatWithTime } from "utils/constants";
import { boxClass } from "utils/TailwindCommonClasses";
import { debounce } from "lodash";
import { updateEmployeeCustom } from "graphql/customMutation";
import { ExecuteMutationV2 } from "utils/Api";
import { getEmployeeAction } from "utils/Actions";
import { InterviewStatusColorMap } from "Pages/Hiring/Constants/Constants";
import InterviewFeedbackCard from "Pages/Analytics/InterviewsOverviewReport/components/InterviewFeedbackCard";
import { GetFileFromS3 } from "Pages/Profile/function/uploadFile";

const ResponsiveGridLayout = WidthProvider(Responsive);
const InterviewsOverviewReport = ({
  jobPositionId = "",
  jobPositionTitle = "",
}) => {
  // Redux state
  const dataFromReducer = useSelector(getInterviewOverviewReportsFromReducer);
  const {
    userData: { email },
  } = useSelector((state) => state?.loginReducer);
  const dispatch = useDispatch();

  // Constants for KPI titles
  const INTERVIEW_KPI_TITLES = {
    upcomingInterview: "Upcoming Interview",
    pendingFeedback: "Pending Feedback",
    completedInterview: "Completed Interview",
    totalInterview: "Total Interview",
    acceptanceRatio: "Acceptance Ratio",
    aiAndInterviewFeedback: "AI and Interviewer Feedback",
    interviewStatus: "Interview Status",
  };
  // Default grid layout configuration
  const defaultLayouts = {
    md: [
      {
        static: false,
        w: 3,
        moved: false,
        h: 4,
        x: 0,
        y: 0,
        i: "upcomingInterview",
      },
      {
        static: false,
        w: 3,
        moved: false,
        h: 4,
        x: 3,
        y: 0,
        i: "pendingFeedback",
      },
      {
        static: false,
        w: 3,
        moved: false,
        h: 4,
        x: 6,
        y: 0,
        i: "completedInterview",
      },
      {
        static: false,
        w: 3,
        moved: false,
        h: 4,
        x: 9,
        y: 0,
        i: "totalInterview",
      },
      {
        static: false,
        w: 4,
        moved: false,
        h: 4,
        x: 0,
        y: 4,
        i: "acceptanceRatio",
      },
      {
        static: false,
        w: 4,
        moved: false,
        h: 6,
        x: 0,
        y: 8,
        i: "aiAndInterviewFeedback",
      },
      {
        static: false,
        w: 3,
        moved: false,
        h: 10,
        x: 4,
        y: 4,
        i: "interviewStatus",
      },
    ],
    sm: [
      {
        static: false,
        w: 3,
        moved: false,
        h: 5,
        x: 0,
        y: 0,
        i: "upcomingInterview",
      },
      {
        static: false,
        w: 3,
        moved: false,
        h: 4,
        x: 3,
        y: 0,
        i: "pendingFeedback",
      },
      {
        static: false,
        w: 3,
        moved: false,
        h: 4,
        x: 0,
        y: 5,
        i: "completedInterview",
      },
      {
        static: false,
        w: 3,
        moved: false,
        h: 4,
        x: 3,
        y: 4,
        i: "totalInterview",
      },
      {
        static: false,
        w: 6,
        moved: false,
        h: 4,
        x: 0,
        y: 9,
        i: "acceptanceRatio",
      },
      {
        static: false,
        w: 6,
        moved: false,
        h: 6,
        x: 0,
        y: 13,
        i: "aiAndInterviewFeedback",
      },
      {
        static: false,
        w: 6,
        moved: false,
        h: 8,
        x: 0,
        y: 19,
        i: "interviewStatus",
      },
    ],
    xs: [
      {
        static: false,
        w: 4,
        moved: false,
        h: 4,
        x: 0,
        y: 0,
        i: "upcomingInterview",
      },
      {
        static: false,
        w: 4,
        moved: false,
        h: 4,
        x: 0,
        y: 4,
        i: "pendingFeedback",
      },
      {
        static: false,
        w: 4,
        moved: false,
        h: 4,
        x: 0,
        y: 8,
        i: "completedInterview",
      },
      {
        static: false,
        w: 4,
        moved: false,
        h: 4,
        x: 0,
        y: 12,
        i: "totalInterview",
      },
      {
        static: false,
        w: 4,
        moved: false,
        h: 4,
        x: 0,
        y: 16,
        i: "acceptanceRatio",
      },
      {
        static: false,
        w: 4,
        moved: false,
        h: 8,
        x: 0,
        y: 20,
        i: "aiAndInterviewFeedback",
      },
      {
        static: false,
        w: 4,
        moved: false,
        h: 9,
        x: 0,
        y: 28,
        i: "interviewStatus",
      },
    ],
  };

  // Table columns
  const upcomingInterviewColumns = [
    {
      title: "Candidate Name",
      dataIndex: "candiate",
      render: (candiate) =>
        `${candiate?.first_name || ""} ${candiate?.last_name || ""}`,
    },
    {
      title: "Interview Title",
      dataIndex: "title",
      render: (title, record) => {
        // For specific job position view, use the provided title
        if (jobPositionId && jobPositionTitle) {
          return jobPositionTitle;
        }

        // For all interviews view, try to get position from candidate or fallback to interview title
        const displayTitle = record?.candiate?.position?.title || title || "-";
        const interviewId = record?.id;
        if (interviewId) {
          return (
            <a
              href={`https://hub.york.ie/hiring/interviews/${interviewId}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              {displayTitle}
            </a>
          );
        }
        return displayTitle;
      },
    },
    {
      title: "Interviewer",
      dataIndex: "taken_by",
      render: (interviewer) => (
        <span>{interviewer?.first_name + " " + interviewer?.last_name} </span>
      ),
    },
    {
      title: "Interview Time",
      dataIndex: "selected_time",
      render: (time) => moment(time, "X").format(DateFormatWithTime),
    },
  ];

  const pendingFeedbackColumns = [
    ...upcomingInterviewColumns,
    {
      title: "Action",
      dataIndex: "action",
      render: (text, record) => (
        <SendOutlined
          className="text-primary-500 cursor-pointer text-lg"
          onClick={() => {
            setSelectedInterviewer(record);
            setNotificationModal(true);
          }}
        />
      ),
    },
  ];

  const completedInterviewColumns = [
    ...upcomingInterviewColumns,
    {
      title: "Action",
      dataIndex: "action",
      render: (text, record) => (
        <ExportOutlined
          className={`text-lg ${
            record?.status === "Completed"
              ? "text-primary-500 cursor-pointer"
              : "text-gray-400"
          }`}
          onClick={() =>
            record?.status === "Completed" && handleViewFeedback(record)
          }
          style={{ opacity: record?.status === "Completed" ? 1 : 0.5 }}
        />
      ),
    },
  ];

  //Table Columns for Interview Assessments
  const renderBooleanIcon = (value) => {
    let boolValue = value;
    if (typeof value === "string") {
      boolValue = value.toLowerCase() === "yes";
    }
    if (boolValue === true) {
      return <CheckCircleFilled className="text-green-500" />;
    } else if (boolValue === false) {
      return <CloseCircleFilled className="text-red-500" />;
    } else {
      return "-";
    }
  };

  const CandidateWiseInterviewAssessmentColumns = [
    {
      title: "Candidate Name",
      dataIndex: "candiate",
      render: (candiate, record) => {
        return (
          <span>{`${candiate?.first_name || ""} ${
            candiate?.last_name || ""
          }`}</span>
        );
      },
    },
    {
      title: "Interview Title",
      dataIndex: "title",
      render: (title, record) => {
        const interviewId = record?.id;
        const positionTitle =
          jobPositionId && jobPositionTitle
            ? jobPositionTitle
            : record?.candiate?.position?.title || title || "-";
        if (interviewId) {
          return (
            <a
              href={`https://hub.york.ie/hiring/interviews/${interviewId}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary-600 underline"
            >
              {positionTitle}
            </a>
          );
        }
        return positionTitle;
      },
    },
    {
      title: "Recommended for Future",
      dataIndex: "aiFeedback",
      render: (aiFeedback) => {
        let value = null;
        try {
          if (aiFeedback) {
            const ai = JSON.parse(aiFeedback);
            value = ai?.interviewer?.["Recommended for Future"];
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Confident",
      dataIndex: "aiFeedback",
      render: (aiFeedback) => {
        let value = null;
        try {
          if (aiFeedback) {
            const ai = JSON.parse(aiFeedback);
            value = ai?.interviewer?.["Confident"];
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Job Description Considered",
      dataIndex: "aiFeedback",
      render: (aiFeedback) => {
        let value = null;
        try {
          if (aiFeedback) {
            const ai = JSON.parse(aiFeedback);
            value = ai?.interviewer?.["Job Description Considered"];
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Profile Relevance",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.profileRelevance;
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Company Summary",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.companySummary;
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Interviewer Intro",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.interviewerIntro;
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Comfort Level",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.comfortLevel;
          }
        } catch {}
        return renderBooleanIcon(value);
      },
    },
    {
      title: "Overall Experience",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.overallExperience;
          }
        } catch {}
        if (value === undefined || value === null || value === "") return "-";
        let color = "red";
        if (value >= 4) color = "green";
        else if (value >= 3) color = "gold";
        return <Tag color={color}>{value} / 5</Tag>;
      },
    },
    {
      title: "Difficulty Level",
      dataIndex: "employee_feedback",
      render: (employee_feedback) => {
        let value = null;
        try {
          if (employee_feedback) {
            const fb =
              typeof employee_feedback === "string"
                ? JSON.parse(employee_feedback)
                : employee_feedback;
            value = fb?.difficultyLevel;
          }
        } catch {}
        if (value === undefined || value === null || value === "") return "-";
        let color = "red";
        if (value >= 4) color = "green";
        else if (value >= 3) color = "gold";
        return <Tag color={color}>{value} / 5</Tag>;
      },
    },
    {
      title: "Interview Time",
      dataIndex: "selected_time",
      render: (time) => moment(time, "X").format(DateFormatWithTime),
    },
  ];

  // Extract data from reducer
  const { lastFetched, InterviewsData } = useMemo(() => {
    if (!dataFromReducer) {
      return { lastFetched: null, InterviewsData: [] };
    }

    // If we have job position ID, look for that specific data
    if (jobPositionId && dataFromReducer[jobPositionId]) {
      return {
        lastFetched: dataFromReducer[jobPositionId]?.time,
        InterviewsData: dataFromReducer[jobPositionId]?.data || [],
      };
    } else if (!jobPositionId) {
      // If no job position ID, look for the "all" data
      return {
        lastFetched: dataFromReducer?.all?.time || null,
        InterviewsData: dataFromReducer?.all?.data || [],
      };
    } else {
      // If requested data isn't available, set to empty
      return { lastFetched: null, InterviewsData: [] };
    }
  }, [dataFromReducer, jobPositionId]);

  // State declarations
  const [isLoading, setIsLoading] = useState(false);
  const [showDetails, setShowDetails] = useState(null);
  const [selectedInterviewer, setSelectedInterviewer] = useState(null);
  const [selectedFeedback, setSelectedFeedback] = useState(null);
  //eslint-disable-next-line
  const [feedbackDrawerVisible, setFeedbackDrawerVisible] = useState(false);
  const [notificationModal, setNotificationModal] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [isSendingNotificationLoading, setIsSendingNotificationLoading] =
    useState(false);
  const [layouts, setLayouts] = useState(defaultLayouts);
  const [currentEmployee, setCurrentEmployee] = useState({});
  const [isApplyingLayout, setIsApplyingLayout] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  // S3 profile image cache for interviewers
  const [interviewerProfileImages, setInterviewerProfileImages] = useState({});

  // Reset view-specific state when job position changes
  useEffect(() => {
    setShowDetails(null);
    setSelectedFeedback(null);
    setSelectedInterviewer(null);
  }, [jobPositionId]);

  // Fetch data on component mount or when stale
  useEffect(() => {
    const needsFetch = jobPositionId
      ? !dataFromReducer[jobPositionId] || !dataFromReducer[jobPositionId]?.data
      : !dataFromReducer?.all || !dataFromReducer?.all?.data;

    const isStale =
      lastFetched && moment().diff(moment(new Date(lastFetched)), "hour") > 24;

    if (needsFetch || isStale) {
      handleFetchReportData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobPositionId, dataFromReducer]);

  // Load employee data and saved layout configuration
  useEffect(() => {
    const loadEmployeeAndLayout = async () => {
      try {
        setIsApplyingLayout(true);

        const employee = await getEmployeeAction(email);
        setCurrentEmployee(employee);

        // Parse configurations safely
        let configurations = {};
        if (employee?.configurations) {
          configurations =
            typeof employee.configurations === "string"
              ? JSON.parse(employee.configurations)
              : employee.configurations;
        }
        // Apply saved layout if exists
        if (configurations?.interviewReport) {
          setLayouts(configurations.interviewReport);
        }
      } catch (error) {
        console.error("Error loading employee data:", error);
      } finally {
        setIsApplyingLayout(false);
      }
    };

    loadEmployeeAndLayout();
  }, [email]);

  // Fetch report data
  const handleFetchReportData = async () => {
    try {
      setIsLoading(true);
      if (jobPositionId) {
        const positionData = await getHiringPositionForAnalyticsDataAction(
          jobPositionId
        );

        // Extract interviews for position data
        let interviewsFromPosition = [];
        if (positionData?.candidates?.items) {
          // all interviews from all candidates in this position
          positionData.candidates.items.forEach((candidate) => {
            if (candidate.interview?.items?.length) {
              const candidateInterviews = candidate.interview.items.map(
                (interview) => ({
                  ...interview,
                  candiate: candidate,
                  title: jobPositionTitle || interview.title,
                })
              );
              interviewsFromPosition = [
                ...interviewsFromPosition,
                ...candidateInterviews,
              ];
            }
          });
        }

        dispatch(
          setInterviewOverviewReportsInReducer({
            time: new Date().toISOString(),
            data: interviewsFromPosition,
            jobPositionId,
          })
        );
      } else {
        // When no specific position is selected (all interviews)
        const allInterviewsData = await listInterviewForAnalyticsDataAction({
          status: { eq: "Completed" },
        });
        dispatch(
          setInterviewOverviewReportsInReducer({
            time: new Date().toISOString(),
            data: allInterviewsData,
            jobPositionId: null,
          })
        );
      }
    } catch (error) {
      console.error("Error Fetching chart data:", error);
      message.error("Unable to fetch data at the moment!");
    } finally {
      setIsLoading(false);
    }
  };

  //Toggle for React Grid Layout
  const toggleEditMode = () => {
    setIsEditMode((prev) => !prev);
  };
  // Process data for KPIs
  const processedData = useMemo(() => {
    if (!InterviewsData || !InterviewsData.length) return null;
    const upcoming = InterviewsData.filter(
      (interview) =>
        interview.status === "Scheduled" ||
        interview.status === "Interviewer Time Selection" ||
        interview.status === "Candidate Time Selection"
    );
    const pendingFeedback = InterviewsData.filter(
      (interview) => interview.status === "Awaiting Feedback"
    );

    const completedInterview = InterviewsData.filter(
      (interview) => interview.status === "Completed"
    );

    const totalInterview = InterviewsData.filter(
      (interview) =>
        interview.status !== "Cancelled" && interview.status !== "No Show"
    );

    const feedbackProvided = InterviewsData.filter(
      (interview) =>
        interview.employee_feedback !== null ||
        interview.candiate_feedback !== null
    );

    const positiveInterviewerFeedbackCount = feedbackProvided.filter(
      (interview) => {
        if (!interview.candiate_feedback) return false;
        try {
          const feedback = JSON.parse(interview.candiate_feedback);
          if (
            feedback.hiring_status === "hire" ||
            feedback.hiring_status === "another_round_needed"
          ) {
            return true;
          } else {
            return false;
          }
        } catch (error) {
          return false;
        }
      }
    ).length;

    const negativeInterviewerFeedbackCount = feedbackProvided.filter(
      (interview) => {
        if (!interview.candiate_feedback) return false;
        try {
          const feedback = JSON.parse(interview.candiate_feedback);
          return feedback.hiring_status === "reject";
        } catch (error) {
          return false;
        }
      }
    ).length;

    const acceptanceRatio = feedbackProvided.length
      ? Math.round(
          (positiveInterviewerFeedbackCount / feedbackProvided.length) * 100
        )
      : 0;

    const withAIFeedback = InterviewsData.filter(
      (interview) => interview.aiFeedback !== null
    );

    const positiveAICount = withAIFeedback.filter((interview) => {
      try {
        const feedback = JSON.parse(interview.aiFeedback);
        return feedback["Should Hire"] === true;
      } catch (error) {
        return false;
      }
    }).length;

    const negativeAICount = withAIFeedback.length - positiveAICount;

    const aiAcceptanceRatio = withAIFeedback.length
      ? Math.round((positiveAICount / withAIFeedback.length) * 100)
      : 0;

    const statusCounts = {};

    InterviewsData.forEach((interview) => {
      const status = interview.status;
      if (!statusCounts[status]) {
        statusCounts[status] = 0;
      }
      statusCounts[status]++;
    });

    return {
      upcoming,
      pendingFeedback,
      completedInterview,
      totalInterview,
      acceptanceData: {
        total: feedbackProvided.length,
        positive: positiveInterviewerFeedbackCount,
        negative: negativeInterviewerFeedbackCount,
        ratio: acceptanceRatio,
      },
      aiFeedbackData: {
        total: withAIFeedback.length,
        positive: positiveAICount,
        negative: negativeAICount,
        ratio: aiAcceptanceRatio,
      },
      statusCounts,
    };
  }, [InterviewsData]);

  // Debounced function to save layout changes
  const debouncedUpdateEmployeeLayoutChanges = useMemo(
    () =>
      debounce(async (updatedLayouts) => {
        if (!currentEmployee?.email) return;

        try {
          const existingConfigurations =
            typeof currentEmployee?.configurations === "string"
              ? JSON.parse(currentEmployee.configurations)
              : currentEmployee?.configurations || {};

          const newConfig = {
            ...existingConfigurations,
            interviewReport: updatedLayouts,
          };

          const input = {
            email: currentEmployee.email,
            configurations: JSON.stringify(newConfig),
          };

          await ExecuteMutationV2(updateEmployeeCustom, { input });
        } catch (err) {
          message.error("Failed to save layout changes");
        }
      }, 2000),
    [currentEmployee]
  );

  // Handle layout changes
  const onLayoutChange = useCallback(
    (currentLayout, allLayouts) => {
      setLayouts(allLayouts);
      debouncedUpdateEmployeeLayoutChanges(allLayouts);
    },
    [debouncedUpdateEmployeeLayoutChanges]
  );

  function KPIDisplay({ count, title, description, showBorder = true }) {
    return (
      <div className="relative w-full h-full">
        {isEditMode && (
          <DragOutlined className="dragHandle cursor-move absolute top-0 left-0" />
        )}

        <div className="flex flex-col md:flex-row items-center h-full w-full overflow-hidden p-2">
          <div className="flex items-center justify-center mb-2 md:mb-0 md:mr-3 w-16 h-16 md:w-20 md:h-20 lg:w-24 lg:h-24 rounded">
            <div className="text-4xl md:text-5xl font-bold">{count}</div>
          </div>
          <div className="flex flex-col text-center md:text-left flex-grow">
            <div className="text-sm md:text-base font-semibold mb-1 md:mb-2 text-primary-500 truncate">
              {title}
            </div>
            <p className="text-xs md:text-sm text-gray-600 overflow-hidden break-words hyphens-auto description-text">
              {description}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Send notification to interviewer
  const handleSendNotificationToInterviewer = async () => {
    try {
      setIsSendingNotificationLoading(true);
      const inputData = {
        type: "Interview Feedback Reminder",
        message: "Please add feedback for the interview",
        toAccount: selectedInterviewer?.interviewTaken_byId,
        interviewerFeedbackNote: notificationMessage,
      };
      await createGlobalNotification(inputData);
      message.success(
        `Notification sent successfully to ${selectedInterviewer?.taken_by?.first_name} ${selectedInterviewer?.taken_by?.last_name}`
      );
    } catch (error) {
      message.error("Failed to send notification");
    } finally {
      setSelectedInterviewer(null);
      setNotificationModal(false);
      setNotificationMessage("");
      setIsSendingNotificationLoading(false);
    }
  };

  // View feedback
  const handleViewFeedback = (interview) => {
    setSelectedFeedback(interview);
    setShowDetails(null); // Close any open detail view
  };

  // Render KPI values
  const renderKPIValue = (kpiKey) => {
    if (!processedData) return "-";

    switch (kpiKey) {
      case "upcomingInterview":
        return (
          <KPIDisplay
            count={processedData.upcoming.length}
            title={
              <>
                <CalendarOutlined className="mr-2 text-primary-500" />
                Upcoming Interview
              </>
            }
            description="Scheduled interviews pending completion"
            showBorder={false}
          />
        );
      case "pendingFeedback":
        return (
          <KPIDisplay
            count={processedData.pendingFeedback.length}
            title={
              <>
                <ClockCircleOutlined className="mr-2 text-primary-500" />
                Pending Feedback
              </>
            }
            description="Completed interviews awaiting interviewer feedback"
            icon={<ClockCircleOutlined />}
          />
        );
      case "completedInterview":
        return (
          <KPIDisplay
            count={processedData.completedInterview.length}
            title={
              <div className="flex items-center">
                <CheckCircleOutlined className="mr-2 text-primary-500" />
                Completed Interview
              </div>
            }
            description="Interviews with submitted feedback and evaluation"
            icon={<CheckCircleOutlined />}
          />
        );

      case "totalInterview":
        return (
          <KPIDisplay
            count={processedData.totalInterview.length}
            title={
              <>
                <TeamOutlined className="mr-2 text-primary-500" />
                Total Interview
              </>
            }
            description="All active interviews (excludes cancelled/no-shows)"
            icon={<TeamOutlined />}
          />
        );
      case "acceptanceRatio":
        return (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-3xl font-semibold">
              {processedData.acceptanceData.ratio}%
            </div>
            <div className="flex gap-6 mt-2">
              <div className="flex items-center">
                <Badge status="default" />
                <span className="ml-2">
                  Total: {processedData.acceptanceData.total}
                </span>
              </div>
              <div className="flex items-center">
                <Badge status="success" />
                <span className="ml-2">
                  Positive: {processedData.acceptanceData.positive}
                </span>
              </div>
              <div className="flex items-center">
                <Badge status="error" />
                <span className="ml-2">
                  Negative: {processedData.acceptanceData.negative}
                </span>
              </div>
            </div>
          </div>
        );
      case "aiAndInterviewFeedback":
        return (
          <div className="grid md:grid-cols-2 gap-4 mt-2 rounded">
            {/* AI Feedback  */}
            <div className="flex flex-col items-center border p-2 rounded">
              <div className="text-medium font-semibold">AI Feedback</div>
              <div className="text-3xl font-semibold">
                {processedData.aiFeedbackData.ratio}%
              </div>
              <div className="flex gap-4 mt-2 flex-wrap">
                <div className="flex items-center">
                  <Badge status="default" />
                  <span className="ml-2">
                    Total: {processedData.aiFeedbackData.total}
                  </span>
                </div>

                <div className="flex items-center">
                  <Badge status="success" />
                  <span className="ml-2">
                    Positive: {processedData.aiFeedbackData.positive}
                  </span>
                </div>

                <div className="flex items-center">
                  <Badge status="error" />
                  <span className="ml-2">
                    Negative: {processedData.aiFeedbackData.negative}
                  </span>
                </div>
              </div>
            </div>
            {/* Interview Feedback */}
            <div className="flex flex-col items-center border p-2 rounded">
              <div className="text-medium font-semibold">
                Interviewer Feedback
              </div>
              <div className="text-3xl font-semibold">
                {processedData.acceptanceData.ratio}%
              </div>
              <div className="flex gap-4 mt-2 flex-wrap">
                <div className="flex items-center">
                  <Badge status="default" />
                  <span className="ml-2">
                    Total: {processedData.acceptanceData.total}
                  </span>
                </div>
                <div className="flex items-center">
                  <Badge status="success" />
                  <span className="ml-2">
                    Positive: {processedData.acceptanceData.positive}
                  </span>
                </div>
                <div className="flex items-center">
                  <Badge status="error" />
                  <span className="ml-2">
                    Negative: {processedData.acceptanceData.negative}
                  </span>
                </div>
              </div>
            </div>
          </div>
        );
      case "interviewStatus":
        return (
          <div className="flex flex-col justify-start items-start gap-2 mt-2 w-full">
            {Object.entries(processedData.statusCounts).map(
              ([status, count]) => (
                <div key={status} className="flex justify-between w-full p-1">
                  <div className="flex items-center gap-1">
                    <span className="text-base font-medium text-gray-700">
                      {status}
                    </span>
                  </div>
                  <Tag color={InterviewStatusColorMap[status]}>{count}</Tag>
                </div>
              )
            )}
          </div>
        );
      default:
        return null;
    }
  };

  // Get data and columns for detail view
  const getDetailViewConfig = () => {
    if (!showDetails) return { data: [], columns: [] };

    let data = [];
    let columns = upcomingInterviewColumns;
    let title = "";

    switch (showDetails) {
      case "upcomingInterview":
        data = processedData.upcoming;
        title = "Upcoming Interview";
        break;
      case "pendingFeedback":
        data = processedData.pendingFeedback;
        columns = pendingFeedbackColumns;
        title = "Pending Feedback Interview";
        break;
      case "completedInterview":
      case "totalInterview":
        data =
          showDetails === "completedInterview"
            ? processedData.completedInterview
            : processedData.totalInterview;
        columns = completedInterviewColumns;
        title =
          showDetails === "completedInterview"
            ? "Completed Interview"
            : "Total Interview";
        break;
      default:
        break;
    }
    return { data, columns, title };
  };

  const {
    data: detailData,
    columns: detailColumns,
    title: detailTitle,
  } = getDetailViewConfig();

  // Check if KPI is clickable
  const isKPIClickable = (kpiKey) => {
    return [
      "upcomingInterview",
      "pendingFeedback",
      "completedInterview",
      "totalInterview",
    ].includes(kpiKey);
  };

  // Helper to group interviews by interviewer
  const interviewerWiseData = useMemo(() => {
    if (!InterviewsData || !InterviewsData.length) return [];
    // Only include interviews with status 'Completed'
    const completedInterviews = InterviewsData.filter(
      (interview) => interview.status === "Completed"
    );
    const grouped = {};
    completedInterviews.forEach((interview) => {
      const interviewer = interview.taken_by;
      if (!interviewer || !interviewer.email) return;
      if (!grouped[interviewer.email]) {
        grouped[interviewer.email] = {
          interviewer,
          interviews: [],
        };
      }
      grouped[interviewer.email].interviews.push(interview);
    });
    // Calculate summary stats for each interviewer
    const arr = Object.values(grouped).map(({ interviewer, interviews }) => {
      let positive = 0,
        negative = 0,
        aiPositive = 0,
        aiTotal = 0,
        totalScore = 0,
        scoreCount = 0;
      interviews.forEach((interview) => {
        // Interviewer Recommendation
        let feedback = null;
        try {
          if (interview.candiate_feedback) {
            feedback =
              typeof interview.candiate_feedback === "string"
                ? JSON.parse(interview.candiate_feedback)
                : interview.candiate_feedback;
          }
        } catch {}
        if (feedback) {
          if (
            feedback.hiring_status === "hire" ||
            feedback.hiring_status === "another_round_needed"
          )
            positive++;
          if (feedback.hiring_status === "reject") negative++;
          if (typeof feedback.overall_score === "number") {
            totalScore += feedback.overall_score;
            scoreCount++;
          }
        }
        // AI Recommendation
        try {
          if (interview.aiFeedback) {
            const ai = JSON.parse(interview.aiFeedback);
            aiTotal++;
            if (ai["Should Hire"] === true) aiPositive++;
          }
        } catch {}
      });
      return {
        key: interviewer.email,
        interviewer,
        total: interviews.length,
        positive,
        negative,
        aiPositive,
        aiTotal,
        aiRatio: aiTotal ? Math.round((aiPositive / aiTotal) * 100) : 0,
        avgScore: scoreCount ? (totalScore / scoreCount).toFixed(2) : "-",
        interviews,
      };
    });
    // Sort by total interviews descending
    arr.sort((a, b) => b.total - a.total);
    return arr;
  }, [InterviewsData]);

  // Fetch S3 profile images for all unique interviewers
  useEffect(() => {
    const fetchProfileImages = async () => {
      if (!InterviewsData || !InterviewsData.length) return;
      const unique = {};
      InterviewsData.forEach((interview) => {
        const interviewer = interview.taken_by;
        if (
          interviewer &&
          interviewer.email &&
          interviewer.profile_pic &&
          typeof interviewer.profile_pic === "string" &&
          interviewer.profile_pic.trim()
        ) {
          unique[interviewer.email] = interviewer.profile_pic;
        }
      });
      const entries = Object.entries(unique);
      if (entries.length === 0) return;
      const newImages = {};
      await Promise.all(
        entries.map(async ([email, key]) => {
          console.log("Fetching S3 for", email, "with key", key);
          try {
            newImages[email] = await GetFileFromS3(key);
            console.log("Fetched S3 URL for", email, ":", newImages[email]);
          } catch (err) {
            newImages[email] = null;
            console.warn("Failed to fetch S3 URL for", email, key, err);
          }
        })
      );
      setInterviewerProfileImages((prev) => ({ ...prev, ...newImages }));
    };
    fetchProfileImages();
  }, [InterviewsData]);

  // Helper to determine column type for summary
  const getSummaryType = (col) => {
    // Boolean columns use renderBooleanIcon
    if (
      [
        "Recommended for Future",
        "Confident",
        "Job Description Considered",
        "Profile Relevance",
        "Company Summary",
        "Interviewer Intro",
        "Comfort Level",
      ].includes(col.title)
    ) {
      return "boolean";
    }
    // Rating columns
    if (["Overall Experience", "Difficulty Level"].includes(col.title)) {
      return "rating";
    }
    // Text columns
    if (["Additional Comments"].includes(col.title)) {
      return "text";
    }
    return null;
  };

  // Build InterviewerWiseAssessmentColumns dynamically
  const InterviewerWiseAssessmentColumns = [
    {
      title: "Interviewer Name",
      dataIndex: ["interviewer", "first_name"],
      key: "interviewerName",
      width: 230,
      render: (_, data) => {
        const { first_name, last_name, profile_pic, email } =
          data.interviewer || {};
        const initials = `${first_name?.[0] || ""}${
          last_name?.[0] || ""
        }`.toUpperCase();
        const s3Url = interviewerProfileImages[email];
        const profileUrl = email
          ? `https://hub.york.ie/employee-view/${email.split("@")[0]}`
          : undefined;
        return (
          <p>
            {profileUrl ? (
              <a
                href={profileUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 no-underline hover:underline"
              >
                <Avatar
                  shape="circle"
                  size={60}
                  icon={!s3Url && <UserOutlined />}
                  src={s3Url || undefined}
                  className="flex-shrink-0"
                >
                  {!s3Url && initials}
                </Avatar>
                <span className="text-gray-900 whitespace-nowrap">
                  {`${first_name || ""} ${last_name || ""}`}
                </span>
                {!s3Url && (
                  <span className="text-red-500 text-xs ml-1">No image</span>
                )}
              </a>
            ) : (
              <span className="flex items-center gap-3">
                <Avatar
                  shape="circle"
                  size={60}
                  icon={!s3Url && <UserOutlined />}
                  src={s3Url || undefined}
                  className="flex-shrink-0"
                >
                  {!s3Url && initials}
                </Avatar>
                <span className="text-gray-900 whitespace-nowrap">
                  {`${first_name || ""} ${last_name || ""}`}
                </span>
                {!s3Url && (
                  <span className="text-red-500 text-xs ml-1">No image</span>
                )}
              </span>
            )}
          </p>
        );
      },
    },
    {
      title: "Total Interviews",
      dataIndex: "total",
      key: "total",
      sorter: (a, b) => a.total - b.total,
      render: (val) => <span>{val}</span>,
    },
    // Dynamically add summary columns for each candidate-wise column
    ...CandidateWiseInterviewAssessmentColumns.filter(
      (col) =>
        !["Candidate Name", "Interview Title", "Interview Time"].includes(
          col.title
        )
    ).map((col) => {
      const summaryType = getSummaryType(col);
      return {
        title: col.title,
        key: col.title,
        render: (_, interviewerRow) => {
          const interviews = interviewerRow.interviews;
          if (!interviews || interviews.length === 0) return "-";
          // Boolean summary: percent true
          if (summaryType === "boolean") {
            let trueCount = 0,
              available = 0;
            interviews.forEach((row) => {
              let value = col.dataIndex ? row[col.dataIndex] : undefined;
              let fieldPresent = false;
              // Determine which field to check
              if (col.dataIndex === "aiFeedback") {
                // AI feedback fields
                let ai = null;
                try {
                  ai = value ? JSON.parse(value) : null;
                } catch {}
                if (ai && ai.interviewer && col.title in ai.interviewer) {
                  fieldPresent = true;
                  value = ai.interviewer[col.title];
                }
              } else if (col.dataIndex === "employee_feedback") {
                // Candidate feedback fields
                let fb = null;
                try {
                  fb = value
                    ? typeof value === "string"
                      ? JSON.parse(value)
                      : value
                    : null;
                } catch {}
                // Map column title to feedback key
                const feedbackKeyMap = {
                  "Profile Relevance": "profileRelevance",
                  "Company Summary": "companySummary",
                  "Interviewer Intro": "interviewerIntro",
                  "Comfort Level": "comfortLevel",
                };
                const key = feedbackKeyMap[col.title];
                if (fb && key && key in fb) {
                  fieldPresent = true;
                  value = fb[key];
                }
              }
              // Only consider if field is present
              if (fieldPresent) {
                available++;
                let boolValue = value;
                if (typeof boolValue === "string")
                  boolValue = boolValue.toLowerCase() === "yes";
                if (
                  boolValue === true ||
                  (typeof value === "object" &&
                    value?.props?.className?.includes("text-green-500"))
                )
                  trueCount++;
              }
            });
            if (available === 0) return "-";
            const percent = Math.round((trueCount / available) * 100);
            const isMajority = percent >= 50;
            return (
              <span>
                {renderBooleanIcon(isMajority)} {percent}% ({trueCount}/
                {available})
              </span>
            );
          }
          // Rating summary: average
          if (summaryType === "rating") {
            let sum = 0,
              count = 0;
            interviews.forEach((row) => {
              let value = col.dataIndex ? row[col.dataIndex] : undefined;
              let num = null;
              try {
                if (
                  col.title === "Overall Experience" ||
                  col.title === "Difficulty Level"
                ) {
                  const fb =
                    typeof value === "string" ? JSON.parse(value) : value;
                  num =
                    fb?.[
                      col.title === "Overall Experience"
                        ? "overallExperience"
                        : "difficultyLevel"
                    ];
                }
              } catch {}
              if (typeof num === "number") {
                sum += num;
                count++;
              } else if (typeof value === "number") {
                sum += value;
                count++;
              }
            });
            if (!count) return "-";
            const avg = (sum / count).toFixed(2);
            let color = "red";
            if (avg >= 4) color = "green";
            else if (avg >= 3) color = "gold";
            return <Tag color={color}>{avg} / 5</Tag>;
          }
          // Text summary: percent non-empty
          if (summaryType === "text") {
            let nonEmpty = 0,
              total = 0;
            interviews.forEach((row) => {
              let value = col.dataIndex ? row[col.dataIndex] : undefined;
              let text = "";
              try {
                const fb =
                  typeof value === "string" ? JSON.parse(value) : value;
                text = fb?.additionalComments;
              } catch {}
              if (text && text.trim() !== "") nonEmpty++;
              total++;
            });
            const percent = Math.round((nonEmpty / total) * 100);
            return <span>{percent}%</span>;
          }
          // Fallback
          return "-";
        },
      };
    }),
  ];

  // Show loading state
  if (isApplyingLayout) {
    return (
      <Loader title="Please wait while we load your personalized dashboard" />
    );
  }

  // Helper to check if any summary column has data for an interviewer row
  function hasAnySummaryData(interviewerRow) {
    // Get the summary columns (excluding name and total)
    const summaryColumns = InterviewerWiseAssessmentColumns.slice(2);
    // For each summary column, call its render function and check if it returns something other than '-' or empty
    return summaryColumns.some((col) => {
      // The render function receives (_, interviewerRow)
      const rendered = col.render
        ? col.render(undefined, interviewerRow)
        : undefined;
      // If it's a React element, try to get its string value
      if (typeof rendered === "string") {
        return rendered !== "-" && rendered.trim() !== "";
      }
      // If it's a number or boolean
      if (typeof rendered === "number" || typeof rendered === "boolean") {
        return !!rendered;
      }
      // If it's a React element, check if it's not just '-'
      if (rendered && rendered.props && rendered.props.children) {
        if (typeof rendered.props.children === "string") {
          return (
            rendered.props.children !== "-" &&
            rendered.props.children.trim() !== ""
          );
        }
        // If children is an array, check if any child is not '-'
        if (Array.isArray(rendered.props.children)) {
          return rendered.props.children.some(
            (child) =>
              typeof child === "string" && child !== "-" && child.trim() !== ""
          );
        }
      }
      // Fallback: if rendered is not null/undefined/empty
      return !!rendered && rendered !== "-";
    });
  }

  return (
    <>
      {/* Analytics Header */}
      <AnalyticsHeader
        title={`Interview Overview Report ${
          jobPositionTitle ? `for ${jobPositionTitle}` : ""
        }`}
        isLoading={isLoading}
        extra={
          <div className="flex gap-4">
            <Button
              type={"default"}
              onClick={toggleEditMode}
              icon={isEditMode ? <CheckCircleOutlined /> : <EditOutlined />}
              size="small"
            >
              {isEditMode ? "Done" : "Edit Layout"}
            </Button>
            <>
              Last Fetched :{" "}
              <Tooltip
                title={moment(new Date(lastFetched))?.format(
                  DateFormatWithTime
                )}
              >
                <div className="border-b-2 border-dashed border-gray-700 pb-0.5 cursor-default">
                  {moment(new Date(lastFetched)).fromNow()}
                </div>
              </Tooltip>
              <Tooltip title="Refresh Data">
                <div onClick={handleFetchReportData} className="cursor-pointer">
                  <ReloadOutlined />
                </div>
              </Tooltip>
            </>
          </div>
        }
      />

      {isLoading ? (
        <Loader title="Loading Interview Overview Report" />
      ) : (
        <div className="w-full">
          <ResponsiveGridLayout
            className="layout"
            layouts={layouts}
            breakpoints={{ md: 996, sm: 768, xs: 480 }}
            cols={{ md: 12, sm: 6, xs: 4 }}
            rowHeight={30}
            onLayoutChange={onLayoutChange}
            draggableHandle=".dragHandle"
            isDraggable={isEditMode}
            isResizable={isEditMode}
          >
            {/* KPI Widgets */}
            {Object.entries(INTERVIEW_KPI_TITLES).map(([key, title]) => (
              <div
                key={key}
                className={`${boxClass} overflow-hidden relative ${
                  showDetails === key ? "border-2 border-primary-500" : ""
                }`}
              >
                <div className="flex flex-col h-full">
                  <div className="mb-1 font-semibold">
                    <div className="flex justify-between items-center">
                      {!isKPIClickable(key) && (
                        <Typography.Title
                          className="flex !text-primary-600 !my-1 !text-base md:!text-lg"
                          level={5}
                        >
                          {isEditMode && (
                            <DragOutlined className="dragHandle cursor-move mr-1" />
                          )}
                          <span className="truncate">{title}</span>
                        </Typography.Title>
                      )}
                    </div>
                  </div>
                  <div
                    className={`flex-grow flex items-center justify-center w-full ${
                      isKPIClickable(key) ? "cursor-pointer" : ""
                    }`}
                    onClick={() =>
                      isKPIClickable(key) &&
                      setShowDetails(showDetails === key ? null : key)
                    }
                  >
                    {renderKPIValue(key)}
                  </div>
                </div>
              </div>
            ))}
          </ResponsiveGridLayout>
        </div>
      )}
      {/* View Details Section */}
      {showDetails ? (
        <div className={`${boxClass} flex flex-col gap-y-2 bg-slate-50 mt-4`}>
          <Typography.Title
            level={3}
            className="flex justify-between mt-1 mb-0"
          >
            <div className="text-primary-500">{detailTitle}</div>
            <div
              className="cursor-pointer font-bold text-sm pr-2 flex align-middle gap-1"
              onClick={() => setShowDetails(null)}
            >
              <CloseCircleOutlined className="text-lg" />
              <span className="self-center">Close</span>
            </div>
          </Typography.Title>
          <div>
            <CustomTable
              columns={detailColumns}
              title={detailTitle}
              dataSource={detailData}
              skeletonLoading={isLoading}
              scroll={{ x: 1000 }}
            />
          </div>
        </div>
      ) : null}
      {/* show interview assesment details */}
      {isLoading ? (
        <Loader title="Loading Interviewer wise Interview Assessments..." />
      ) : (
        <CustomTable
          columns={InterviewerWiseAssessmentColumns}
          scroll={{ x: 1000 }}
          title="Interviewer wise Interview Assessments"
          pagination={{ pageSize: 100 }}
          expandable={{
            expandedRowRender: (record) => (
              <CustomTable
                columns={CandidateWiseInterviewAssessmentColumns}
                dataSource={record.interviews}
                pagination={false}
                rowKey="id"
                scroll={{ x: 1000 }}
                expandable={{
                  expandedRowRender: (row) => (
                    <div style={{ maxWidth: "500px", minWidth: "100%" }}>
                      <InterviewFeedbackCard
                        aiFeedback={{
                          ...(row.aiFeedback
                            ? typeof row.aiFeedback === "string"
                              ? JSON.parse(row.aiFeedback)
                              : row.aiFeedback
                            : {}),
                          candidateFeedback: row.employee_feedback
                            ? typeof row.employee_feedback === "string"
                              ? JSON.parse(row.employee_feedback)
                              : row.employee_feedback
                            : null,
                        }}
                        candidateFeedback={
                          row.candiate_feedback
                            ? typeof row.candiate_feedback === "string"
                              ? JSON.parse(row.candiate_feedback)
                              : row.candiate_feedback
                            : null
                        }
                      />
                    </div>
                  ),
                }}
              />
            ),
          }}
          rowKey="key"
          dataSource={interviewerWiseData.filter(hasAnySummaryData)}
        />
      )}

      {/* Send Feedback Reminder Modal */}
      <Modal
        title="Send Feedback Reminder"
        open={notificationModal}
        onCancel={() => {
          setNotificationModal(false);
          setSelectedInterviewer(null);
          setNotificationMessage("");
        }}
        onOk={handleSendNotificationToInterviewer}
        okText="Send"
        cancelText="Cancel"
        okButtonProps={{ loading: isSendingNotificationLoading }}
      >
        {selectedInterviewer && (
          <div className="mb-4">
            <p className="text-gray-500">
              Send a reminder to {selectedInterviewer?.taken_by?.first_name}{" "}
              {selectedInterviewer?.taken_by?.last_name} to submit feedback for
              the interview with {selectedInterviewer?.candiate?.first_name}{" "}
              {selectedInterviewer?.candiate?.last_name}
            </p>
            <div className="mt-4">
              <label className="block mb-2">
                Add a personalized note (optional):
              </label>
              <TextArea
                rows={4}
                value={notificationMessage}
                onChange={(e) => setNotificationMessage(e.target.value)}
                maxLength={50}
                showCount
                placeholder="E.g., Hi there, could you please provide feedback for the interview you conducted? It's important for us to move forward with the candidate."
              />
            </div>
          </div>
        )}
      </Modal>

      {/* Feedback Drawer */}
      {selectedFeedback && (
        <Drawer
          title="Interview Feedback"
          placement="right"
          onClose={() => {
            setSelectedFeedback(null);
            setFeedbackDrawerVisible(false);
          }}
          visible={true}
          open={true}
          width={"70%"}
        >
          <RenderInterviewFeedback interview={selectedFeedback} isDetailed />
        </Drawer>
      )}
    </>
  );
};

export default InterviewsOverviewReport;
