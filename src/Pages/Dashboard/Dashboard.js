import { DragOutlined } from "@ant-design/icons";
import { But<PERSON>, message, Typography } from "antd";
import moment from "moment";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Responsive, WidthProvider } from "react-grid-layout";
import "react-grid-layout/css/styles.css";
import "react-markdown-editor-lite/lib/index.css";
import { useSelector } from "react-redux";
import "react-resizable/css/styles.css";

// Components
import Loader from "Commons/Loader";
import ArchivedNotices from "Pages/Dashboard/ArchivedNoticeBoard";
import PendingWFHRequestByEmployee from "Pages/Dashboard/Components/PendingWFHRequestByEmployee";
import { EmployeeLeaveRequestDashBoard } from "Pages/Leave/V2/EmployeeLeaveRequestDashboard";
import EmployeeInterviewList from "./EmployeeInterviewList";
import KudosDashboard from "./KudosBoard";
import NoticeBoard from "./NoticeBoard";
import PeopleOnLeave from "./PeopleOnLeave";
import ProjectAllocation from "./ProjectAllocation";
import SendWish from "./SendWish";
import DashboardJiraBox from "./Components/DashboardJiraBox";
import CelebrationBoard from "./Components/CelebrationBoard";
import KnowYourColleagues from "./Components/KnowYourColleagues";
import CommercialTieUps from "./Components/CommercialTieUps";
import ChatbotWidget from "./Components/ChatbotWidget";
import LeaveCalendarWidget from "./Components/LeaveCalendarWidget";

// Utilities and Actions
import { updateEmployeeCustom } from "graphql/customMutation";
import { debounce } from "lodash";
import { getEmployeeAction } from "utils/Actions";
import { ExecuteMutationV2 } from "utils/Api";
import { boxClass } from "utils/TailwindCommonClasses";
import {
  getEmployeeDetails,
  isExecutive,
  isHr,
  isSquadLeader,
  isUnitTeamLeader,
  isRecruiter, // Add this import
} from "../../store/slices/loginSlice";

import PeopleOnWFH from "./Components/PeopleOnWFH";
import NewJoinee from "./Components/NewJoinee";
import CustomizeWidgetModal from "Commons/CustomizeWidgetModal";
import WidgetToggle from "Commons/WidgetToggle";
import PendingActionItems from "Pages/Dashboard/PendingActionItems";

const ResponsiveGridLayout = WidthProvider(Responsive);
// Default layout configurations for different screen sizes
const defaultLayouts = {
  md: [
    {
      i: "announcements",
      x: 0,
      y: 0,
      w: 8,
      h: 10,
      minW: 4,
      minH: 8,
      visible: true,
    },
    {
      i: "sendWish",
      x: 8,
      y: 0,
      w: 4,
      h: 10,
      minW: 4,
      minH: 8,
      visible: true,
    },
    { i: "kudos", x: 0, y: 6, w: 8, h: 10, visible: true },
    {
      i: "pendingWFH",
      x: 8,
      y: 6,
      w: 12,
      h: 12,
      minW: 8,
      minH: 10,
      visible: true,
    },
    { i: "celebrations", x: 8, y: 6, w: 4, h: 10, visible: true },
    { i: "chatbot", x: 0, y: 16, w: 4, h: 10, visible: true },
    {
      i: "leaveDashboard",
      x: 0,
      y: 26,
      w: 12,
      h: 10,
      visible: true,
    },
    { i: "leaveCalendar", x: 0, y: 36, w: 8, h: 12, visible: true },
    { i: "peopleOnLeave", x: 8, y: 36, w: 4, h: 8, minW: 4, visible: true },
    { i: "projectAllocation", x: 0, y: 48, w: 4, h: 8, visible: true },
    {
      i: "newJoinee",
      x: 4,
      y: 48,
      w: 4,
      h: 8,
      minW: 6,
      maxW: 6,
      visible: true,
    },
    { i: "interviewList", x: 0, y: 56, w: 12, h: 8, visible: true },
    { i: "jiraBox", x: 0, y: 64, w: 12, h: 10, visible: true },
    { i: "commercialTieUps", x: 0, y: 74, w: 12, h: 10, visible: true },
    {
      i: "pendingActionItems",
      x: 8,
      y: 20,
      w: 20,
      h: 10,
      minW: 20,
      minH: 8,
      visible: true,
    },
  ],
  sm: [
    {
      i: "announcements",
      x: 0,
      y: 0,
      w: 6,
      h: 6,
      minW: 4,
      minH: 8,
      visible: true,
    },
    { i: "kudos", x: 0, y: 6, w: 6, h: 6, visible: true },
    {
      i: "pendingWFH",
      x: 0,
      y: 12,
      w: 6,
      h: 8,
      minW: 3,
      minH: 6,
      visible: true,
    },
    { i: "celebrations", x: 0, y: 12, w: 6, h: 8, visible: true },
    { i: "chatbot", x: 0, y: 20, w: 6, h: 8, visible: true },
    {
      i: "leaveDashboard",
      x: 0,
      y: 28,
      w: 6,
      h: 10,
      visible: true,
    },
    { i: "leaveCalendar", x: 0, y: 38, w: 6, h: 12, visible: true },
    { i: "peopleOnLeave", x: 0, y: 50, w: 6, h: 6, minW: 4, visible: true },
    {
      i: "sendWish",
      x: 0,
      y: 56,
      w: 6,
      h: 6,
      minW: 4,
      minH: 8,
      visible: true,
    },
    { i: "projectAllocation", x: 0, y: 62, w: 6, h: 6, visible: true },
    {
      i: "newJoinee",
      x: 0,
      y: 68,
      w: 6,
      h: 6,
      minW: 6,
      maxW: 6,
      visible: true,
    },
    { i: "interviewList", x: 0, y: 74, w: 6, h: 8,visible: true },
    { i: "jiraBox", x: 0, y: 82, w: 6, h: 10, visible: true },
    { i: "commercialTieUps", x: 0, y: 92, w: 6, h: 10, visible: true },
    {
      i: "pendingActionItems",
      x: 0,
      y: 102,
      w: 6,
      h: 8,
      minW: 3,
      minH: 6,
      visible: true,
    },
  ],
  xs: [
    {
      i: "announcements",
      x: 0,
      y: 0,
      w: 4,
      h: 6,
      minW: 4,
      minH: 8,
      visible: true,
    },
    { i: "kudos", x: 0, y: 6, w: 4, h: 6, visible: true },
    {
      i: "pendingWFH",
      x: 0,
      y: 12,
      w: 4,
      h: 6,
      minW: 2,
      minH: 4,
      visible: true,
    },
    { i: "celebrations", x: 0, y: 12, w: 4, h: 6, visible: true },
    { i: "chatbot", x: 0, y: 18, w: 4, h: 8, visible: true },
    {
      i: "leaveDashboard",
      x: 0,
      y: 26,
      w: 4,
      h: 10,
      visible: true,
    },
    { i: "leaveCalendar", x: 0, y: 36, w: 4, h: 12, visible: true },
    { i: "interviewList", x: 0, y: 48, w: 4, h: 8, visible: true },
    { i: "peopleOnLeave", x: 0, y: 56, w: 4, h: 6, minW: 4, visible: true },
    {
      i: "sendWish",
      x: 0,
      y: 62,
      w: 4,
      h: 6,
      minW: 4,
      minH: 8,
      visible: true,
    },
    { i: "projectAllocation", x: 0, y: 68, w: 4, h: 6, visible: true },
    {
      i: "newJoinee",
      x: 0,
      y: 74,
      w: 4,
      h: 6,
      minW: 6,
      maxW: 6,
      visible: true,
    },
    { i: "jiraBox", x: 0, y: 80, w: 4, h: 10, visible: true },
    { i: "commercialTieUps", x: 0, y: 90, w: 4, h: 10, visible: true },
    {
      i: "pendingActionItems",
      x: 0,
      y: 100,
      w: 4,
      h: 8,
      minW: 2,
      minH: 4,
      visible: true,
    },
  ],
};

/**
 * Main Dashboard component with customizable grid layout
 * Features:
 * - Drag-and-drop resizable widgets
 * - Role-based content visibility
 * - Persistent layout customization
 */
export default function Dashboard() {
  // Redux state selectors
  const {
    userData: { email },
  } = useSelector((state) => state?.loginReducer);
  const isExecutiveLogin = useSelector(isExecutive);
  const isHrLogin = useSelector(isHr);
  const isUnitLeader = useSelector(isUnitTeamLeader);
  const isSquadLeaderLogin = useSelector(isSquadLeader);
  const isRecruiterLogin = useSelector(isRecruiter); // Add this selector
  const employeeDetails = useSelector(getEmployeeDetails);

  // Memoized role check flags to prevent unnecessary recalculations
  const isPowerUserWithoutHR = useMemo(
    () => isExecutiveLogin || isUnitLeader || isSquadLeaderLogin,
    [isExecutiveLogin, isUnitLeader, isSquadLeaderLogin]
  );
  const isPowerUser = useMemo(
    () => isPowerUserWithoutHR || isHrLogin,
    [isPowerUserWithoutHR, isHrLogin]
  );
  // Local state management
  const [currentEmployee, setCurrentEmployee] = useState({});
  const [addNoticeModalOpen, setAddNoticeModalOpen] = useState(false);
  const [archivedNoticesVisible, setArchivedNoticesVisible] = useState(false);
  const [layouts, setLayouts] = useState(defaultLayouts);
  const [isApplyingLayout, setIsApplyingLayout] = useState(false);
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [widgetConfigs, setWidgetConfigs] = useState({});
  const [currentBreakpoint, setCurrentBreakpoint] = useState("md");
  // Get start of day timestamp
  const startOfToday = moment().startOf("day").unix();

  /**
   * Load employee data and saved layout configuration
   */
  useEffect(() => {
    const loadEmployeeAndLayout = async () => {
      try {
        setIsApplyingLayout(true);
        const employee = await getEmployeeAction(email);
        setCurrentEmployee(employee);
        // Parse configurations safely
        let configurations = {};
        if (employee?.configurations) {
          configurations =
            typeof employee.configurations === "string"
              ? JSON.parse(employee.configurations)
              : employee.configurations;
        }

        if (configurations?.dashboard) {
          let updatedLayouts = { ...configurations.dashboard };

          // Loop through each breakpoint (md, sm, xs)
          ["md", "sm", "xs"].forEach((breakpoint) => {
            const defaultLayout = defaultLayouts[breakpoint] || [];
            const dbLayout = configurations.dashboard[breakpoint] || [];

            const existingWidgetMap = new Map(dbLayout.map((w) => [w.i, w]));
            let mergedLayout = [...dbLayout]; // preserve order

            // Add only new widgets that aren't in DB
            defaultLayout.forEach((defaultWidget) => {
              if (!existingWidgetMap.has(defaultWidget.i)) {
                const maxY = dbLayout.reduce(
                  (max, item) => Math.max(max, item.y + item.h),
                  0
                );

                mergedLayout.push({
                  ...defaultWidget,
                  x: 0,
                  y: maxY,
                  moved: false,
                  static: false,
                  visible: defaultWidget.visible ?? true,
                });
              }
            });
            updatedLayouts[breakpoint] = mergedLayout;
          });
          const widgetConfigMap = buildWidgetConfig(configurations.dashboard);
          setWidgetConfigs(widgetConfigMap);
          const visibleLayouts = applyVisibilityToLayouts(
            updatedLayouts,
            widgetConfigMap
          );

          setLayouts(visibleLayouts);

          const newConfig = {
            ...configurations,
            dashboard: updatedLayouts,
          };

          await ExecuteMutationV2(updateEmployeeCustom, {
            input: {
              email: employee.email,
              configurations: JSON.stringify(newConfig),
            },
          });
        } else {
          const newConfig = {
            ...configurations,
            dashboard: enforceMinSize(defaultLayouts),
          };

          await ExecuteMutationV2(updateEmployeeCustom, {
            input: {
              email: employee.email,
              configurations: JSON.stringify({
                ...configurations,
                dashboard: enforceMinSize(defaultLayouts),
              }),
            },
          });
        }
      } catch (error) {
        console.error("Error loading employee data:", error);
      } finally {
        setIsApplyingLayout(false);
      }
    };
    loadEmployeeAndLayout();
  }, [email]);

  const buildWidgetConfig = (layouts) => {
    const merged = [...layouts.md, ...layouts.sm, ...layouts.xs];
    const configMap = {};
    merged.forEach((widget) => {
      if (!configMap[widget.i]) {
        configMap[widget.i] = {
          key: widget.i,
          visible: widget.visible ?? true,
        };
      }
    });
    return configMap;
  };

  const applyVisibilityToLayouts = (layouts, widgetConfigs) => {
    const updatedLayouts = { ...layouts };
    Object.keys(updatedLayouts).forEach((breakpoint) => {
      updatedLayouts[breakpoint] = updatedLayouts[breakpoint].map((widget) => ({
        ...widget,
        visible: widgetConfigs[widget.i]?.visible ?? true,
      }));
    });
    return updatedLayouts;
  };
  /**
   * Debounced function to save layout changes
   */
  const debouncedUpdateEmployeeLayoutChanges = useMemo(
    () =>
      debounce(async (updatedLayouts) => {
        if (!currentEmployee?.email) return;

        try {
          const existingConfigurations =
            typeof currentEmployee?.configurations === "string"
              ? JSON.parse(currentEmployee.configurations)
              : currentEmployee?.configurations || {};
          const newConfig = {
            ...existingConfigurations,
            dashboard: enforceMinSize(updatedLayouts),
          };

          const input = {
            email: currentEmployee.email,
            configurations: JSON.stringify(newConfig),
          };

          await ExecuteMutationV2(updateEmployeeCustom, { input });
        } catch (err) {
          message.error("Failed to save layout changes");
        }
      }, 2000),
    [currentEmployee]
  );

  /**
   * Handle layout changes
   */
  const onLayoutChange = useCallback(
    (currentLayout, allLayouts) => {
      setLayouts(allLayouts);
      debouncedUpdateEmployeeLayoutChanges(allLayouts);
    },
    [debouncedUpdateEmployeeLayoutChanges]
  );
  const handleToggleWidget = (key, checked) => {
    const updated = {
      ...widgetConfigs,
      [key]: {
        ...widgetConfigs[key],
        visible: checked,
      },
    };
    const updatedLayouts = applyVisibilityToLayouts(layouts, updated);

    setWidgetConfigs(updated);
    setLayouts(updatedLayouts);
    debouncedUpdateEmployeeLayoutChanges(updatedLayouts);
  };

  // Show loading state while applying layout
  if (isApplyingLayout) {
    return (
      <Loader title="Please wait while we load your personalized dashboard" />
    );
  }
  const isVisible = (key) => widgetConfigs?.[key]?.visible;

  return (
    <div className="w-full">
      <div className="flex justify-end !mr-3">
        <CustomizeWidgetModal
          widgetConfigs={widgetConfigs}
          onToggleWidget={handleToggleWidget}
          isCustomizing={isCustomizing}
          setIsCustomizing={setIsCustomizing}
        />
      </div>
      <ResponsiveGridLayout
        className="layout"
        layouts={enforceMinSize(layouts)}
        breakpoints={{ md: 996, sm: 768, xs: 480 }}
        cols={{ md: 12, sm: 6, xs: 4 }}
        rowHeight={30}
        onBreakpointChange={(newBreakpoint) =>
          setCurrentBreakpoint(newBreakpoint)
        }
        onLayoutChange={onLayoutChange}
        draggableHandle=".dragHandle"
        isDraggable
        isResizable
      >
        {/* Announcements Widget */}
        {isVisible("announcements") && (
          <div key="announcements" className={`${boxClass} overflow-hidden`}>
            <div className="mb-2 font-semibold">
              <div className="flex justify-between mb-2">
                <Typography.Title className="flex" level={5}>
                  {isCustomizing && (
                    <DragOutlined className="dragHandle cursor-move mr-1" />
                  )}
                  Announcements
                </Typography.Title>

                <div>
                  <div className="flex items-center gap-1">
                    {(isExecutiveLogin || isHrLogin) && (
                      <Button
                        onClick={() => setAddNoticeModalOpen(true)}
                        type="primary"
                      >
                        + Add Announcement
                      </Button>
                    )}
                    <Button
                      onClick={() => setArchivedNoticesVisible(true)}
                      type="link"
                      className="ml-2"
                    >
                      View Old Announcements
                    </Button>
                    {isCustomizing && (
                      <WidgetToggle
                        key="announcements"
                        widgetKey="announcements"
                        visible={widgetConfigs.announcements?.visible}
                        onChange={(checked) =>
                          handleToggleWidget("announcements", checked)
                        }
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="overflow-auto h-[calc(100%-3rem)]">
              <NoticeBoard
                AddNoticeModal={addNoticeModalOpen}
                setAddNoticeModal={setAddNoticeModalOpen}
                startOfToday={startOfToday}
                setArchivedNoticesVisible={setArchivedNoticesVisible}
              />
              <ArchivedNotices
                visible={archivedNoticesVisible}
                onClose={() => setArchivedNoticesVisible(false)}
                startOfToday={startOfToday}
              />
            </div>
          </div>
        )}

        {/* New Joinee Widget */}
        {isVisible("newJoinee") && (
          <div key="newJoinee" className={`${boxClass} overflow-hidden`}>
            <Typography.Title className="flex justify-between" level={5}>
              <span>
                {isCustomizing && (
                  <DragOutlined className="dragHandle cursor-move mr-1" />
                )}
                Recent Joinees (Last 5)
              </span>
              {isCustomizing && (
                <WidgetToggle
                  widgetKey="newJoinee"
                  visible={widgetConfigs.newJoinee?.visible}
                  onChange={(checked) =>
                    handleToggleWidget("newJoinee", checked)
                  }
                />
              )}
            </Typography.Title>
            <NewJoinee />
          </div>
        )}

        {/* Kudos Widget */}
        {isVisible("kudos") && (
          <div key="kudos" className={`${boxClass} overflow-hidden`}>
            <KudosDashboard
              isCustomizing={isCustomizing}
              widgetConfigs={widgetConfigs}
              handleToggleWidget={handleToggleWidget}
            />
          </div>
        )}

        {/* Pending WFH Requests Widget */}
        {isVisible("pendingWFH") &&
          isPowerUserWithoutHR &&
          employeeDetails?.country !== "US" && (
            <div key="pendingWFH" className={`${boxClass} overflow-hidden`}>
              <PendingWFHRequestByEmployee isCustomizing={isCustomizing} />
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute mt-3 mr-1">
                  <WidgetToggle
                    widgetKey="pendingWFH"
                    visible={widgetConfigs.pendingWFH?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("pendingWFH", checked)
                    }
                  />
                </div>
              )}
            </div>
          )}

        {/* Leave Dashboard */}
        {isVisible("leaveDashboard") &&
          isPowerUser &&
          employeeDetails?.country !== "US" && (
            <div key="leaveDashboard" className={`${boxClass} overflow-hidden`}>
              <EmployeeLeaveRequestDashBoard isCustomizing={isCustomizing} />
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute z-10 mt-3 mr-1">
                  <WidgetToggle
                    widgetKey="leaveDashboard"
                    visible={widgetConfigs.leaveDashboard?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("leaveDashboard", checked)
                    }
                  />
                </div>
              )}
            </div>
          )}

        {/* Leave Calendar Widget */}
        {isVisible("leaveCalendar") && employeeDetails?.country !== "US" && (
          <div key="leaveCalendar" className={`${boxClass} overflow-hidden`}>
            <DashboardSmallBox title="Leave Calendar">
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute mt-2 mr-2 z-10">
                  <WidgetToggle
                    widgetKey="leaveCalendar"
                    visible={widgetConfigs.leaveCalendar?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("leaveCalendar", checked)
                    }
                  />
                </div>
              )}
              <LeaveCalendarWidget />
            </DashboardSmallBox>
          </div>
        )}

        {/* Interview List */}
        {isVisible("interviewList") &&
          (isPowerUser || employeeDetails?.isInterviewer === "true") && (
            <div key="interviewList" className={`${boxClass} overflow-hidden`}>
              {isCustomizing && (
                <div className="flex justify-end">
                  <WidgetToggle
                    widgetKey="interviewList"
                    visible={widgetConfigs.interviewList?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("interviewList", checked)
                    }
                  />
                </div>
              )}
              <DashboardSmallBox
                title="Interview List"
                isCustomizing={isCustomizing}
              >
                <EmployeeInterviewList
                  isCustomizing={isCustomizing}
                  widgetConfigs={widgetConfigs}
                  handleToggleWidget={handleToggleWidget}
                />
              </DashboardSmallBox>
            </div>
          )}

        {/* People On WFH */}
        {isVisible("peopleOnWFH") && employeeDetails?.country !== "US" && (
          <div key="peopleOnWFH" className={`${boxClass} overflow-hidden`}>
            <DashboardSmallBox
              title="People On WFH"
              isCustomizing={isCustomizing}
            >
              <PeopleOnWFH />
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute mt-2 mr-2">
                  <WidgetToggle
                    widgetKey="peopleOnWFH"
                    visible={widgetConfigs.peopleOnWFH?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("peopleOnWFH", checked)
                    }
                  />
                </div>
              )}
            </DashboardSmallBox>
          </div>
        )}

        {/* People On Leave */}
        {isVisible("peopleOnLeave") && employeeDetails?.country !== "US" && (
          <div key="peopleOnLeave" className={`${boxClass} overflow-hidden`}>
            <DashboardSmallBox
              title="People On Leave"
              isCustomizing={isCustomizing}
            >
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute mt-2 mr-2">
                  <WidgetToggle
                    widgetKey="peopleOnLeave"
                    visible={widgetConfigs.peopleOnLeave?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("peopleOnLeave", checked)
                    }
                  />
                </div>
              )}
              <PeopleOnLeave
                layout={layouts[currentBreakpoint]?.find(
                  (item) => item.i === "peopleOnLeave"
                )}
              />
            </DashboardSmallBox>
          </div>
        )}

        {/* Send Wish */}
        {isVisible("sendWish") && (
          <div key="sendWish" className={`${boxClass} overflow-hidden`}>
            <DashboardSmallBox
              title="Send a Wish"
              isCustomizing={isCustomizing}
            >
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute mt-2 mr-2">
                  <WidgetToggle
                    widgetKey="sendWish"
                    visible={widgetConfigs.sendWish?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("sendWish", checked)
                    }
                  />
                </div>
              )}
              <SendWish />
            </DashboardSmallBox>
          </div>
        )}

        {/* Project Allocation */}
        {isVisible("projectAllocation") && (
          <div
            key="projectAllocation"
            className={`${boxClass} overflow-hidden`}
          >
            <DashboardSmallBox
              title="Assigned Projects"
              isCustomizing={isCustomizing}
            >
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute mt-2 mr-2">
                  <WidgetToggle
                    widgetKey="projectAllocation"
                    visible={widgetConfigs.projectAllocation?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("projectAllocation", checked)
                    }
                  />
                </div>
              )}
              <ProjectAllocation currentEmployee={currentEmployee} />
            </DashboardSmallBox>
          </div>
        )}

        {/* Jira Issues Box */}
        {isVisible("jiraBox") && (
          <div key="jiraBox" className={`${boxClass} overflow-hidden`}>
            <DashboardSmallBox
              title="Issues you are working on (or should be!)"
              isCustomizing={isCustomizing}
            >
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute mt-2 mr-2">
                  <WidgetToggle
                    widgetKey="jiraBox"
                    visible={widgetConfigs.jiraBox?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("jiraBox", checked)
                    }
                  />
                </div>
              )}
              <DashboardJiraBox />
            </DashboardSmallBox>
          </div>
        )}

        {/* Celebrations Widget */}
        {isVisible("celebrations") && (
          <div key="celebrations" className={`${boxClass} overflow-hidden`}>
            <DashboardSmallBox
              title="Celebrations"
              isCustomizing={isCustomizing}
            >
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute mt-2 mr-2">
                  <WidgetToggle
                    widgetKey="celebrations"
                    visible={widgetConfigs.celebrations?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("celebrations", checked)
                    }
                  />
                </div>
              )}
              <CelebrationBoard />
            </DashboardSmallBox>
          </div>
        )}

        {/* Chatbot Widget */}
        {(isHrLogin || isUnitLeader || isExecutiveLogin || isRecruiterLogin) &&
          isVisible("chatbot") && (
            <div key="chatbot" className={`${boxClass} overflow-hidden`}>
              <DashboardSmallBox title="HR Chatbot">
                {isCustomizing && (
                  <div className="flex top-0 right-0 absolute mt-2 mr-2 z-10">
                    <WidgetToggle
                      widgetKey="chatbot"
                      visible={widgetConfigs.chatbot?.visible}
                      onChange={(checked) =>
                        handleToggleWidget("chatbot", checked)
                      }
                    />
                  </div>
                )}
                <ChatbotWidget />
              </DashboardSmallBox>
            </div>
          )}

        {/* Know Your Colleagues Widget */}
        {isVisible("knowYourColleagues") && (
          <div
            key="knowYourColleagues"
            className={`${boxClass} overflow-hidden`}
          >
            <DashboardSmallBox
              title="Know Your Colleagues"
              isCustomizing={isCustomizing}
            >
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute mt-2 mr-2">
                  <WidgetToggle
                    widgetKey="knowYourColleagues"
                    visible={widgetConfigs.knowYourColleagues?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("knowYourColleagues", checked)
                    }
                  />
                </div>
              )}
              <KnowYourColleagues />
            </DashboardSmallBox>
          </div>
        )}

        {isVisible("commercialTieUps") && (
          <div key="commercialTieUps" className={`${boxClass} overflow-hidden`}>
            <DashboardSmallBox
              title="Commercial Tie-Ups"
              isCustomizing={isCustomizing}
            >
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute mt-2 mr-2">
                  <WidgetToggle
                    widgetKey="commercialTieUps"
                    visible={widgetConfigs.commercialTieUps?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("commercialTieUps", checked)
                    }
                  />
                </div>
              )}
              <CommercialTieUps />
            </DashboardSmallBox>
          </div>
        )}
        {isVisible("pendingActionItems") && (
          <div
            key="pendingActionItems"
            className={`${boxClass} overflow-hidden`}
          >
            <DashboardSmallBox
              title="Pending Action Items"
              isCustomizing={isCustomizing}
            >
              {isCustomizing && (
                <div className="flex top-0 right-0 absolute mt-2 mr-2">
                  <WidgetToggle
                    widgetKey="pendingActionItems"
                    visible={widgetConfigs.pendingActionItems?.visible}
                    onChange={(checked) =>
                      handleToggleWidget("pendingActionItems", checked)
                    }
                  />
                </div>
              )}
              <PendingActionItems />
            </DashboardSmallBox>
          </div>
        )}
      </ResponsiveGridLayout>
    </div>
  );
}

/**
 * Reusable dashboard widget container component
 */
function DashboardSmallBox({ title, children, isCustomizing }) {
  return (
    <div className="flex flex-col items-center h-full overflow-auto">
      <div className="relative w-full flex items-center justify-center">
        {isCustomizing && (
          <div className="absolute left-2 dragHandle cursor-move">
            <DragOutlined />
          </div>
        )}
        <Typography.Title
          level={5}
          className="text-center border-b-2 border-green-500 inline-block pb-1"
        >
          {title}
        </Typography.Title>
      </div>
      <div className="flex-1 w-full">{children}</div>
    </div>
  );
}

function enforceMinSize(layouts) {
  const minSizes = {
    announcements: { minW: 6, minH: 8 },
    pendingWFH: { minW: 4, minH: 8 },
    pendingActionItems: { minW: 6, minH: 8 },
    sendWish: { minW: 4, minH: 8 },
    peopleOnLeave: { minW: 4, minH: 8 },
    newJoinee: { minW: 6, minH: 8, maxW: 6 },
  };
  const newLayouts = { ...layouts };
  Object.keys(newLayouts).forEach((breakpoint) => {
    newLayouts[breakpoint] = (newLayouts[breakpoint] || []).map((item) => {
      if (minSizes[item.i]) {
        return { ...item, ...minSizes[item.i] };
      }
      return item;
    });
  });
  return newLayouts;
}
