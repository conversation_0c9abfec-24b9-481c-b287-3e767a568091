import React, { useEffect, useMemo, useRef, useState } from "react";
import { Col, Input, List, message, Row, Skeleton, Tag, Select } from "antd";
import moment from "moment";
import { LeftOutlined, RightOutlined, SearchOutlined } from "@ant-design/icons";
import { DateFormat } from "utils/constants";
import {
  getFestivalLeaves,
  listPeopleOnLeaveTodayAction,
} from "../../utils/Actions";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import useSearchHook from "utils/hooks/useSearchHook";
import { useSelector } from "react-redux";
import {
  getCurrentUserData,
  getEmployeeDetails,
} from "store/slices/loginSlice";
import { ExecuteQueryCustomV2 } from "utils/Api";
import { listEmployeeProjectAllocations } from "graphql/queries";
import { boxClass } from "utils/TailwindCommonClasses";

function PeopleOnLeave() {
  const [selectedDate, setSelectedDate] = useState(moment());
  const [skeletonLoader, setskeletonLoader] = useState(false);
  const [listLeaves, setlistLeaves] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState(() => {
    // Initialize filter type from localStorage or default to "All"
    const savedFilter = localStorage.getItem("peopleOnLeaveFilter");
    return savedFilter || "All";
  });
  const [currentUser, setCurrentUser] = useState(null);
  const [userProjectAllocations, setUserProjectAllocations] = useState([]);
  const [userSquad, setUserSquad] = useState(null);

  // Get current user data
  const userData = useSelector(getCurrentUserData);
  const employeeDetails = useSelector(getEmployeeDetails);
  const listContainerRef = useRef(null);
  const [columnCount, setColumnCount] = useState(1);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const width = entry.contentRect.width;
        if (width >= 800) {
          setColumnCount(4); // xl
        } else if (width >= 600) {
          setColumnCount(3); // lg
        } else if (width >= 500) {
          setColumnCount(2); // md
        } else {
          setColumnCount(1); // sm
        }
      }
    });

    if (listContainerRef.current) {
      observer.observe(listContainerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  // Save filter type to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("peopleOnLeaveFilter", filterType);
  }, [filterType]);

  useEffect(() => {
    if (userData?.email) {
      setCurrentUser(userData);
      setUserSquad(employeeDetails?.squad?.name);
      fetchUserProjectAllocations(userData.email);
    }
  }, [userData, employeeDetails]);

  // Fetch user's project allocations
  const fetchUserProjectAllocations = async (email) => {
    try {
      const filter = {
        employeeEmployee_project_allocationId: { eq: email },
      };
      const result = await ExecuteQueryCustomV2(
        listEmployeeProjectAllocations,
        { filter }
      );
      setUserProjectAllocations(result || []);
    } catch (error) {
      console.error("Error fetching project allocations:", error);
    }
  };

  useEffect(() => {
    handlelistPeopleOnLeaveToday();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedDate]);

  async function handlelistPeopleOnLeaveToday() {
    setskeletonLoader(true);

    try {
      const filter = {
        start_time: { le: moment(selectedDate).startOf("day").format() },
        and: { end_time: { ge: moment(selectedDate).endOf("day").format() } },
      };
      let plannedLeaves = await listPeopleOnLeaveTodayAction(filter);
      plannedLeaves =
        plannedLeaves?.filter((val) => val?.employee?.active === true) || [];

      const processedPlannedLeaves = await Promise.all(
        plannedLeaves.map(processEmployeeLeave)
      );

      const festivalLeaves = await getAllFestivalLeaves();

      const allLeaves = [...processedPlannedLeaves, ...festivalLeaves];

      const uniqueLeaves = allLeaves.reduce((acc, current) => {
        const isEmployeePresent = acc.some(
          (item) => item.employee.employee_id === current.employee.employee_id
        );
        if (!isEmployeePresent) {
          acc.push(current);
        }
        return acc;
      }, []);

      const sortedUniqueLeaves = uniqueLeaves.sort((a, b) =>
        a.employee?.first_name.localeCompare(b.employee?.first_name)
      );
      setlistLeaves(sortedUniqueLeaves);
    } catch (err) {
      console.error(err);
      message.error("Unable to fetch Leaves");
    } finally {
      setskeletonLoader(false);
    }
  }

  async function processEmployeeLeave(employeeLeave) {
    return {
      ...employeeLeave,
      first_name: `${employeeLeave.employee.first_name}`,
      last_name: `${employeeLeave.employee.last_name}`,
      email: employeeLeave?.employeeLeavesId,
    };
  }

  async function getAllFestivalLeaves() {
    const filter = {
      date: { eq: moment(selectedDate).format("YYYY-MM-DD") },
    };
    try {
      let festivalLeaves = await getFestivalLeaves(filter);
      festivalLeaves =
        festivalLeaves?.filter((val) => val?.employee?.active === true) || [];
      return Promise.all(festivalLeaves.map(processEmployeeLeave));
    } catch (err) {
      console.error(err);
      return [];
    }
  }

  const tagColor = useMemo(
    () => ({
      CASUAL: "orange",
      SICK: "red",
      PRIVILEGE: "green",
    }),
    []
  );

  const handleNavigation = (direction) => {
    const newDate = selectedDate.clone().add(direction, "day");
    setSelectedDate(newDate);
  };

  // Filter leaves based on selected filter type and search query
  const filteredLeavesMemo = useMemo(() => {
    let filtered = listLeaves;

    // First apply type filter
    if (filterType === "Team") {
      // Filter by project allocations - show people from same projects
      const userProjectIds = userProjectAllocations.map(
        (allocation) => allocation.project?.id
      );
      filtered = filtered.filter((leave) => {
        // Check if the employee on leave is allocated to any of the user's projects
        return leave.employee?.employee_project_allocation?.items?.some(
          (allocation) => userProjectIds.includes(allocation.project?.id)
        );
      });
    } else if (filterType === "Squad") {
      // Filter by squad - show people from same squad
      filtered = filtered.filter(
        (leave) => leave.employee?.squad?.name === userSquad
      );
    }

    // Then apply search filter
    if (searchQuery) {
      filtered = filtered.filter((item) => {
        const fullName = item?.employee?.first_name + item?.employee?.last_name;
        return fullName?.toLowerCase().includes(searchQuery.toLowerCase());
      });
    }

    return filtered;
  }, [listLeaves, filterType, userProjectAllocations, userSquad, searchQuery]);

  // Filter options
  const filterOptions = [
    { value: "All", label: "All" },
    { value: "Team", label: "My Team" },
    { value: "Squad", label: "My Squad" },
  ];

  // Force re-render when filter changes by adding a key
  const listKey = `${filterType}-${searchQuery}`;

  return (
    <div ref={listContainerRef}>
      <Row
        gutter={15}
        justify="center"
        align="middle"
        className="mb-3 text-center"
      >
        <Col>
          <LeftOutlined
            className="prev-icon"
            onClick={() => handleNavigation(-1)}
          />
        </Col>
        <Col>
          <span className="date-text">
            <b>{selectedDate.format(DateFormat)}</b>
          </span>
        </Col>
        <Col>
          <RightOutlined
            className="next-icon"
            onClick={() => handleNavigation(1)}
          />
        </Col>
      </Row>
      {skeletonLoader ? (
        <Skeleton active />
      ) : (
        <>
          {listLeaves && listLeaves.length > 0 ? (
            <div className="mb-2">
              <Row gutter={[8, 8]} align="middle">
                <Col flex="auto">
                  <Input
                    placeholder="Search people on leave"
                    prefix={<SearchOutlined />}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="people-on-search-input w-100"
                  />
                </Col>
                <Col>
                  <Select
                    value={filterType}
                    onChange={setFilterType}
                    options={filterOptions}
                    style={{ width: 120 }}
                    placeholder="Filter by"
                  />
                </Col>
              </Row>
            </div>
          ) : null}
          <List
            key={listKey}
            itemLayout="horizontal"
            grid={{
              gutter: 15,
              column: columnCount,
            }}
            dataSource={filteredLeavesMemo}
            renderItem={(item) => (
              <List.Item>
                <div className={columnCount > 1 ? `${boxClass} ` : ""}>
                  <List.Item.Meta
                    title={
                      <span className="flex flex-col">
                        <div className="block items-center gap-1">
                          <RenderEmployeeFullName
                            employee={item?.employee}
                            showAvatar={true}
                            avatarSize={25}
                            showPopover
                          />
                        </div>
                        <span className="text-smallest text-gray-500 ml-8">
                          (
                          {item.start_time
                            ? `${moment(item.start_time).format(
                                DateFormat
                              )} - ${moment(item.end_time).format(DateFormat)}`
                            : `${moment(item.date).format(
                                DateFormat
                              )} - ${moment(item.date).format(DateFormat)}`}
                          )
                        </span>
                      </span>
                    }
                    description={
                      <Tag
                        color={tagColor[item?.type] || "grey"}
                        className="text-smallest font-semibold p-1 py-0 ml-8"
                      >
                        {item?.type ? item?.type : item.name}
                      </Tag>
                    }
                  />
                </div>
              </List.Item>
            )}
          />
        </>
      )}
    </div>
  );
}

export default PeopleOnLeave;
