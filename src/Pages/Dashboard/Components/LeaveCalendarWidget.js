import React, { useState, useEffect } from "react";
import {
  Card,
  Calendar,
  Modal,
  List,
  Button,
  Select,
  Spin,
  Typography,
} from "antd";
import moment from "moment";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import { useSelector } from "react-redux";
import {
  getCurrentUserData,
  getEmployeeDetails,
  isExecutive,
  isHr,
  isUnitTeamLeader,
} from "store/slices/loginSlice";
import { ExecuteQueryCustomV2 } from "utils/Api";
import { listEmployeeProjectAllocations } from "graphql/queries";
import { Storage } from "aws-amplify";
import CustomImage from "Commons/CustomImage";
import { getDefaultAvatarImage } from "utils/commonMethods";
import { listAllLeaves } from "Pages/Leave/Actions/LeaveAction";

const { Title } = Typography;

const LeaveCalendarWidget = () => {
  const [filterType, setFilterType] = useState("Team");
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedDateEmployees, setSelectedDateEmployees] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [userProjectAllocations, setUserProjectAllocations] = useState([]);
  const [userSquad, setUserSquad] = useState(null);
  const [allLeaves, setAllLeaves] = useState([]);
  const [leaveLoader, setLeaveLoader] = useState(false);

  // Get current user data
  const userData = useSelector(getCurrentUserData);
  const employeeDetails = useSelector(getEmployeeDetails);
  const isExecutiveLogin = useSelector(isExecutive);
  const isHrLogin = useSelector(isHr);
  const isUnitLeader = useSelector(isUnitTeamLeader);

  // Check if user has permission to see all employees filter
  const canSeeAllEmployeesFilter =
    isHrLogin || isExecutiveLogin || isUnitLeader;

  // Save filter type to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("dashboardLeaveCalendarFilterType", filterType);
  }, [filterType]);

  // Set appropriate default filter based on user permissions
  useEffect(() => {
    if (userData) {
      const savedFilter = localStorage.getItem(
        "dashboardLeaveCalendarFilterType"
      );

      if (canSeeAllEmployeesFilter) {
        // HR/Executive/Unit Leaders can use "All" filter
        if (!savedFilter) {
          setFilterType("All");
        } else if (
          savedFilter === "All" ||
          savedFilter === "Team" ||
          savedFilter === "Squad"
        ) {
          setFilterType(savedFilter);
        } else {
          setFilterType("All");
        }
      } else {
        // Regular users can only use "Team" or "Squad"
        if (!savedFilter || savedFilter === "All") {
          setFilterType("Team");
        } else if (savedFilter === "Team" || savedFilter === "Squad") {
          setFilterType(savedFilter);
        } else {
          setFilterType("Team");
        }
      }
    }
  }, [userData, canSeeAllEmployeesFilter]);

  useEffect(() => {
    if (userData?.email) {
      setCurrentUser(userData);
      setUserSquad(employeeDetails?.squad?.name);
      fetchUserProjectAllocations(userData.email);
    }
  }, [userData, employeeDetails]);

  // Fetch user's project allocations
  const fetchUserProjectAllocations = async (email) => {
    try {
      const filter = {
        employeeEmployee_project_allocationId: { eq: email },
      };
      const result = await ExecuteQueryCustomV2(
        listEmployeeProjectAllocations,
        { filter }
      );
      setUserProjectAllocations(result || []);
    } catch (error) {
      console.error("Error fetching project allocations:", error);
    }
  };

  // Fetch all leaves for calendar
  const fetchAllLeavesForCalendar = async () => {
    setLeaveLoader(true);
    try {
      // Fetch all leaves for the current year
      const currentYear = moment().format("YYYY");
      const filter = {
        start_time: { beginsWith: currentYear },
      };

      const allLeavesData = await listAllLeaves(filter);
      setAllLeaves(allLeavesData || []);
    } catch (error) {
      console.error("Error fetching all leaves:", error);
    } finally {
      setLeaveLoader(false);
    }
  };

  useEffect(() => {
    fetchAllLeavesForCalendar();
  }, []);

  // Filter leaves based on selected filter type
  const filteredLeaves = React.useMemo(() => {
    let filtered = allLeaves;

    // Apply team/squad filter
    if (filterType === "Team") {
      const userProjectIds = userProjectAllocations.map(
        (allocation) => allocation.project?.id
      );
      filtered = filtered.filter((leave) => {
        return leave.employee?.employee_project_allocation?.items?.some(
          (allocation) => userProjectIds.includes(allocation.project?.id)
        );
      });
    } else if (filterType === "Squad") {
      filtered = filtered.filter(
        (leave) => leave.employee?.squad?.name === userSquad
      );
    }
    // "All" filter is only available for HR/Executive/Unit Leaders, so no additional filtering needed

    return filtered;
  }, [allLeaves, filterType, userProjectAllocations, userSquad]);

  // Process leaves for calendar view
  const calendarData = React.useMemo(() => {
    const dateMap = {};

    filteredLeaves.forEach((leave) => {
      if (
        leave.adjustment_type === "DEBIT" &&
        leave.start_time &&
        leave.end_time
      ) {
        const startDate = moment(leave.start_time);
        const endDate = moment(leave.end_time);

        // Generate all dates in the leave range
        const currentDate = startDate.clone();
        while (currentDate.isSameOrBefore(endDate)) {
          const dateKey = currentDate.format("YYYY-MM-DD");
          if (!dateMap[dateKey]) {
            dateMap[dateKey] = [];
          }
          dateMap[dateKey].push(leave);
          currentDate.add(1, "day");
        }
      }
    });

    return dateMap;
  }, [filteredLeaves]);

  // Get background color based on leave count
  const getBackgroundColor = (count) => {
    if (count === 0) return "transparent";

    if (count <= 2) {
      return "#f6ffed"; // light green
    } else if (count <= 5) {
      return "#fffbe6"; // light yellow
    } else {
      return "#fff2f0"; // light red
    }
  };

  // Calendar date cell renderer
  const dateCellRender = (value) => {
    const dateKey = value.format("YYYY-MM-DD");
    const dayLeaves = calendarData[dateKey] || [];
    const uniqueEmployees = dayLeaves.reduce((acc, leave) => {
      const email = leave.employee?.email;
      if (!acc.find((emp) => emp.email === email)) {
        acc.push(leave.employee);
      }
      return acc;
    }, []);

    return (
      <div
        style={{
          position: "relative",
          height: "100%",
          backgroundColor: getBackgroundColor(uniqueEmployees.length),
          borderRadius: "4px",
          padding: "2px",
        }}
      >
        {uniqueEmployees.length > 0 && (
          <div
            style={{
              position: "absolute",
              top: "2px",
              left: "2px",
              right: "2px",
              display: "flex",
              flexWrap: "wrap",
              gap: "1px",
            }}
          >
            {uniqueEmployees.slice(0, 4).map((employee, index) => (
              <div
                key={employee.email}
                style={{
                  width: "30px",
                  height: "30px",
                  borderRadius: "50%",
                  overflow: "hidden",
                  border: "1px solid #fff",
                }}
              >
                <CustomImage
                  S3Key={employee.profile_pic}
                  src={getDefaultAvatarImage(
                    employee.first_name,
                    employee.last_name
                  )}
                  className="w-full h-full object-cover rounded-full"
                  imgClassName="w-full h-full object-cover rounded-full"
                />
              </div>
            ))}
            {uniqueEmployees.length > 4 && (
              <div
                style={{
                  width: "30px",
                  height: "30px",
                  borderRadius: "50%",
                  backgroundColor: "#1890ff",
                  color: "white",
                  fontSize: "10px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  border: "1px solid #fff",
                  fontWeight: "bold",
                }}
              >
                +{uniqueEmployees.length - 4}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Handle date click
  const onDateSelect = (value) => {
    const dateKey = value.format("YYYY-MM-DD");
    const dayLeaves = calendarData[dateKey] || [];
    const uniqueEmployees = dayLeaves.reduce((acc, leave) => {
      const email = leave.employee?.email;
      if (!acc.find((emp) => emp.email === email)) {
        acc.push(leave.employee);
      }
      return acc;
    }, []);

    setSelectedDate(dateKey);
    setSelectedDateEmployees(uniqueEmployees);
    setIsModalVisible(true);
  };

  // Filter options
  const filterOptions = canSeeAllEmployeesFilter
    ? [
        { value: "All", label: "All" },
        { value: "Team", label: "My Projects" },
        { value: "Squad", label: "My Squad" },
      ]
    : [
        { value: "Team", label: "My Projects" },
        { value: "Squad", label: "My Squad" },
      ];

  return (
    <div>
      <style>
        {`
          .leave-calendar-widget .ant-picker-calendar-date {
            border: 1px solid #f0f0f0 !important;
            cursor: pointer !important;
            min-height: 80px !important;
            min-width: 80px !important;
            width: 100% !important;
            position: relative !important;
            display: flex !important;
            flex-direction: column !important;
          }
          .leave-calendar-widget .ant-picker-calendar-date:hover {
            background-color: #f5f5f5 !important;
          }
          .leave-calendar-widget .ant-picker-calendar-date-content {
            height: 100% !important;
            min-height: 80px !important;
            width: 100% !important;
            flex: 1 !important;
          }
          .leave-calendar-widget .ant-picker-calendar-date-value {
            position: absolute !important;
            bottom: 4px !important;
            right: 4px !important;
            z-index: 1 !important;
          }
          .leave-calendar-widget .ant-picker-calendar-date-today {
            border-color: #1890ff !important;
          }
          .leave-calendar-widget .ant-picker-calendar-date-selected {
            background-color: #e6f7ff !important;
            border-color: #1890ff !important;
          }
          .leave-calendar-widget .ant-picker-calendar-date-today.ant-picker-calendar-date-selected {
            background-color: #e6f7ff !important;
          }
        `}
      </style>

      {/* Controls and Legend */}
      <div
        style={{
          marginBottom: 16,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          flexWrap: "wrap",
          gap: "8px",
        }}
      >
        <Select
          value={filterType}
          onChange={setFilterType}
          options={filterOptions}
          style={{ width: 120 }}
          placeholder="Filter by"
        />

        <div
          style={{
            fontSize: "12px",
            color: "#666",
            display: "flex",
            gap: "8px",
            flexWrap: "wrap",
          }}
        >
          <span style={{ display: "inline-flex", alignItems: "center" }}>
            <span
              style={{
                display: "inline-block",
                width: "12px",
                height: "12px",
                backgroundColor: "#f6ffed",
                border: "1px solid #d9d9d9",
                marginRight: 4,
              }}
            ></span>
            ≤2
          </span>
          <span style={{ display: "inline-flex", alignItems: "center" }}>
            <span
              style={{
                display: "inline-block",
                width: "12px",
                height: "12px",
                backgroundColor: "#fffbe6",
                border: "1px solid #d9d9d9",
                marginRight: 4,
              }}
            ></span>
            3-5
          </span>
          <span style={{ display: "inline-flex", alignItems: "center" }}>
            <span
              style={{
                display: "inline-block",
                width: "12px",
                height: "12px",
                backgroundColor: "#fff2f0",
                border: "1px solid #d9d9d9",
                marginRight: 4,
              }}
            ></span>
            &gt;5
          </span>
          <span style={{ display: "inline-flex", alignItems: "center" }}>
            <span
              style={{
                display: "inline-block",
                width: "10px",
                height: "10px",
                borderRadius: "50%",
                backgroundColor: "#1890ff",
                color: "white",
                fontSize: "6px",
                textAlign: "center",
                lineHeight: "10px",
                marginRight: 4,
              }}
            >
              +
            </span>
            +N
          </span>
        </div>
      </div>

      {/* Calendar View */}
      <div>
        <Spin spinning={leaveLoader}>
          <Calendar
            dateCellRender={dateCellRender}
            onSelect={onDateSelect}
            style={{
              backgroundColor: "white",
              padding: 16,
              borderRadius: 8,
              border: "1px solid #d9d9d9",
            }}
            fullscreen={false}
            className="leave-calendar-widget"
            headerRender={({ value, type, onChange, onTypeChange }) => {
              const current = value.month();
              const months = [
                "Jan",
                "Feb",
                "Mar",
                "Apr",
                "May",
                "Jun",
                "Jul",
                "Aug",
                "Sep",
                "Oct",
                "Nov",
                "Dec",
              ];

              return (
                <div style={{ padding: "8px 0" }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                      }}
                    >
                      <button
                        type="button"
                        onClick={() => {
                          const now = value.clone().month(current - 1);
                          onChange(now);
                        }}
                        style={{
                          border: "none",
                          background: "none",
                          cursor: "pointer",
                          padding: "4px 8px",
                          borderRadius: "4px",
                          fontSize: "14px",
                        }}
                      >
                        ‹
                      </button>
                      <span style={{ fontSize: "16px", fontWeight: "bold" }}>
                        {months[current]} {value.year()}
                      </span>
                      <button
                        type="button"
                        onClick={() => {
                          const now = value.clone().month(current + 1);
                          onChange(now);
                        }}
                        style={{
                          border: "none",
                          background: "none",
                          cursor: "pointer",
                          padding: "4px 8px",
                          borderRadius: "4px",
                          fontSize: "14px",
                        }}
                      >
                        ›
                      </button>
                    </div>
                  </div>
                </div>
              );
            }}
          />
        </Spin>
      </div>

      {/* Employee Details Modal */}
      <Modal
        title={`Employees on Leave - ${
          selectedDate ? moment(selectedDate).format("DD MMM YYYY") : ""
        }`}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsModalVisible(false)}>
            Close
          </Button>,
        ]}
        width={600}
      >
        {selectedDateEmployees.length > 0 ? (
          <List
            dataSource={selectedDateEmployees}
            renderItem={(employee) => (
              <List.Item>
                <RenderEmployeeFullName
                  employee={employee}
                  showAvatar={true}
                  avatarSize={32}
                  showPopover
                />
              </List.Item>
            )}
          />
        ) : (
          <p>No employees on leave for this date.</p>
        )}
      </Modal>
    </div>
  );
};

export default LeaveCalendarWidget;
