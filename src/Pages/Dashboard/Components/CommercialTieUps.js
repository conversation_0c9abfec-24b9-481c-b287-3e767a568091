import React, { useEffect, useState } from "react";
import { Carousel, Card, Modal } from "antd";
import { primaryColor } from "../../../theme";
import { Storage } from "aws-amplify";

const commercialTieUps = [
  {
    id: 1,
    title: "Arham Living - PG for Boys",
    imageUrl: "tieups/1.jpg",
    description:
      "Enjoy premium co-living at Luxurious Executive PG for Boys, featuring furnished rooms, meals, kitchen access, housekeeping, and secure premises. Exclusive discounted rates for York IE employees.",
  },
  {
    id: 4,
    title: "Shivam Girls PG",
    imageUrl: "tieups/4.jpg",
    description:
      "Comfortable and secure PG living near Kabir Enclave, Bopal. York IE employees enjoy 15% off on fully furnished AC/Non-AC rooms with single and double sharing options.",
  },
  {
    id: 2,
    title: "FitTreat Gymnasium",
    imageUrl: "tieups/2.jpg",
    description:
      "Unlock peak performance at FitTreat Gymnasium with professional-grade equipment and expert training.",
  },
  {
    id: 3,
    title: "SFW Gym",
    imageUrl: "tieups/3.png",
    description:
      "Achieve your fitness goals with top-tier training at SFW Gym. York IE employees enjoy exclusive membership access at special rates.",
  },
  {
    id: 5,
    title: "AT Salon",
    imageUrl: "tieups/5.jpg",
    description:
      "Pamper yourself at AT Salon, South Bopal, with an exclusive 20% discount on all services for York IE employees—haircuts, facials, nails, and more. Call 9316710432 to book!",
  },
];

const CustomArrow = ({ type, onClick }) => {
  const arrowStyle = {
    position: "absolute",
    top: "50%",
    transform: "translateY(-50%)",
    zIndex: 1,
    cursor: "pointer",
    color: primaryColor,
    fontSize: "24px",
    background: "transparent",
    border: "none",
    padding: "10px",
  };

  return (
    <button
      style={{
        ...arrowStyle,
        [type === "prev" ? "left" : "right"]: "10px",
      }}
      onClick={onClick}
    >
      {type === "prev" ? "←" : "→"}
    </button>
  );
};

// Helper hook to fetch S3 image URLs
function useTieUpImages(tieUps) {
  const [imageUrls, setImageUrls] = useState({});

  useEffect(() => {
    let isMounted = true;
    async function fetchImages() {
      const urls = {};
      await Promise.all(
        tieUps.map(async (tieUp) => {
          if (tieUp.imageUrl) {
            try {
              const url = await Storage.get(tieUp.imageUrl, {
                level: "public",
              });
              urls[tieUp.id] = url;
            } catch {
              urls[tieUp.id] = undefined;
            }
          }
        })
      );
      if (isMounted) setImageUrls(urls);
    }
    fetchImages();
    return () => {
      isMounted = false;
    };
  }, [tieUps]);

  return imageUrls;
}

const CommercialTieUps = () => {
  const imageUrls = useTieUpImages(commercialTieUps);

  // State for image preview
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const [previewTitle, setPreviewTitle] = useState("");

  const handleImageClick = (imgUrl, title) => {
    setPreviewImage(imgUrl);
    setPreviewTitle(title);
    setPreviewVisible(true);
  };

  const handlePreviewClose = () => {
    setPreviewVisible(false);
    setPreviewImage(null);
    setPreviewTitle("");
  };

  return (
    <Card>
      <div style={{ position: "relative", paddingBottom: "30px" }}>
        <Carousel
          autoplay
          dots={true}
          arrows={true}
          dotPosition="bottom"
          prevArrow={<CustomArrow type="prev" />}
          nextArrow={<CustomArrow type="next" />}
        >
          {commercialTieUps.map((tieUp) => (
            <div key={tieUp.id}>
              <div
                style={{
                  height: "200px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  background: "#f5f5f5",
                  borderRadius: "8px",
                  overflow: "hidden",
                  cursor: imageUrls[tieUp.id] ? "pointer" : "default",
                }}
                onClick={() =>
                  imageUrls[tieUp.id] &&
                  handleImageClick(imageUrls[tieUp.id], tieUp.title)
                }
                title={imageUrls[tieUp.id] ? "Click to preview" : ""}
              >
                <img
                  src={imageUrls[tieUp.id]}
                  alt={tieUp.title}
                  style={{
                    maxWidth: "100%",
                    maxHeight: "100%",
                    objectFit: "contain",
                    pointerEvents: "none", // Prevent drag, but allow parent div click
                  }}
                  draggable={false}
                />
              </div>
              <h3 style={{ marginTop: "12px", textAlign: "center" }}>
                {tieUp.title}
              </h3>
              <p style={{ textAlign: "center", color: "#666" }}>
                {tieUp.description}
              </p>
            </div>
          ))}
        </Carousel>
        <style jsx global>{`
          .ant-carousel .slick-dots {
            bottom: -30px;
          }
          .ant-carousel .slick-dots li button {
            background: #ccc;
            opacity: 1;
          }
          .ant-carousel .slick-dots li.slick-active button {
            background: ${primaryColor};
            opacity: 1;
          }
        `}</style>
        <Modal
          open={previewVisible}
          footer={null}
          onCancel={handlePreviewClose}
          centered
          width={600}
          bodyStyle={{ padding: 0, textAlign: "center" }}
          title={previewTitle}
        >
          <img
            alt={previewTitle}
            style={{
              width: "100%",
              maxHeight: "70vh",
              objectFit: "contain",
              background: "#f5f5f5",
            }}
            src={previewImage}
          />
        </Modal>
      </div>
    </Card>
  );
};

export default CommercialTieUps;
