import { CheckOutlined, CloseOutlined, DragOutlined } from "@ant-design/icons";
import { Button, Card, Empty, Input, message, Modal, Typography } from "antd";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import Loader from "Commons/Loader";
import moment from "moment";
import {
  listWFHPendingRequestForDashboardModuleDataAction,
  updateWorkFromHomeRequestForDashboardAction,
} from "Pages/Dashboard/Actions/PendingWFHRequestAction";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import {
  getEmployeeDetails,
  isExecutive,
  isSquadLeader,
  isUnitTeamLeader,
  isHr,
} from "store/slices/loginSlice";
import { ListEmployeesCustomAction } from "utils/Actions";
import { LongText } from "utils/commonMethods";
import { DateFormat } from "utils/constants";
import { boxClass } from "utils/TailwindCommonClasses";

const { Text } = Typography;
const { TextArea } = Input;

export default function PendingWFHRequestByEmployee({ isCustomizing }) {
  const employeeDetails = useSelector(getEmployeeDetails);
  const {
    userData: { email },
    userReportees,
  } = useSelector((state) => state?.loginReducer);
  const isSquadLeaderLogin = useSelector(isSquadLeader);
  const isExecutiveLogin = useSelector(isExecutive);
  const isUnitLeader = useSelector(isUnitTeamLeader);
  const isHrLogin = useSelector(isHr);
  const currentEmployee = useSelector(getEmployeeDetails);
  const [processingId, setProcessingId] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalComment, setModalComment] = useState("");
  const [currentRequest, setCurrentRequest] = useState(null);
  const [actionType, setActionType] = useState("");
  const [submitLoading, setSubmitLoading] = useState(false);
  const [commentError, setCommentError] = useState(false);
  const [squadReporteesEmails, setSquadReporteesEmails] = useState([]);
  const [isLoadingWFHRequests, setIsLoadingWFHRequests] = useState(false);
  const [pendingWFHRequests, setPendingWFHRequests] = useState([]);

  const isPowerUserWithHR = useMemo(
    () => isExecutiveLogin || isUnitLeader || isSquadLeaderLogin || isHrLogin,
    [isExecutiveLogin, isUnitLeader, isSquadLeaderLogin, isHrLogin]
  );
  const isManager = useMemo(
    () => isExecutiveLogin || isUnitLeader || isHrLogin,
    [isExecutiveLogin, isUnitLeader, isHrLogin]
  );
  /**
   * Collect all reportee emails recursively
   */
  const collectReporteeEmails = useCallback((employees, emails = []) => {
    employees.forEach((emp) => {
      if (emp?.email) emails.push(emp.email);
      if (emp?.reportees?.length) {
        collectReporteeEmails(emp.reportees, emails);
      }
    });
    return emails;
  }, []);

  /**
   * Get squad reportees emails for squad leaders
   */
  useEffect(() => {
    if (isSquadLeaderLogin && userReportees?.length > 0) {
      const emails = collectReporteeEmails(userReportees);
      setSquadReporteesEmails(emails);
    }
  }, [isSquadLeaderLogin, userReportees, collectReporteeEmails]);

  /**
   * Fetch pending WFH requests based on user role
   */
  const fetchPendingWFHRequests = useCallback(async () => {
    if (!isPowerUserWithHR) return;

    try {
      setIsLoadingWFHRequests(true);
      let filter = null;

      if (isManager) {
        filter = { status: { eq: "SUBMITTED" } };
      } else if (
        isSquadLeaderLogin &&
        // squadReporteesEmails.length &&
        currentEmployee?.employeeSquadId
      ) {
        const squadFilter = {
          and: [
            { employeeSquadId: { eq: currentEmployee.employeeSquadId } },
            { hidden_profile: { ne: true } },
            { active: { ne: false } },
          ],
        };

        const squadEmployees = await ListEmployeesCustomAction(squadFilter);
        const emails = [
          ...new Set([
            ...squadEmployees.map((e) => e.email).filter((e) => e !== email),
            ...squadReporteesEmails,
          ]),
        ];

        filter = {
          and: [
            { status: { eq: "SUBMITTED" } },
            { or: emails.map((e) => ({ employeeId: { eq: e } })) },
          ],
        };
      }
      if (filter) {
        const response =
          await listWFHPendingRequestForDashboardModuleDataAction(filter);
        setPendingWFHRequests(response);
      }
    } catch (err) {
      console.error("Error fetching WFH requests:", err);
      message.error("Failed to load WFH requests");
    } finally {
      setIsLoadingWFHRequests(false);
    }
  }, [
    isPowerUserWithHR,
    isManager,
    isSquadLeaderLogin,
    squadReporteesEmails,
    currentEmployee?.employeeSquadId,
    email,
  ]);

  // Fetch WFH requests when dependencies change
  useEffect(() => {
    fetchPendingWFHRequests();
  }, [fetchPendingWFHRequests]);

  // Helper to determine if request is for single or multiple days
  const getFormattedDateRange = (start, end) => {
    const startDate = moment(start).format(DateFormat);
    const endDate = end ? moment(end).format(DateFormat) : null;

    if (!end || moment(start).isSame(end, "day")) return startDate;
    return `${startDate} - ${endDate}`;
  };

  const getWfhLengthLabel = (wfhLength) => {
    const labelMap = {
      FIRST_HALF: "First Half",
      SECOND_HALF: "Second Half",
      FULL_DAY: "Full Day",
    };
    return labelMap[wfhLength] || wfhLength;
  };

  const openModal = (request, action) => {
    setCurrentRequest(request);
    setActionType(action);
    setModalComment("");
    setIsModalVisible(true);
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    setCurrentRequest(null);
    setActionType("");
    setModalComment("");
    setCommentError(false);
  };

  const handleModalOk = async () => {
    if (!currentRequest || !actionType) return;
    if (!modalComment.trim()) {
      setCommentError(true);
      return;
    }

    try {
      setSubmitLoading(true);
      setProcessingId(currentRequest.id);
      setIsModalVisible(false);

      const input = {
        id: currentRequest.id,
        status: actionType,
        reviewedBy: employeeDetails?.email,
        comments: JSON.stringify({
          text: modalComment.trim(),
          timestamp: moment(),
        }),
      };

      await updateWorkFromHomeRequestForDashboardAction(input);

      setPendingWFHRequests((prev) =>
        prev.filter((item) => item.id !== currentRequest.id)
      );

      message.success(
        `WFH request for ${
          currentRequest.employee.first_name
        } has been ${actionType.toLowerCase()}`
      );
    } catch (error) {
      console.error(`Error ${actionType.toLowerCase()} WFH request:`, error);
      message.error(`Failed to ${actionType.toLowerCase()} the request`);
    } finally {
      setProcessingId(null);
      setCurrentRequest(null);
      setActionType("");
      setModalComment("");
      setSubmitLoading(false);
      setCommentError(false);
    }
  };

  const renderModal = () => (
    <Modal
      title={`${actionType === "APPROVED" ? "Approve" : "Reject"} WFH Request`}
      open={isModalVisible}
      onCancel={handleModalCancel}
      footer={null}
    >
      <div className="mb-4">
        <p>
          Please provide a comment for this decision:{" "}
          <span className="text-red-500">*</span>
        </p>
        <TextArea
          rows={4}
          placeholder={`Why are you ${
            actionType === "APPROVED" ? "approving" : "rejecting"
          } this request?`}
          value={modalComment}
          onChange={(e) => {
            setModalComment(e.target.value);
            if (e.target.value.trim()) setCommentError(false);
          }}
          className={`mt-2 ${commentError ? "border-red-500" : ""}`}
          status={commentError ? "error" : ""}
        />
        {commentError && (
          <div className="text-red-500 text-sm mt-1">
            Please provide a comment for your decision
          </div>
        )}
      </div>
      <div className="flex justify-end space-x-3">
        <Button onClick={handleModalCancel} disabled={submitLoading}>
          Cancel
        </Button>
        <Button onClick={handleModalOk} type="primary" loading={submitLoading}>
          {actionType === "APPROVED" ? "Approve" : "Reject"}
        </Button>
      </div>
    </Modal>
  );
  if (isLoadingWFHRequests) return <Loader title="Loading WFH Requests..." />;

  return (
    <div className={`w-full flex flex-col h-full `}>
      <div className="p-2 flex-none">
        <Text className="flex font-semibold">
          {isCustomizing && (
            <DragOutlined className="dragHandle cursor-move mr-1" />
          )}
          WFH Requests to approve
        </Text>
      </div>

      <div className="flex-grow min-h-0">
        {pendingWFHRequests?.length > 0 ? (
          <div className="h-full overflow-y-auto">
            <div className="gap-3 p-2  grid grid-cols-[repeat(auto-fit,minmax(200px,1fr))] auto-rows-min sm:gap-4 sm:grid-cols-[repeat(auto-fit,minmax(220px,1fr))]">
              {pendingWFHRequests
                .slice()
                .sort((a, b) => new Date(b.startDate) - new Date(a.startDate))
                .map((request) => {
                  const {
                    id,
                    employee,
                    startDate,
                    endDate,
                    wfhLength,
                    reason,
                  } = request;

                  return (
                    <Card
                      key={id}
                      className={`bg-gray-50 shadow-sm hover:shadow-md transition-shadow relative min-w-0 ${
                        pendingWFHRequests.length === 1 ? "max-w-sm w-full" : ""
                      }`}
                      bordered={false}
                    >
                      {processingId === id && (
                        <div className="absolute inset-0 bg-gray-200 bg-opacity-60 flex items-center justify-center z-10 rounded">
                          <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-primary"></div>
                        </div>
                      )}

                      <div className="flex flex-col min-w-0">
                        <div className="flex justify-between items-center mb-2 min-w-0">
                          <div className="min-w-0 flex-1 mr-2">
                            <RenderEmployeeFullName
                              employee={employee}
                              showAvatar
                              avatarSize={20}
                              className="m-0 gap-1 sm:gap-2 text-sm sm:text-base"
                              showPopover
                            />
                          </div>
                          <div className="flex space-x-2 sm:space-x-3 flex-shrink-0">
                            <CheckOutlined
                              onClick={() => openModal(request, "APPROVED")}
                              className="text-green-600 text-base sm:text-lg cursor-pointer"
                            />
                            <CloseOutlined
                              onClick={() => openModal(request, "REJECTED")}
                              className="text-red-600 text-base sm:text-lg cursor-pointer"
                            />
                          </div>
                        </div>

                        {/* Created At Date */}
                        {request.createdAt && (
                          <div className="text-xs text-gray-400 mb-1 truncate">
                            Created:{" "}
                            {moment(request.createdAt).format(
                              "DD MMM YYYY, hh:mm A"
                            )}
                          </div>
                        )}

                        <div className="min-w-0">
                          <Text className="text-gray-800 text-sm sm:text-base block truncate">
                            {getFormattedDateRange(startDate, endDate)}
                          </Text>
                          <div className="text-xs text-gray-500 mt-0.5 truncate">
                            {getWfhLengthLabel(wfhLength)}
                          </div>
                        </div>

                        <div className="mt-2 min-w-0">
                          <Text type="secondary" className="text-xs">
                            Reason
                          </Text>
                          <div className="text-sm text-gray-700 min-w-0">
                            <LongText text={reason} limit={80} />
                          </div>
                        </div>
                      </div>
                    </Card>
                  );
                })}
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <Empty description="No pending WFH requests" className="p-4" />
          </div>
        )}
      </div>

      {renderModal()}
    </div>
  );
}
