import React, { useState, useEffect, useRef } from "react";
import {
  Button,
  Card,
  Input,
  Modal,
  message,
  Typography,
  List,
  Avatar,
  Select,
  Popconfirm,
  Badge,
} from "antd";
import {
  GiftOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import moment from "moment";
import { useSelector } from "react-redux";
import { ExecuteMutationV2, ExecuteQueryCustomV2 } from "utils/Api";
import {
  createCelebration,
  updateCelebration,
  deleteCelebration,
} from "graphql/customMutation";
import { listCelebrations } from "graphql/customQueries";
import { boxClass } from "utils/TailwindCommonClasses";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import { Storage } from "aws-amplify";
import "./CelebrationBoard.css";
import {
  createAndUpdateReactionAction,
  deleteReactionsAction,
  getReactionsByReactableIdAction,
} from "Pages/Dashboard/Actions/NoticeBoardAction";
import EmojiReactionPicker, {
  EmojiReactionTooltip,
} from "Commons/EmojiReactionPicker";

const { TextArea } = Input;
const { Title } = Typography;
const { Option } = Select;

const CELEBRATION_TYPES = [
  { value: "WEDDING", label: "Wedding" },
  { value: "ENGAGEMENT", label: "Engagement" },
  { value: "NEW_HOME", label: "New Home" },
  { value: "BABY_BOY", label: "New Baby Boy" },
  { value: "BABY_GIRL", label: "New Baby Girl" },
  { value: "PROMOTION", label: "Promotion" },
  { value: "ACHIEVEMENT", label: "Achievement" },
  { value: "CERTIFICATION", label: "Certification" },
  { value: "PROJECT_COMPLETION", label: "Project Completion" },
  { value: "AWARD", label: "Award" },
  { value: "TEAM_SUCCESS", label: "Team Success" },
  { value: "PROMINENT_CLIENT_WIN", label: "Prominent Client Win" },
  { value: "OTHER", label: "Other" },
];

const ProfileAvatar = ({ profilePic }) => {
  const [avatarUrl, setAvatarUrl] = useState(null);

  useEffect(() => {
    let isMounted = true;
    if (profilePic) {
      Storage.get(profilePic)
        .then((url) => {
          if (isMounted) setAvatarUrl(url);
        })
        .catch(() => {
          if (isMounted) setAvatarUrl(null);
        });
    } else {
      setAvatarUrl(null);
    }
    return () => {
      isMounted = false;
    };
  }, [profilePic]);

  return <Avatar src={avatarUrl} size={40} className="mr-3 shrink-0 " />;
};

const CelebrationBoard = () => {
  const [celebrations, setCelebrations] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [celebrationType, setCelebrationType] = useState("");
  const [celebrationNote, setCelebrationNote] = useState("");
  const [customCelebrationType, setCustomCelebrationType] = useState("");
  const [editingCelebration, setEditingCelebration] = useState(null);
  const [processingReactions, setProcessingReactions] = useState({});
  const [confettiCelebrationId, setConfettiCelebrationId] = useState(null);

  const { userData } = useSelector((state) => state.loginReducer);
  const employeeEmail = userData?.email;
  const listContainerRef = useRef(null);
  const [columnCount, setColumnCount] = useState(1);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const width = entry.contentRect.width;
        if (width >= 800) {
          setColumnCount(3);
        } else if (width >= 500) {
          setColumnCount(2); 
        } else {
          setColumnCount(1); 
        }
      }
    });

    if (listContainerRef.current) {
      observer.observe(listContainerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    if (employeeEmail) {
      fetchCelebrations();
    }
  }, [employeeEmail]);

  const fetchCelebrations = async () => {
    try {
      setLoading(true);
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const response = await ExecuteQueryCustomV2(listCelebrations, {
        limit: 10,
      });

      if (response) {
        const recentCelebrations = response.filter((celebration) => {
          const celebrationDate = new Date(celebration.createdAt);
          return celebrationDate >= thirtyDaysAgo;
        });
        const celebrationsWithReactions = await Promise.all(
          recentCelebrations.map(async (celebration) => {
            const reactions = await getReactionsByReactableIdAction(
              celebration.id
            );
            console.log("All reactions:", reactions);
            const reactionMap = {};
            if (reactions) {
              reactions.forEach((r) => {
                if (!reactionMap[r.reaction]) {
                  reactionMap[r.reaction] = { count: 0, employees: [] };
                }
                reactionMap[r.reaction].count += 1;
                reactionMap[r.reaction].employees.push({
                  email: r.employee,
                  name: `${r.employeeDetails?.first_name || ""} ${
                    r.employeeDetails?.last_name || ""
                  }`.trim(),
                });
              });
            }
            return { ...celebration, reactions: reactionMap };
          })
        );

        // Sort celebrations by updatedAt in descending order
        const sortedCelebrations = celebrationsWithReactions.sort((a, b) => {
          const dateA = new Date(a.updatedAt || a.createdAt);
          const dateB = new Date(b.updatedAt || b.createdAt);
          return dateB - dateA;
        });

        setCelebrations(sortedCelebrations);
      } else {
        setCelebrations([]);
      }
    } catch (error) {
      console.error("Error fetching celebrations:", error);
      message.error("Failed to load celebrations");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!employeeEmail) {
      message.error("User email not found. Please try logging in again.");
      return;
    }

    if (!celebrationType) {
      message.error("Please select a celebration type");
      return;
    }

    if (celebrationType === "OTHER" && !customCelebrationType.trim()) {
      message.error("Please enter the celebration type");
      return;
    }

    try {
      setLoading(true);
      const input = {
        title:
          celebrationType === "OTHER"
            ? customCelebrationType
            : CELEBRATION_TYPES.find((type) => type.value === celebrationType)
                ?.label,
        note: celebrationNote,
        employeeEmail: employeeEmail,
        createdAt: new Date().toISOString(),
      };

      let response;
      if (editingCelebration) {
        response = await ExecuteMutationV2(updateCelebration, {
          input: {
            id: editingCelebration.id,
            ...input,
          },
        });
        if (response.errors) {
          throw new Error(response.errors[0].message);
        }
        message.success("Celebration updated successfully!");
      } else {
        response = await ExecuteMutationV2(createCelebration, { input });
        if (response.errors) {
          throw new Error(response.errors[0].message);
        }
        message.success("Celebration shared successfully!");
      }

      setIsModalOpen(false);
      resetForm();
      await fetchCelebrations();
    } catch (error) {
      console.error("Error saving celebration:", error);
      message.error(
        `Failed to ${editingCelebration ? "update" : "share"} celebration: ${
          error.message
        }`
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (celebrationId) => {
    try {
      setLoading(true);
      const response = await ExecuteMutationV2(deleteCelebration, {
        input: { id: celebrationId },
      });
      if (response.errors) {
        throw new Error(response.errors[0].message);
      }
      message.success("Celebration deleted successfully!");
      await fetchCelebrations();
    } catch (error) {
      console.error("Error deleting celebration:", error);
      message.error("Failed to delete celebration");
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (celebration) => {
    const type =
      CELEBRATION_TYPES.find((t) => t.label === celebration.title)?.value ||
      "OTHER";
    setCelebrationType(type);
    if (type === "OTHER") {
      setCustomCelebrationType(celebration.title);
    }
    setCelebrationNote(celebration.note || "");
    setEditingCelebration(celebration);
    setIsModalOpen(true);
  };

  const resetForm = () => {
    setCelebrationType("");
    setCelebrationNote("");
    setCustomCelebrationType("");
    setEditingCelebration(null);
  };

  const handleCelebrationTypeChange = (value) => {
    setCelebrationType(value);
    if (value !== "OTHER") {
      setCustomCelebrationType("");
    }
  };

  const isOwnCelebration = (celebration) => {
    return celebration?.employeeEmail === employeeEmail;
  };

  const handleEmojiReaction = async (celebrationId, emoji = "🎉") => {
    if (processingReactions[celebrationId]) return;
    setProcessingReactions((prev) => ({ ...prev, [celebrationId]: true }));
    try {
      const celebrationIndex = celebrations.findIndex(
        (c) => c.id === celebrationId
      );
      if (celebrationIndex === -1) return;
      const celebration = celebrations[celebrationIndex];
      const reactions = celebration.reactions || {};
      const hasReacted = reactions[emoji]?.employees?.some(
        (emp) => emp.email === employeeEmail
      );
      let updatedReactions = { ...reactions };
      let revertUpdate = false;
      if (!hasReacted) {
        updatedReactions[emoji] = updatedReactions[emoji] || {
          count: 0,
          employees: [],
        };
        updatedReactions[emoji].count += 1;
        updatedReactions[emoji].employees = [
          ...updatedReactions[emoji].employees,
          {
            email: employeeEmail,
            name: userData?.first_name + " " + userData?.last_name,
          },
        ];
        setCelebrations((prev) => {
          const newCelebrations = [...prev];
          newCelebrations[celebrationIndex] = {
            ...celebration,
            reactions: updatedReactions,
          };
          return newCelebrations;
        });
        const input = {
          reactableId: celebrationId,
          reactableType: "Kudos",
          reaction: emoji,
          employee: employeeEmail,
        };
        console.log("Reaction mutation input:", input);
        const response = await createAndUpdateReactionAction(input);
        console.log("Create reaction response:", response);
        if (response?.errors) {
          revertUpdate = true;
          throw new Error(response.errors[0].message);
        }
      } else {
        updatedReactions[emoji].count -= 1;
        updatedReactions[emoji].employees = updatedReactions[
          emoji
        ].employees.filter((emp) => emp.email !== employeeEmail);
        if (updatedReactions[emoji].count === 0) {
          delete updatedReactions[emoji];
        }
        setCelebrations((prev) => {
          const newCelebrations = [...prev];
          newCelebrations[celebrationIndex] = {
            ...celebration,
            reactions: updatedReactions,
          };
          return newCelebrations;
        });

        const allReactions = await getReactionsByReactableIdAction(
          celebrationId
        );
        console.log("All reactions:", allReactions);
        const matchingReaction = allReactions?.find(
          (item) => item.employee === employeeEmail && item.reaction === emoji
        );
        if (matchingReaction) {
          const response = await deleteReactionsAction({
            id: matchingReaction.id,
          });
          console.log("Delete reaction response:", response);
          if (response?.errors) {
            revertUpdate = true;
            throw new Error(response.errors[0].message);
          }
        }
      }
      setConfettiCelebrationId(celebrationId);
      setTimeout(() => setConfettiCelebrationId(null), 2000);
    } catch (error) {
      console.error("Error handling emoji reaction:", error);
      message.error("Failed to update reaction: " + error.message);
      // Revert optimistic update if mutation failed
      await fetchCelebrations();
    } finally {
      setProcessingReactions((prev) => {
        const newState = { ...prev };
        delete newState[celebrationId];
        return newState;
      });
    }
  };

  return (
    <div className={`overflow-hidden`} ref={listContainerRef}>
      <div className="flex justify-center items-center mb-4">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            resetForm();
            setIsModalOpen(true);
          }}
        >
          Share Celebration
        </Button>
      </div>

      <List
        loading={loading}
        dataSource={celebrations}
        grid={{
          gutter: [16, 24],
          column: columnCount,
        }}
        renderItem={(celebration) => (
          <Card
            className="mb-3 mx-5 celebration-card"
            style={{ height: "100%" }}
          >
            <div className="flex items-start">
              <ProfileAvatar profilePic={celebration?.employee?.profile_pic} />
              <div className="flex-1">
                <div className="flex justify-between items-start flex-wrap gap-2">
                  <div>
                    <div className="text-base">
                      <RenderEmployeeFullName
                        employee={celebration.employee}
                        showAvatar={false}
                      />
                      {" is celebrating "}
                      <span className="font-medium">{celebration.title}</span>
                    </div>
                    <div className="text-gray-400 text-sm date">
                      {moment(celebration.createdAt).format("MMM D, YYYY")}
                    </div>
                  </div>
                  {isOwnCelebration(celebration) && (
                    <div className="flex gap-2 shrink-0">
                      <Button
                        type="text"
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(celebration)}
                        title="Edit celebration"
                      />
                      <Popconfirm
                        title="Delete celebration"
                        description="Are you sure you want to delete this celebration?"
                        onConfirm={() => handleDelete(celebration.id)}
                        okText="Yes"
                        cancelText="No"
                      >
                        <Button
                          type="text"
                          danger
                          icon={<DeleteOutlined />}
                          title="Delete celebration"
                        />
                      </Popconfirm>
                    </div>
                  )}
                </div>
                {celebration.note && (
                  <div className="text-gray-400">
                    <i>Footnote: {celebration.note}</i>
                  </div>
                )}
                <div className="mt-2 flex items-center">
                  <EmojiReactionTooltip
                    emoji={"🎉"}
                    reactions={celebration.reactions || {}}
                    userEmail={employeeEmail}
                  >
                    <Badge
                      className={`cursor-pointer rounded-xl px-1.5 py-0.5 flex items-center gap-1 outline outline-1 w-10 h-6 justify-center ${
                        celebration.reactions?.["🎉"]?.employees?.some(
                          (emp) => emp.email === employeeEmail
                        )
                          ? "bg-green-100 outline-green-200 hover:bg-green-100"
                          : "outline-gray-200 hover:bg-gray-100"
                      }`}
                      onClick={() => handleEmojiReaction(celebration.id, "🎉")}
                    >
                      <span>🎉</span>
                      {celebration.reactions?.["🎉"]?.count > 0 && (
                        <span className="text-sm">
                          {celebration.reactions["🎉"].count}
                        </span>
                      )}
                    </Badge>
                  </EmojiReactionTooltip>
                </div>
              </div>
            </div>
          </Card>
        )}
      />

      <Modal
        title={editingCelebration ? "Edit Celebration" : "Share a Celebration"}
        open={isModalOpen}
        onOk={handleSubmit}
        onCancel={() => {
          setIsModalOpen(false);
          resetForm();
        }}
        okText={editingCelebration ? "Update" : "Share"}
        cancelText="Cancel"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              What are you celebrating?
            </label>
            <Select
              className="w-full"
              placeholder="Select celebration type"
              value={celebrationType}
              onChange={handleCelebrationTypeChange}
            >
              {CELEBRATION_TYPES.map((type) => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
          </div>

          {celebrationType === "OTHER" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Please specify
              </label>
              <Input
                placeholder="Enter celebration type"
                value={customCelebrationType}
                onChange={(e) => setCustomCelebrationType(e.target.value)}
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Add a note (optional)
            </label>
            <TextArea
              maxLength={50}
              placeholder="e.g. Sweets in the pantry... (Max 50 characters)"
              value={celebrationNote}
              onChange={(e) => setCelebrationNote(e.target.value)}
              rows={4}
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CelebrationBoard;
