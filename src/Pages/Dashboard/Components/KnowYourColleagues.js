import React, { useState, useEffect } from "react";
import { Card, Avatar, Typography, Button, Tag } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { ExecuteQueryCustomV2 } from "utils/Api";
import { listEmployeesCustom } from "graphql/customQueries";
import { Storage } from "aws-amplify";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";

const { Title, Text } = Typography;

const KnowYourColleagues = () => {
  const [randomEmployee, setRandomEmployee] = useState(null);
  const [loading, setLoading] = useState(true);
  const [avatarUrl, setAvatarUrl] = useState(null);

  useEffect(() => {
    fetchRandomEmployee();
  }, []);

  useEffect(() => {
    if (randomEmployee?.profile_pic) {
      Storage.get(randomEmployee.profile_pic)
        .then((url) => setAvatarUrl(url))
        .catch(() => setAvatarUrl(null));
    } else {
      setAvatarUrl(null);
    }
  }, [randomEmployee]);

  const fetchRandomEmployee = async () => {
    try {
      setLoading(true);
      const response = await ExecuteQueryCustomV2(listEmployeesCustom, {
        filter: {
          introduction: { attributeExists: true },
          active: { eq: true },
        },
        limit: 999, // Fetch a reasonable batch size
      });

      if (response?.length > 0) {
        // Filter out employees with empty introductions
        const employeesWithIntro = response.filter(
          (emp) => emp.introduction && emp.introduction.trim() !== ""
        );

        if (employeesWithIntro.length > 0) {
          // Get a random employee from the filtered list
          const randomIndex = Math.floor(
            Math.random() * employeesWithIntro.length
          );
          setRandomEmployee(employeesWithIntro[randomIndex]);
        } else {
          setRandomEmployee(null);
        }
      } else {
        setRandomEmployee(null);
      }
    } catch (error) {
      console.error("Error fetching random employee:", error);
      setRandomEmployee(null);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchRandomEmployee();
  };

  if (!randomEmployee) {
    return null;
  }

  // Calculate experience in years
  const getExperience = () => {
    if (!randomEmployee.career_start_date) return null;
    const startDate = new Date(randomEmployee.career_start_date);
    const today = new Date();
    const diffTime = Math.abs(today - startDate);
    const diffYears = Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365));
    return diffYears;
  };

  return (
    <div className="mb-4">
      <div className="flex justify-center items-center mb-4">
        <Button
          type="link"
          onClick={handleRefresh}
          className="p-0"
          loading={loading}
        >
          Show Another
        </Button>
      </div>
      <div className="flex items-start space-x-4">
        <Avatar
          size={64}
          src={avatarUrl}
          icon={<UserOutlined />}
          className="flex-shrink-0"
        />
        <div className="flex-1">
          <div className="mb-3">
            <RenderEmployeeFullName
              employee={randomEmployee}
              showAvatar={false}
              className="text-lg font-medium"
            />
            <div className="flex items-center gap-2 text-gray-600">
              {randomEmployee.title?.name && (
                <span>{randomEmployee.title.name}</span>
              )}
              {getExperience() !== null && (
                <>
                  <span>•</span>
                  <span>{getExperience()} years of experience</span>
                </>
              )}
            </div>
          </div>

          <div className="mb-3">
            <div className="mb-2">
              <div className="mb-1 font-semibold text-gray-700 text-xs">
                Skills
              </div>
              <div className="border-b border-gray-200 mb-2"></div>
              <div className="flex flex-wrap gap-2">
                {randomEmployee?.skills?.items?.length > 0 ? (
                  randomEmployee.skills.items.map((skill, index) => (
                    <Tag key={index} color="blue" className="text-xs py-0.5">
                      {skill?.skillID}
                    </Tag>
                  ))
                ) : (
                  <span className="text-gray-400 text-sm">
                    No skills listed
                  </span>
                )}
              </div>
            </div>
            <div>
              <div className="mb-1 font-semibold text-gray-700 text-xs">
                Subject Matter Expert (SME) In
              </div>
              <div className="border-b border-gray-200 mb-2"></div>
              <div className="flex flex-wrap gap-2">
                {randomEmployee?.SME?.items?.length > 0 ? (
                  randomEmployee.SME.items.map((sme, index) => (
                    <Tag
                      key={`sme-${index}`}
                      color="green"
                      className="text-xs py-0.5"
                    >
                      {sme?.sMEID}
                    </Tag>
                  ))
                ) : (
                  <span className="text-gray-400 text-sm">
                    No SME areas listed
                  </span>
                )}
              </div>
            </div>
          </div>

          {randomEmployee.introduction && (
            <div className="mb-3">
              <div className="mb-1 font-semibold text-gray-700">
                Introduction
              </div>
              <div className="border-b border-gray-200 mb-2"></div>
              <div
                className="text-gray-600"
                dangerouslySetInnerHTML={{
                  __html: randomEmployee.introduction,
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default KnowYourColleagues;
