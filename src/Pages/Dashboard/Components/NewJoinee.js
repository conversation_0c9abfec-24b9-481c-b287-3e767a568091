import React, { useEffect, useRef, useState } from "react";
import { ListEmployeesCustomAction } from "utils/Actions";
import { List } from "antd";
import EmployeeCard from "Commons/EmployeeCard";
import Loader from "Commons/Loader"; // Import the Loader component

export default function NewJoinee() {
  // State to store the list of all employees
  const [AllEmployees, setAllEmployees] = useState([]);
  const [loading, setLoading] = useState(true); // State to manage loading
  const listContainerRef = useRef(null);
  const [columnCount, setColumnCount] = useState(1);

  // useEffect hook to fetch employees when the component mounts
  useEffect(() => {
    fetchEmployees();
  }, []);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const width = entry.contentRect.width;
        if (width >= 800) {
          setColumnCount(4); // xl
        } else if (width >= 600) {
          setColumnCount(3); // lg
        } else if (width >= 500) {
          setColumnCount(2); // md
        } else {
          setColumnCount(1); // sm
        }
      }
    });

    if (listContainerRef.current) {
      observer.observe(listContainerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);
  /**
   * @async
   * @function fetchEmployees
   * @description Asynchronously fetches a list of active and non-hidden employees,
   * sorts them by their York start date (most recent first), and updates the
   * @returns {void}
   */
  const fetchEmployees = async () => {
    setLoading(true); // Set loading to true before fetching
    const filter = {
      and: [{ hidden_profile: { ne: true } }, { active: { ne: false } }],
    };

    const result = await ListEmployeesCustomAction(filter);

    if (result && Array.isArray(result)) {
      const sortedRecent = result
        .filter((e) => e.york_start_date)
        .sort(
          (a, b) => new Date(b.york_start_date) - new Date(a.york_start_date)
        )
        .slice(0, 5);

      setAllEmployees(sortedRecent);
    }
    setLoading(false); // Set loading to false after fetching (success or error)
  };

  return (
    <div className="h-full overflow-auto px-1 pb-5">
      {loading ? (
        <Loader title={"Please wait while we fetch new joiners"} />
      ) : (
        <List
          className="w-full"
          itemLayout="vertical"
          split={false}
          dataSource={AllEmployees}
          renderItem={(employee) => (
            <List.Item key={employee.employee_id} className="p-0">
              <EmployeeCard employee={employee} />
            </List.Item>
          )}
        />
      )}
    </div>
  );
}
