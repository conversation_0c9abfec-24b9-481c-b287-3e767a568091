import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { ExecuteQueryCustomV2 } from "utils/Api";
import Loader from "Commons/Loader";
import { getJiraEventsByEmployee } from "../Database/Queries";

const DashboardJiraBox = () => {
  const email = useSelector((state) => state?.loginReducer?.userData?.email);
  const [jiraEvents, setJiraEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchJiraEvents = async () => {
      setLoading(true);
      try {
        const res = await ExecuteQueryCustomV2(getJiraEventsByEmployee, {
          employeeEmail: email,
          limit: 999, // Show top 5, or adjust as needed
        });
        console.log("res", res);
        setJiraEvents(res || []);
      } catch (err) {
        setJiraEvents([]);
      } finally {
        setLoading(false);
      }
    };
    if (email) fetchJiraEvents();
  }, [email]);

  return (
    <div className="flex flex-col items-center h-full overflow-auto">
      <div className="flex-1 w-full">
        {loading ? (
          <Loader title="Loading your Jira issues..." />
        ) : jiraEvents.length === 0 ? (
          <div className="text-center text-gray-500 mt-4">
            No Jira issues assigned to you.
          </div>
        ) : (
          // Group by sprintId, sort sprints by sprintId descending (numeric if possible)
          (() => {
            // Filter out closed
            const filtered = jiraEvents.filter(
              (issue) =>
                !issue.status || issue.status.toLowerCase() !== "closed"
            );
            // Group by sprintId
            const groups = {};
            filtered.forEach((issue) => {
              const sprintId = issue.sprintId || "No Sprint";
              if (!groups[sprintId]) groups[sprintId] = [];
              groups[sprintId].push(issue);
            });
            // Sort sprintIds ascending (numeric if possible)
            const sprintIds = Object.keys(groups).sort((a, b) => {
              const aNum = parseInt(a.match(/\d+/)?.[0]);
              const bNum = parseInt(b.match(/\d+/)?.[0]);
              if (!isNaN(aNum) && !isNaN(bNum)) return aNum - bNum;
              if (!isNaN(aNum)) return -1;
              if (!isNaN(bNum)) return 1;
              return a.localeCompare(b);
            });
            // Priority order helper
            const priorityOrder = ["p0", "p1", "p2", "p3", "p4"];
            const getPriorityIndex = (priority) => {
              if (!priority) return -1;
              const lower = priority.toLowerCase();
              for (let i = 0; i < priorityOrder.length; i++) {
                if (lower.startsWith(priorityOrder[i])) return i;
              }
              return -1;
            };
            return (
              <div>
                {sprintIds.map((sprintId) => {
                  // Only show group if at least one issue is not closed
                  const openIssues = groups[sprintId].filter(
                    (issue) =>
                      !issue.status || issue.status.toLowerCase() !== "closed"
                  );
                  if (openIssues.length === 0) return null;
                  return (
                    <div key={sprintId} className="mb-6">
                      <div className="font-bold text-lg mb-2">
                        {groups[sprintId][0]?.sprintName || sprintId}
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                        {openIssues
                          .sort((a, b) => {
                            const aIndex = getPriorityIndex(a.priority);
                            const bIndex = getPriorityIndex(b.priority);
                            if (aIndex !== bIndex) {
                              if (aIndex === -1) return 1;
                              if (bIndex === -1) return -1;
                              return aIndex - bIndex;
                            }
                            return (
                              new Date(b.createdAt) - new Date(a.createdAt)
                            );
                          })
                          .map((issue) => (
                            <div
                              key={issue.issueKey}
                              className="bg-white border rounded-lg shadow p-4 flex flex-col gap-2 min-w-0"
                            >
                              <div className="flex justify-between items-center">
                                <div
                                  className="font-semibold text-base text-black"
                                  title={issue.issueSummary}
                                >
                                  {issue.issueSummary}
                                </div>
                              </div>
                              <hr className="my-2 border-gray-200" />
                              <a
                                href={`https://yorkdocs.atlassian.net/browse/${issue.issueKey}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="font-semibold text-sm text-black mb-1"
                                title={issue.issueKey}
                              >
                                {issue.issueKey}
                              </a>
                              <div className="flex flex-wrap gap-2 text-xs text-gray-500">
                                {issue.issueType && (
                                  <span className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded">
                                    {issue.issueType}
                                  </span>
                                )}
                                {issue.sprintName && (
                                  <span className="bg-green-100 text-green-700 px-2 py-0.5 rounded">
                                    {issue.sprintName}
                                  </span>
                                )}
                                {issue.status && (
                                  <span className="bg-blue-200 text-blue-800 px-2 py-0.5 rounded">
                                    Status: {issue.status}
                                  </span>
                                )}
                                {issue.priority && (
                                  <span className="bg-orange-200 text-orange-800 px-2 py-0.5 rounded">
                                    Priority: {issue.priority}
                                  </span>
                                )}
                                {issue.severity && (
                                  <span className="bg-red-200 text-red-800 px-2 py-0.5 rounded">
                                    Severity: {issue.severity}
                                  </span>
                                )}
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            );
          })()
        )}
      </div>
    </div>
  );
};

export default DashboardJiraBox;
