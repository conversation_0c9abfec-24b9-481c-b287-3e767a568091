import React, { useState, useRef, useEffect } from "react";
import { Input, <PERSON><PERSON>, Card, Avatar, Spin, Typography, Space } from "antd";
import { SendOutlined, RobotOutlined, UserOutlined } from "@ant-design/icons";
import { useSelector } from "react-redux";
import { Storage } from "aws-amplify";
import { Auth } from "aws-amplify";
import { boxClass } from "utils/TailwindCommonClasses";
import { ExecuteQueryCustomV2 } from "utils/Api";
import ReactMarkdown from "react-markdown";

const { Text } = Typography;

// Helper function to detect if content contains markdown
const containsMarkdown = (content) => {
  const markdownPatterns = [
    /\*\*.*?\*\*/, // bold
    /\*.*?\*/, // italic
    /`.*?`/, // inline code
    /```[\s\S]*?```/, // code blocks
    /^#{1,6}\s/, // headers
    /^[-*+•]\s/, // unordered lists (including bullet points)
    /^\d+\.\s/, // ordered lists
    /^>\s/, // blockquotes
    /\[.*?\]\(.*?\)/, // links
    /^\|.*\|$/, // tables
    /^---$/, // horizontal rules
    /\n\n/, // double line breaks (paragraphs)
  ];

  return markdownPatterns.some((pattern) => pattern.test(content));
};

// Helper function to preprocess content for markdown rendering
const preprocessMarkdown = (content) => {
  // Convert bullet points to proper markdown
  return content
    .replace(/^•\s/gm, "* ") // Convert • to *
    .replace(/^-\s/gm, "* ") // Convert - to *
    .replace(/^\+\s/gm, "* "); // Convert + to *
};

// Add this function to strip code block wrappers
function stripMarkdownCodeBlock(content) {
  // Remove ```markdown ... ``` or ``` ... ```
  return content.replace(/^```(?:markdown)?\n?/i, "").replace(/\n?```$/, "");
}

// Component to render message content with markdown support for system messages
const MessageContent = ({ content, isSystemMessage }) => {
  if (isSystemMessage) {
    // Remove code block wrapper if present
    let processedContent = stripMarkdownCodeBlock(content);
    processedContent = preprocessMarkdown(processedContent);
    console.log("Bot message content:", processedContent); // Debug log
    return (
      <div
        className="markdown-content w-full break-words overflow-hidden"
        style={{ whiteSpace: "normal" }}
      >
        <ReactMarkdown
          components={{
            // Customize markdown components for better styling in chat context
            p: ({ children }) => (
              <p className="mb-2 last:mb-0 text-sm leading-relaxed break-words">
                {children}
              </p>
            ),
            h1: ({ children }) => (
              <h1 className="text-lg font-bold mb-2 text-gray-800 break-words">
                {children}
              </h1>
            ),
            h2: ({ children }) => (
              <h2 className="text-base font-bold mb-2 text-gray-800 break-words">
                {children}
              </h2>
            ),
            h3: ({ children }) => (
              <h3 className="text-sm font-bold mb-1 text-gray-800 break-words">
                {children}
              </h3>
            ),
            ul: ({ children }) => (
              <ul className="list-disc list-inside mb-2 space-y-1 text-sm break-words">
                {children}
              </ul>
            ),
            ol: ({ children }) => (
              <ol className="list-decimal list-inside mb-2 space-y-1 text-sm break-words">
                {children}
              </ol>
            ),
            li: ({ children }) => (
              <li className="text-sm leading-relaxed break-words">
                {children}
              </li>
            ),
            strong: ({ children }) => (
              <strong className="font-semibold text-gray-800 break-words">
                {children}
              </strong>
            ),
            em: ({ children }) => (
              <em className="italic text-gray-700 break-words">{children}</em>
            ),
            code: ({ children }) => (
              <code className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono text-gray-800 break-all">
                {children}
              </code>
            ),
            pre: ({ children }) => (
              <pre className="bg-gray-100 p-2 rounded text-xs font-mono overflow-x-auto mb-2 text-gray-800 break-all whitespace-pre-wrap">
                {children}
              </pre>
            ),
            blockquote: ({ children }) => (
              <blockquote className="border-l-4 border-green-300 pl-3 italic text-gray-600 mb-2 bg-green-50 py-1 rounded-r break-words">
                {children}
              </blockquote>
            ),
            a: ({ href, children }) => (
              <a
                href={href}
                className="text-blue-600 hover:text-blue-800 underline break-all"
                target="_blank"
                rel="noopener noreferrer"
              >
                {children}
              </a>
            ),
            table: ({ children }) => (
              <div className="overflow-x-auto mb-2 max-w-full">
                <table className="min-w-full border border-gray-300 text-sm break-all">
                  {children}
                </table>
              </div>
            ),
            th: ({ children }) => (
              <th className="border border-gray-300 px-2 py-1 bg-gray-100 font-semibold text-left break-words">
                {children}
              </th>
            ),
            td: ({ children }) => (
              <td className="border border-gray-300 px-2 py-1 break-words">
                {children}
              </td>
            ),
            hr: () => <hr className="border-gray-300 my-2" />,
          }}
        >
          {processedContent}
        </ReactMarkdown>
      </div>
    );
  }
  // For user messages, render as plain text
  return <Text className="text-black break-words">{content}</Text>;
};

// GraphQL query to get employee data
const getEmployeeByEmail = /* GraphQL */ `
  query GetEmployee($email: String!) {
    getEmployee(email: $email) {
      email
      first_name
      last_name
      profile_pic
    }
  }
`;

const ChatbotWidget = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: "bot",
      content: "Hello! I'm your **HR assistant**. How can I help you today?",
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(null);
  const [employeeData, setEmployeeData] = useState(null);
  const messagesEndRef = useRef(null);
  const textareaRef = useRef(null);

  // Get user data from Redux
  const { userData } = useSelector((state) => state?.loginReducer);
  const { email } = userData || {};

  // Fetch employee data from GraphQL
  useEffect(() => {
    let isMounted = true;
    if (email) {
      const fetchEmployeeData = async () => {
        try {
          const response = await ExecuteQueryCustomV2(getEmployeeByEmail, {
            email,
          });
          if (isMounted && response) {
            setEmployeeData(response);
            console.log("Employee data:", response);
          }
        } catch (error) {
          console.error("Error fetching employee data:", error);
        }
      };
      fetchEmployeeData();
    }
    return () => {
      isMounted = false;
    };
  }, [email]);

  // Get profile picture using Storage.get
  useEffect(() => {
    let isMounted = true;
    if (employeeData?.profile_pic) {
      Storage.get(employeeData.profile_pic)
        .then((url) => {
          if (isMounted) setAvatarUrl(url);
          console.log("Profile pic URL:", url);
        })
        .catch((error) => {
          console.error("Error getting profile picture:", error);
          if (isMounted) setAvatarUrl(null);
        });
    } else {
      setAvatarUrl(null);
    }
    return () => {
      isMounted = false;
    };
  }, [employeeData?.profile_pic]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({
      behavior: "smooth",
      block: "nearest",
    });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Auto-resize textarea height
  const handleTextareaInput = (e) => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      textarea.style.height = textarea.scrollHeight + "px";
    }
  };

  const sendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: "user",
      content: inputValue.trim(),
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsLoading(true);
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      // Focus without scrolling the page
      textareaRef.current.focus({ preventScroll: true });
    }

    try {
      // Get Cognito token
      const session = await Auth.currentSession();
      const idToken = session.getIdToken().getJwtToken();

      const response = await fetch(
        "https://workflow.yorkdevs.link/webhook/hubchatbot",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: idToken,
          },
          body: JSON.stringify({
            chatInput: userMessage.content,
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Network response was not ok");
      }

      const data = await response.json();

      const botMessage = {
        id: Date.now() + 1,
        type: "bot",
        content: data.output || "Sorry, I couldn't process your request.",
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, botMessage]);
    } catch (error) {
      console.error("Error sending message:", error);
      const errorMessage = {
        id: Date.now() + 1,
        type: "bot",
        content:
          "Sorry, I'm having trouble connecting right now. Please try again later.",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTime = (timestamp) => {
    return timestamp.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    if (employeeData?.first_name && employeeData?.last_name) {
      return `${employeeData.first_name.charAt(
        0
      )}${employeeData.last_name.charAt(0)}`.toUpperCase();
    }
    return email?.charAt(0)?.toUpperCase() || "U";
  };

  return (
    <Card
      className={`${boxClass} h-full flex flex-col`}
      bodyStyle={{
        padding: 0,
        height: "100%",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-3 max-h-96">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${
              message.type === "user" ? "justify-end" : "justify-start"
            } w-full`}
          >
            <div
              className={`flex items-start space-x-2 ${
                message.type === "user"
                  ? "flex-row-reverse space-x-reverse"
                  : ""
              }`}
              style={{ maxWidth: "85%", minWidth: 0 }}
            >
              <Avatar
                src={message.type === "user" ? avatarUrl : undefined}
                icon={
                  message.type === "user" ? (
                    avatarUrl ? undefined : (
                      <UserOutlined />
                    )
                  ) : (
                    <RobotOutlined />
                  )
                }
                className={
                  message.type === "user" ? "bg-white" : "bg-green-600"
                }
                size="small"
                style={{ flexShrink: 0 }}
              >
                {message.type === "user" && !avatarUrl
                  ? getUserInitials()
                  : undefined}
              </Avatar>
              <div
                className={`rounded-lg px-3 py-2 flex-shrink-0 ${
                  message.type === "user"
                    ? "bg-white text-black border border-gray-300"
                    : "bg-green-50 text-black border border-green-200"
                }`}
                style={{
                  minWidth: 0,
                  wordWrap: "break-word",
                  overflowWrap: "break-word",
                  maxWidth: "100%",
                }}
              >
                <div className="w-full overflow-hidden">
                  <MessageContent
                    content={message.content}
                    isSystemMessage={message.type === "bot"}
                  />
                </div>
                <div className={`text-xs mt-1 text-gray-600`}>
                  {formatTime(message.timestamp)}
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Loading indicator */}
        {isLoading && (
          <div className="flex justify-start">
            <div className="flex items-start space-x-2">
              <Avatar
                icon={<RobotOutlined />}
                className="bg-green-600"
                size="small"
              />
              <div className="bg-green-100 rounded-lg px-3 py-2 border border-green-200">
                <Spin size="small" />
                <Text className="text-green-600 ml-2">Typing...</Text>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="border-t border-gray-300 p-4">
        <div className="flex space-x-2">
          <textarea
            ref={textareaRef}
            value={inputValue}
            onChange={(e) => {
              setInputValue(e.target.value);
              handleTextareaInput();
            }}
            onInput={handleTextareaInput}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
              }
              // Shift+Enter will add a new line by default
            }}
            placeholder="Type your message..."
            disabled={isLoading}
            className="flex-1 resize-none border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-400 min-h-[40px] max-h-32"
            rows={1}
            style={{ width: "100%", overflow: "auto" }}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={sendMessage}
            disabled={!inputValue.trim() || isLoading}
            loading={isLoading}
            style={{
              backgroundColor: "rgb(0, 180, 138)",
              borderColor: "rgb(0, 180, 138)",
              color: "white",
            }}
          >
            Send
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ChatbotWidget;
