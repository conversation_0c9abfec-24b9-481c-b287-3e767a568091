import React, {
  useEffect,
  useState,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { Col, Input, List, Row, Skeleton } from "antd";
import { LeftOutlined, RightOutlined, SearchOutlined } from "@ant-design/icons";
import moment from "moment";

import { listWFHByFilter } from "Pages/Leave/WFH/Actions/WFHActions";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import useSearchHook from "utils/hooks/useSearchHook";
import { DateFormat } from "utils/constants";

function PeopleOnWFH() {
  const [selectedDate, setSelectedDate] = useState(moment());
  const [allWFHData, setAllWFHData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [containerWidth, setContainerWidth] = useState(0);
  const containerRef = useRef(null);

  // Observe component width
  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      for (let entry of entries) {
        setContainerWidth(entry.contentRect.width);
      }
    });

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    fetchWFHData();
  }, [selectedDate]);

  const handleNavigation = (direction) => {
    setSelectedDate((prev) => prev.clone().add(direction, "day"));
  };
  // Fetch WFH data
  const fetchWFHData = useCallback(async () => {
    setLoading(true);
    try {
      const filterStartDate = moment(selectedDate)
        .utc()
        .startOf("day")
        .format();
      const filterEndDate = moment(selectedDate).utc().endOf("day").format();

      const filter = {
        and: [
          { startDate: { le: filterEndDate } },
          { endDate: { ge: filterStartDate } },
          { status: { eq: "APPROVED" } },
          { adjustment_type: { eq: "DEBIT" } },
        ],
      };

      const results = await listWFHByFilter(filter);
      if (results?.items) {
        const formattedResults = results.items.map((item) => ({
          ...item,
          start_time_formatted: moment(item.startDate).format(DateFormat),
          end_time_formatted: moment(item.endDate).format(DateFormat),
        }));
        setAllWFHData(formattedResults);
      } else {
        setAllWFHData([]);
      }
    } catch (error) {
      console.error("Failed to fetch WFH data:", error);
      setAllWFHData([]);
    } finally {
      setLoading(false);
    }
  }, [selectedDate]);

  const filteredByDate = useMemo(() => {
    const target = selectedDate.clone().startOf("day");
    return allWFHData.filter((item) => {
      const start = moment(item.startDate).startOf("day");
      const end = moment(item.endDate).startOf("day");
      return target.isBetween(start, end, null, "[]");
    });
  }, [allWFHData, selectedDate]);

  const sortedByName = useMemo(() => {
    return [...filteredByDate].sort((a, b) => {
      const nameA =
        `${a?.employee?.first_name} ${a?.employee?.last_name}`.toLowerCase();
      const nameB =
        `${b?.employee?.first_name} ${b?.employee?.last_name}`.toLowerCase();
      return nameA.localeCompare(nameB);
    });
  }, [filteredByDate]);

  const searchFunction = useCallback(
    (item) => {
      const fullName =
        `${item?.employee?.first_name}${item?.employee?.last_name}`.toLowerCase();
      return fullName.includes(searchQuery.toLowerCase());
    },
    [searchQuery]
  );

  const filteredWFHList = useSearchHook(
    sortedByName,
    searchFunction,
    searchQuery,
    [],
    300
  );

  return (
    <div ref={containerRef} style={{ padding: "0 16px" }}>
      {" "}
      <Row
        gutter={15}
        justify="center"
        align="middle"
        className="mb-3 text-center"
      >
        <Col>
          <LeftOutlined
            className="prev-icon"
            onClick={() => handleNavigation(-1)}
          />
        </Col>
        <Col>
          <span className="date-text">
            <b>{selectedDate.format(DateFormat)}</b>
          </span>
        </Col>
        <Col>
          <RightOutlined
            className="next-icon"
            onClick={() => handleNavigation(1)}
          />
        </Col>
      </Row>
      {loading ? (
        <Skeleton active />
      ) : (
        <>
          {filteredByDate.length > 0 && (
            <Input
              placeholder="Search people on WFH"
              prefix={<SearchOutlined />}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="people-on-search-input w-100"
              style={{ marginBottom: 12 }}
            />
          )}

          {containerWidth < 576 ? (
            // Vertical List View for very narrow containers
            <List
              itemLayout="horizontal"
              dataSource={filteredWFHList}
              renderItem={(item) => (
                <List.Item key={item.id}>
                  <List.Item.Meta
                    title={
                      <span className="flex flex-col">
                        <div className="block items-center gap-1 m-2">
                          <RenderEmployeeFullName
                            employee={item?.employee}
                            showAvatar={true}
                            avatarSize={25}
                            showPopover
                          />
                        </div>
                        <span className="text-smallest text-gray-500 ml-8">
                          ({item.start_time_formatted} -{" "}
                          {item.end_time_formatted})
                        </span>
                      </span>
                    }
                  />
                </List.Item>
              )}
            />
          ) : (
            // Responsive grid based on component width
            <Row gutter={[16, 16]}>
              {filteredWFHList.map((item) => {
                let span = 24;
                if (containerWidth >= 992) {
                  span = 6;
                } else if (containerWidth >= 768) {
                  span = 8;
                } else if (containerWidth >= 576) {
                  span = 12;
                }

                return (
                  <Col key={item.id} span={span}>
                    <div className="border border-[#e0e0e0] p-2 rounded-lg bg-white h-full w-full box-border">
                      <RenderEmployeeFullName
                        employee={item?.employee}
                        showAvatar={true}
                        avatarSize={30}
                        showPopover
                      />
                      <p className="text-[12px] text-[#666] ml-8">
                        ({item.start_time_formatted} - {item.end_time_formatted}
                        )
                      </p>
                    </div>
                  </Col>
                );
              })}
            </Row>
          )}
        </>
      )}
    </div>
  );
}

export default PeopleOnWFH;
