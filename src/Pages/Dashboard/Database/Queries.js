export const getJiraEventsByEmployee = /* GraphQL */ `
  query GetJiraEventsByEmployee(
    $employeeEmail: String!
    $sortDirection: ModelSortDirection
    $filter: ModelJiraEventFilterInput
    $limit: Int
    $nextToken: String
  ) {
    getJiraEventsByEmployee(
      employeeEmail: $employeeEmail
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        issueKey
        eventType
        issueSummary
        description
        descriptionQualityAI
        sprintId
        sprintName
        boardId
        boardName
        storyPoints
        employeeEmail
        employee {
          email
          employee_id
          first_name
          last_name
          mobile
          profile_pic
          profile_pic_requested
          introduction
          metadata
          country
          createdAt
          updatedAt
          __typename
        }
        issueCreatorEmail
        issueCreator {
          email
          employee_id
          first_name
          last_name
          mobile
          profile_pic
          profile_pic_requested
          introduction
          metadata
          country
          createdAt
          updatedAt
          __typename
        }
        jiraProjectId
        projectId
        project {
          id
          name
        }
        parentIssueKey
        createdAt
        issueType
        status
        priority
        severity
        updatedAt
        employeeJiraEventsId
        projectJiraEventsId
        __typename
      }
      nextToken
      __typename
    }
  }
`;
