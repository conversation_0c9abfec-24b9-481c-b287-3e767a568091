export const listExecutableHistoriesPendingItems = /* GraphQL */ `
  query ListExecutableHistories(
    $filter: ModelExecutableHistoryFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listExecutableHistories(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        rule {
          id
          title
          description
          frequency
          allowedDelay
          completionType
          status
          ruleDefination
          createdAt
          updatedAt
          executionRuleResponsibleGroupId
          executionRuleAuthorityGroupId
          __typename
        }
        status
        metaData
        createdAt
        updatedAt
        executableHistoryRuleId
        employeeEmail
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const listExecutablesPendingItems = /* GraphQL */ `
  query ListExecutables(
    $filter: ModelExecutableFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listExecutables(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        rule {
          id
          title
          description
          frequency
          allowedDelay
          completionType
          status
          ruleDefination
          createdAt
          updatedAt
          executionRuleResponsibleGroupId
          executionRuleAuthorityGroupId
          __typename
        }
        expiry
        status
        metaData
        createdAt
        updatedAt
        executableRuleId
        employeeEmail
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const listExecutionRulesPendingItems = /* GraphQL */ `
  query ListExecutionRules(
    $filter: ModelExecutionRuleFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listExecutionRules(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        frequency
        allowedDelay
        responsibleGroup {
          id
          name
          description
          permissions
          leaderEmail
          createdAt
          updatedAt
          __typename
        }
        authorityGroup {
          id
          name
          description
          permissions
          leaderEmail
          createdAt
          updatedAt
          __typename
        }
        completionType
        status
        ruleDefination
        createdAt
        updatedAt
        executionRuleResponsibleGroupId
        executionRuleAuthorityGroupId
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const updateExecutableQuery = /* GraphQL */ `
  mutation UpdateExecutable(
    $input: UpdateExecutableInput!
    $condition: ModelExecutableConditionInput
  ) {
    updateExecutable(input: $input, condition: $condition) {
      id
      __typename
    }
  }
`;
