import {
  listExecutableHistoriesPendingItems,
  listExecutablesPendingItems,
  listExecutionRulesPendingItems,
  updateExecutableQuery,
} from "Pages/Dashboard/Database/PendingItemsQueries";
import { ExecuteMutationV2, ExecuteQueryCustomV2 } from "utils/Api";

export const listExecutableHistoriesPendingAction = async (
  filter,
  nextToken
) => {
  return ExecuteQueryCustomV2(listExecutableHistoriesPendingItems, {
    filter,
    nextToken,
  });
};
export const listExecutablesPendingItemsAction = async (filter) => {
  return ExecuteQueryCustomV2(listExecutablesPendingItems, { filter });
};
export const listExecutionRulesPendingItemsAction = async (filter) => {
  return ExecuteQueryCustomV2(listExecutionRulesPendingItems, { filter });
};
export const updateExecutableAction = async (input) => {
  return ExecuteMutationV2(updateExecutableQuery, { input });
};
