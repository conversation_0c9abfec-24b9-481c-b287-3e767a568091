/* eslint-disable react-hooks/exhaustive-deps */
import { Button, List, message, Skeleton } from "antd";

import moment from "moment";

import React, { useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { createWishAction, ListEmployeesCustomAction } from "utils/Actions";
import {
  createGlobalNotification,
  GetSortOrder,
  gettimeDiff,
  removeDuplicatesFromArray,
} from "utils/commonMethods";
import { DateFormat, Wishes, WishesNotification } from "utils/constants";
import { getEmployeeDetails } from "../../store/slices/loginSlice";
import { isExecutive, isHr } from "store/slices/loginSlice";
import ExportToCSV from "Commons/ExportToCSV";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import { boxClass } from "utils/TailwindCommonClasses";

function setCookie(name, value, hours) {
  var expires = new Date();
  expires.setTime(expires.getTime() + hours * 60 * 60 * 1000); // Convert hours to milliseconds
  document.cookie =
    name + "=" + value + ";expires=" + expires.toUTCString() + ";path=/";
}

function getCookie(key) {
  var cookieString = document.cookie;
  var cookies = {};

  if (cookieString) {
    var cookieArray = cookieString.split(";");

    for (var i = 0; i < cookieArray.length; i++) {
      var cookie = cookieArray[i].trim().split("=");
      var name = cookie[0];
      var value = cookie[1];
      cookies[name] = value;
    }
  }

  return cookies[key] || null;
}

function SendWish() {
  const [EmployeeOccasions, setEmployeeOccasions] = useState([]);
  const [upcommingEvents, setupcommingEvents] = useState([]);
  const [pastEvents, setpastEvents] = useState([]);
  const [skeletonLoader, setskeletonLoader] = useState(false);

  const [selectedFields, setSelectedFields] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const {
    userData: { email },
  } = useSelector((state) => state?.loginReducer);

  const ExecutvieLogin = useSelector(isExecutive);
  const HrLogin = useSelector(isHr);

  const listContainerRef = useRef(null);
  const [columnCount, setColumnCount] = useState(1);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const width = entry.contentRect.width;
        if (width >= 900) {
          setColumnCount(4);
        }else if (width >= 700) {
          setColumnCount(3);
        }
         else if (width >= 600) {
          setColumnCount(2);
        } else {
          setColumnCount(1);
        }
      }
    });

    if (listContainerRef.current) {
      observer.observe(listContainerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);
  const allFields = [
    { value: "name", label: "Name" },
    { value: "occasion", label: "Occasion" },
    { value: "date", label: "Date" },
    // Add more fields as necessary
  ];

  const formatEmployeeData = () => {
    if (selectedFields.length === 0) {
      return [];
    }

    const selectedData = [
      ...EmployeeOccasions,
      ...upcommingEvents,
      ...pastEvents,
    ].map((item) => {
      let eventData = {};

      if (selectedFields.includes("name"))
        eventData.name = `${item.first_name} ${item.last_name}`;
      if (selectedFields.includes("occasion"))
        eventData.occasion = item.occasion;
      if (selectedFields.includes("date")) eventData.date = item.date;

      return eventData;
    });
    return selectedData;
  };

  const handleExportCSV = () => {
    // This logic is handled by the ExportToCSV component
    setIsModalVisible(false);
  };

  //Moment.js date object representing the current date and time
  //useMemo ensures that selectedDate is computed only once when the component mounts (unless unmounted and remounted)..
  const selectedDate = useMemo(() => moment(), []);

  function addZero(count) {
    return count < 10 ? "0" + String(count) : String(count);
  }

  //unction fetches employee details and categorizes them into Today’s Events, Upcoming Events, and Past Events
  // based on their work anniversary, birthday, or wedding anniversary.
  async function handleListOccasion() {
    const getDateAndMonth = () => {
      return `-${addZero(new Date().getMonth() + 1)}-`;
    };

    //filters employee having an occasion (Birthday, Work Anniversary, or Wedding Anniversary) this month.
    let filter = {
      or: [
        { york_start_date: { contains: getDateAndMonth() } },
        { anniversary_date: { contains: getDateAndMonth() } },
        { birth_date: { contains: getDateAndMonth() } },
      ],
      and: [
        { email: { ne: email } },
        { hidden_profile: { ne: true } },
        {
          active: { ne: false },
        },
      ],
    };

    return await ListEmployeesCustomAction(filter)
      .then(async (res) => {
        let isJoinedToday = moment()?.format("YYYY-MM-DD");
        let currentDate = moment().get("D");
        let currentMonth = moment().get("M");
        let currentYear = moment().get("Y");

        //Maps database fields to human-readable names.
        let fieldNameMap = {
          birth_date: "Birthday",
          anniversary_date: "Wedding Anniversary",
          york_start_date: "Work Anniversary",
        };

        let upcommingEvents = [];
        let pastEvents = [];
        let todayEvents = [];

        res
          ?.map((item) => {
            item.full_name = `${item?.first_name} ${item?.last_name}`;

            item.isJoinedToday =
              String(item?.york_start_date) === isJoinedToday;
            item.isWork = String(item?.york_start_date).includes(
              moment(selectedDate).format("-MM-DD")
            );
            item.isWedding = String(item?.anniversary_date).includes(
              moment(selectedDate).format("-MM-DD")
            );

            item.yearsInYork =
              gettimeDiff(new Date(), new Date(item?.york_start_date)) + " Ago";

            item.isBirthDate = String(item?.birth_date).includes(
              moment(selectedDate).format("-MM-DD")
            );

            //occasion string to store occasion
            let occasion = [
              item?.isWork && `Work Anniversary - Joined ${item?.yearsInYork}`,
              item?.isWedding && `Wedding Anniversary`,
              item?.isBirthDate && `Wish Happy Birthday`,
            ]
              .filter(Boolean) // removes all false, null, undefined, or empty values from the array
              .join(" | "); //converts the filtered array into a single string, separated by " | "

            item.occasion = occasion;

            // for getting past and upcoming events data checks if today is the work anniversary, birthday, or wedding anniversary of the employee.
            let isAnythingToday =
              String(item?.york_start_date).includes(
                moment(selectedDate).format("-MM-DD")
              ) ||
              String(item?.birth_date).includes(
                moment(selectedDate).format("-MM-DD")
              ) ||
              String(item?.anniversary_date).includes(
                moment(selectedDate).format("-MM-DD")
              );

            //it's NOT today, we loop through york_start_date, birth_date, and anniversary_date for past or upcoming events
            if (!isAnythingToday) {
              let checkableItems = [
                "york_start_date",
                "birth_date",
                "anniversary_date",
              ];

              //If month matches the current month but year is different, check the day:
              // If the day is in the past, add to pastEvents.
              // If the day is in the future, add to upcomingEvents.
              checkableItems?.forEach((field) => {
                if (
                  moment(item?.[field])?.get("M") === currentMonth &&
                  moment(item?.[field])?.get("Y") !== currentYear
                ) {
                  if (moment(item?.[field])?.get("D") < currentDate) {
                    pastEvents.push({
                      ...item,
                      date: moment(item?.[field]).format(DateFormat),
                      occasion: fieldNameMap[field],
                    });
                  } else {
                    upcommingEvents.push({
                      ...item,
                      date: moment(item?.[field]).format(DateFormat),
                      occasion: fieldNameMap[field],
                    });
                  }
                }
              });
            } else {
              todayEvents.push(item);
            }
            return item;
          })
          .filter((item) => !item?.isJoinedToday)
          .sort((a, b) => a.first_name.localeCompare(b.first_name));

        return (
          {
            upcoming: removeDuplicatesFromArray(upcommingEvents)?.sort(
              GetSortOrder("date", true)
            ),
            past: removeDuplicatesFromArray(pastEvents)?.sort(
              GetSortOrder("date", false)
            ),
            today: removeDuplicatesFromArray(todayEvents)?.filter(
              (item) => !item?.isJoinedToday
            ),
          } ?? []
        );
      })
      .catch((err) => {
        setskeletonLoader(false);
        console.log(err);
      });
  }

  //initialize the state using handleListOccasion function
  const init = async () => {
    setskeletonLoader(true);

    try {
      let { today, upcoming, past } = await handleListOccasion();
      setEmployeeOccasions(today);
      setupcommingEvents(upcoming);
      setpastEvents(past);
    } catch (error) {
      console.log("error", error);
    } finally {
      setskeletonLoader(false);
    }
  };

  ////to fetch data and runs when selectedDate changes,
  useEffect(() => {
    init();
  }, [selectedDate]);

  return (
    <div ref={listContainerRef} className="w-full">
      <div
        className={`w-full mb-4  flex justify-${
          ExecutvieLogin || HrLogin ? "between" : "center"
        } items-center`}
      >
        <span className="font-bold">{selectedDate.format("MMM-YYYY")}</span>
        {(ExecutvieLogin || HrLogin) && (
          <ExportToCSV
            allFields={allFields}
            selectedFields={selectedFields}
            setSelectedFields={setSelectedFields}
            formatEmployeeData={formatEmployeeData}
            fileName="employee_occasions"
            visible={isModalVisible}
            onClose={() => setIsModalVisible(false)}
            onOk={handleExportCSV}
            buttonType="link"
          />
        )}
      </div>

      {skeletonLoader ? (
        <Skeleton active />
      ) : (
        <>
          {EmployeeOccasions?.length > 0 && (
            <>
              <div className="text-primary-500 font-semibold">Today</div>
              <List
                itemLayout="horizontal"
                className="anniversaries-box"
                dataSource={EmployeeOccasions}
                grid={{
                  gutter: 16,
                  column: columnCount,
                }}
                style={{ height: "100%" }}
                renderItem={(item) => (
                  <RenderOccastionListItem
                    item={item}
                    showwish={true}
                    showDate={false}
                    email={email}
                  />
                )}
              />
            </>
          )}
          {upcommingEvents?.length > 0 && (
            <>
              <div className="text-primary-500 font-semibold">
                Upcoming Events
              </div>
              <List
                itemLayout="horizontal"
                className="anniversaries-box"
                dataSource={upcommingEvents}
                grid={{
                  gutter: 16,
                  column: columnCount,
                }}
                renderItem={(item) => (
                  <RenderOccastionListItem
                    item={item}
                    showwish={false}
                    showDate={true}
                    email={email}
                  />
                )}
              />
            </>
          )}
          {pastEvents?.length > 0 && (
            <>
              <div className="text-primary-500 font-semibold">Past Events</div>
              <List
                itemLayout="horizontal"
                className="anniversaries-box"
                dataSource={pastEvents}
                grid={{
                  gutter: 16,
                  column: columnCount,
                }}
                renderItem={(item) => (
                  <RenderOccastionListItem
                    item={item}
                    showwish={false}
                    showDate={true}
                    email={email}
                    columnCount={columnCount}
                  />
                )}
              />
            </>
          )}
        </>
      )}
    </div>
  );
}

export default SendWish;

//to render each item in the list of occasions.
const RenderOccastionListItem = ({
  item,
  showwish,
  showDate,
  email,
  columnCount,
}) => {
  const [sendWishButtonLoader, setSendWishButtonLoader] = useState(false);
  const employeeDetailsData = useSelector(getEmployeeDetails);

  async function handleWish(to, type) {
    setSendWishButtonLoader(true);
    let inputData = {
      wishToId: to?.email,
      wishFromId: email,
      comments: Wishes[type],
      expiry: Number(moment().add("day", 3).endOf("day").format("X")),
    };

    createWishAction(inputData)
      .then((res) => {
        var cookieName = `wish-to-${to?.email}`;
        var cookieValue = "true";
        var expirationHours = 24;
        setCookie(cookieName, cookieValue, expirationHours);

        const emailData = employeeDetailsData?.email.split("@")[0];

        const data = {
          type: "Wish",
          message: `${employeeDetailsData?.first_name} wished you on your ${WishesNotification[type]}`,
          toAccount: to?.email,
          actionText: `View ${employeeDetailsData?.first_name}'s Profile`,
          actionPathName: `/employee-view/${emailData}`,
        };

        createGlobalNotification(data);
        setSendWishButtonLoader(false);
        message.success("Wish delivered successfully!");
      })
      .catch((err) => {
        setSendWishButtonLoader(false);
        message.error("Unable Wish at this time");
      });
  }

  const isWished = getCookie(`wish-to-${item?.email}`);
  return (
    <List.Item
      actions={
        showwish &&
        !isWished && [
          <Button
            loading={sendWishButtonLoader}
            onClick={() => {
              handleWish(
                item,
                item?.isBirthDate
                  ? "birthday"
                  : item?.isWork
                  ? "work"
                  : "wedding"
              );
            }}
            size="small"
            className="text-smallest"
          >
            Wish
          </Button>,
        ]
      }
    >
      <div className={columnCount > 1 ? `${boxClass}` : ""}>
        <List.Item.Meta
          title={
            <div className="block items-center ">
              <RenderEmployeeFullName
                employee={item}
                showAvatar={true}
                avatarSize={35}
                className="flex items-center gap-3 "
                avatarClassName="mt-2"
                showPopover
              />
              <div className="ml-12 -mt-4  text-gray-500">
                {showDate && <small>({item?.date})</small>}
              </div>
            </div>
          }
          description={
            <>
              <div className="ml-12">{item?.occasion}</div>
            </>
          }
        />
      </div>
    </List.Item>
  );
};
