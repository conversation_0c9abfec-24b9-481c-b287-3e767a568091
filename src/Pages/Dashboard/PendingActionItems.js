import React, { useEffect, useState } from "react";
import {
  Typo<PERSON>,
  List,
  Badge,
  Button,
  Empty,
  message,
  Divider,
  Tooltip,
  Tag,
  Popconfirm,
  Select,
} from "antd";
import {
  CheckOutlined,
  UserOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import {
  listExecutableHistoriesPendingAction,
  listExecutablesPendingItemsAction,
  listExecutionRulesPendingItemsAction,
  updateExecutableAction,
} from "Pages/Dashboard/Actions/PendingItemsAction";
import { useSelector } from "react-redux";
import { getEmployeeDetails } from "store/slices/loginSlice";
import moment from "moment";
import { DateFormatWithTime } from "utils/constants";
import InfiniteScroll from "react-infinite-scroll-component";
import Loader from "Commons/Loader";

const { Title, Text } = Typography;

const PendingActionItems = ({ isCustomizing }) => {
  // Selector
  const employeeDetails = useSelector(getEmployeeDetails);

  // States
  const [loading, setLoading] = useState(false);
  const [completingItems, setCompletingItems] = useState(new Set());
  const [executableItems, setExecutableItems] = useState([]);
  const [historyItems, setHistoryItems] = useState([]);
  const [hasMoreHistory, setHasMoreHistory] = useState(true);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [executionRules, setExecutionRules] = useState([]);
  const [historyFilter, setHistoryFilter] = useState("all");

  // Initial data fetch
  useEffect(() => {
    loadAllItems();
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**
   * Loads all the action items for the user.
   * @param {boolean} refresh whether to refresh the data or not
   * @returns {Promise<void>}
   */
  const loadAllItems = async () => {
    setLoading(true);
    try {
      const filterForExecution = {
        or: (employeeDetails.groups || []).map((groupId) => ({
          executionRuleResponsibleGroupId: { eq: groupId },
        })),
      };

      const executionRules = await listExecutionRulesPendingItemsAction(
        filterForExecution
      );
      setExecutionRules(executionRules);

      const filterForExecutable = {
        or: executionRules.map((executable) => ({
          executableRuleId: { eq: executable.id },
        })),
      };

      const allExecutables = await listExecutablesPendingItemsAction(
        filterForExecutable
      );
      //TODO:remove once the backend is fixed
      const filterForHistory = {
        or: executionRules.map((executable) => ({
          executableHistoryRuleId: { eq: executable.id },
        })),
      };
      const executableHistory = await listExecutableHistoriesPendingAction(
        filterForHistory
      );

      setExecutableItems(allExecutables);

      setHistoryItems(executableHistory);
    } catch (error) {
      console.error("Error fetching action items:", error);
      message.error("Failed to load action items. Please try again later.");
    } finally {
      setLoading(false);
    }
  };
  /**
   * Return the name of the responsible group for the given rule. If the rule has the property directly, use it. Otherwise,
   * find the rule in the list of executionRules by responsibleGroupId and return its responsibleGroup.name.
   * If the rule has no responsible group, return "Unassigned".
   * @param rule {Object} The rule to find the responsible group for.
   * @returns {String} The name of the responsible group.
   */
  const getResponsibleGroupName = (rule) => {
    if (rule?.responsibleGroup?.name) return rule.responsibleGroup.name;
    const groupId = rule?.executionRuleResponsibleGroupId;
    if (!groupId) return "Unassigned";
    const foundRule = executionRules.find(
      (r) => r.executionRuleResponsibleGroupId === groupId
    );
    return foundRule?.responsibleGroup?.name || "Unassigned";
  };

  /**
   * Completes the given item. If the item has a completionType of "Manual", attempts to update the item's status to
   * "COMPLETED" and sets the metaData.completionBy to the employee's email. If the update is successful, updates the
   * local state of the executable items to reflect the completed item. In case of an error, shows an error message.
   * @param item {Object} The item to complete.
   */
  const handleCompleteItem = async (item) => {
    if (item.rule?.completionType !== "Manual") return;

    setCompletingItems((prev) => new Set([...prev, item.id]));

    try {
      await updateExecutableAction({
        id: item.id,
        metaData: JSON.stringify({
          completionBy: employeeDetails.email,
        }),
        status: "COMPLETED",
      });

      setExecutableItems((prev) =>
        prev.map((ex) =>
          ex.id === item.id ? { ...ex, status: "COMPLETED" } : ex
        )
      );

      message.success(`"${item.rule.title}" marked as completed!`);
    } catch (error) {
      console.error("Error completing item:", error);
      message.error("Failed to complete item. Please try again.");
    } finally {
      setCompletingItems((prev) => {
        const newSet = new Set(prev);
        newSet.delete(item.id);
        return newSet;
      });
    }
  };

  /**
   * Returns true if the given due date is overdue (i.e. it has passed), and false otherwise.
   * @param {number} dueDate The due date in seconds since the epoch. If not provided, returns false.
   */
  const isOverdue = (dueDate) => {
    if (!dueDate) return false;
    return dueDate * 1000 < Date.now();
  };

  /**
   * Renders a pending action item as a list item with relevant details and actions.
   *
   * @param {Object} item - The action item to be rendered.
   * @param {string} item.id - Unique identifier for the action item.
   * @param {string} item.status - Current status of the action item (e.g., "PENDING").
   * @param {Object} item.rule - Rules associated with the action item.
   * @param {string} [item.rule.title] - Title of the action item.
   * @param {string} [item.rule.completionType] - Type of completion for the action item (e.g., "Manual", "Auto").
   * @param {string} [item.rule.description] - Description of the action item.
   * @param {number} item.expiry - Expiry date of the action item in seconds since the epoch.
   *
   * @returns {JSX.Element} A list item component displaying the action item with options to mark as completed if applicable.
   */

  const renderPendingItem = (item) => {
    const canComplete =
      item.status === "PENDING" && item.rule?.completionType === "Manual";
    const overdue = isOverdue(item.expiry);

    return (
      <List.Item key={item.id} className="w-full">
        <div className="w-full">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 w-full">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2 justify-between w-full">
                <div className="flex items-center gap-2">
                  <Title level={5} className="mb-0 text-gray-800">
                    {item.rule?.title || "Unknown Task"}
                  </Title>
                  <Tooltip title="Completion type">
                    <Tag
                      color={
                        item.rule?.completionType === "Auto" ? "gray" : "green"
                      }
                      className="ml-2 mb-1"
                    >
                      {item.rule?.completionType || "Manual"}
                    </Tag>
                  </Tooltip>
                </div>
                {canComplete && (
                  <Popconfirm
                    title="Are you sure you want to mark this item as completed?"
                    onConfirm={() => handleCompleteItem(item)}
                    okText="Yes"
                    cancelText="No"
                    placement="left"
                  >
                    <Tooltip title="Mark as Completed">
                      <Button
                        type="primary"
                        shape="circle"
                        size="small"
                        icon={<CheckOutlined />}
                        loading={completingItems.has(item.id)}
                      />
                    </Tooltip>
                  </Popconfirm>
                )}
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <UserOutlined />
                  <span>{getResponsibleGroupName(item.rule)}</span>
                </div>

                <div className="flex items-center gap-1">
                  <CalendarOutlined />
                  <Tooltip title="This is the due/expiry date for this action item">
                    <span className={overdue ? "text-red-500 font-medium" : ""}>
                      {moment(item.expiry * 1000).format(DateFormatWithTime)}
                      {overdue && " (Overdue)"}
                    </span>
                  </Tooltip>
                </div>
              </div>

              {item.rule?.description && (
                <Text className="text-gray-500 text-sm mt-1 block">
                  {item.rule.description}
                </Text>
              )}
            </div>
          </div>
        </div>
      </List.Item>
    );
  };

  /**
   * Render a completed action item in the list of completed items.
   *
   * @param {Object} item - The completed action item
   * @returns {React.ReactElement} The completed action item component
   */
  const renderCompletedItem = (item) => (
    <>
      <List.Item key={item.id}>
        <div>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <Title level={5} className="mb-0 text-gray-700">
                  {item.rule?.title || "Unknown Task"}
                </Title>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-sm text-gray-500">
                <span>
                  Completion Date:{" "}
                  {moment(item.createdAt).format(DateFormatWithTime) ||
                    "Unknown"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </List.Item>
      <Divider style={{ margin: 0 }} />
    </>
  );

  // Filtered history items based on filter
  const getFilteredHistoryItems = () => {
    // TODO: Set this history with Infinite Scroll
    if (historyFilter === "all") return historyItems;
    const now = moment();
    let fromDate;
    if (historyFilter === "7days") {
      fromDate = now.clone().subtract(7, "days");
    } else if (historyFilter === "30days") {
      fromDate = now.clone().subtract(30, "days");
    }
    return historyItems.filter((item) =>
      moment(item.createdAt).isAfter(fromDate)
    );
  };

  const BATCH_SIZE = 2;
  const [historyBatchIndex, setHistoryBatchIndex] = useState(0);

  /**
   * Fetches a batch of completed action items.
   * @param {boolean} [isInitial=false] - Whether this is the initial fetch or not.
   * If true, it clears the existing history items and fetches from the start.
   * If false, it fetches the next batch of items and appends to the existing items.
   */
  const fetchHistoryItemsBatch = async (isInitial = false) => {
    if (loadingHistory) return;
    setLoadingHistory(true);
    try {
      if (!executionRules.length) {
        setLoadingHistory(false);
        return;
      }
      // Calculate batch
      const start = historyBatchIndex * BATCH_SIZE;
      const end = start + BATCH_SIZE;
      const batchRules = executionRules.slice(start, end);

      if (batchRules.length === 0) {
        setHasMoreHistory(false);
        setLoadingHistory(false);
        return;
      }

      const filterForHistory = {
        or: batchRules.map((executable) => ({
          executableHistoryRuleId: { eq: executable.id },
        })),
      };

      const response = await listExecutableHistoriesPendingAction(
        filterForHistory
      );
      const items = response?.items || [];
      setHistoryItems((prev) => (isInitial ? items : [...prev, ...items]));
      setHistoryBatchIndex((prev) => prev + 1);
      setHasMoreHistory(end < executionRules.length);
    } catch (error) {
      message.error("Failed to load completed items.");
    } finally {
      setLoadingHistory(false);
    }
  };

  // Initial fetch for completed items
  useEffect(() => {
    setHistoryBatchIndex(0);
    fetchHistoryItemsBatch(true);
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [executionRules]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-4 space-y-6">
      {/* Pending Items Section */}
      <div>
        <div className="flex items-center justify-between ">
          <Title level={4} className="mb-0 text-gray-800">
            Pending Action Items
          </Title>
          <Badge count={executableItems.length} color="red" className="mb-1" />
        </div>

        {executableItems.length === 0 ? (
          <Empty
            description="No pending action items"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            className="py-8"
          />
        ) : (
          <List dataSource={executableItems} renderItem={renderPendingItem} />
        )}
      </div>

      <Divider />

      {/* Completed Items Section */}
      <div>
        <div className="flex items-center justify-between ">
          <Title level={4} className="mb-0 text-gray-800">
            Completed Items
          </Title>
          <Select
            value={historyFilter}
            onChange={setHistoryFilter}
            className="w-36"
            options={[
              { value: "all", label: "All" },
              { value: "7days", label: "Last 7 Days" },
              { value: "30days", label: "Last 30 Days" },
            ]}
            size="small"
          />
        </div>

        {getFilteredHistoryItems().length === 0 && !loadingHistory ? (
          <Empty
            description="No completed items"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            className="py-8"
          />
        ) : (
          <div
            id="completedScrollContainer"
            className="max-h-[50vh] overflow-auto"
          >
            {/*TODO:  Need to change  InfiniteScroll parameter   */}
            <InfiniteScroll
              dataLength={getFilteredHistoryItems().length}
              next={() => fetchHistoryItemsBatch(false)}
              hasMore={hasMoreHistory}
              loader={
                <div className="flex justify-center align-middle ">
                  <Loader />
                </div>
              }
              endMessage={<Divider plain>No more completed items</Divider>}
              scrollableTarget="completedScrollContainer"
            >
              <List
                dataSource={getFilteredHistoryItems()}
                renderItem={renderCompletedItem}
              />
            </InfiniteScroll>
          </div>
        )}
      </div>
    </div>
  );
};

export default PendingActionItems;
