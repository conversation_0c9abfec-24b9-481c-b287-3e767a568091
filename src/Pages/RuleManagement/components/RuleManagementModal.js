import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Row,
  Col,
  Divider,
  message,
  Switch,
} from "antd";
import {
  createExecutionRuleManagementAction,
  updateExecutionRuleManagementAction,
} from "Pages/RuleManagement/Actions/RuleManagementAction";

const RuleManagementModal = ({
  triggerKey,
  editingRule,
  allGroups,
  setRules,
}) => {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (triggerKey) setVisible(true);
  }, [triggerKey]);

  useEffect(() => {
    if (visible) {
      if (editingRule) {
        form.setFieldsValue({
          title: editingRule.title,
          description: editingRule.description,
          frequency: editingRule.frequency,
          allowedDelay: editingRule.allowedDelay || 0,
          completionType: editingRule.completionType || "Manual",
          alertGroups: editingRule.executionRuleAuthorityGroupId,
          responsibleGroup: editingRule.executionRuleResponsibleGroupId,
          status: editingRule.status === true || editingRule.status === "true",
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          status: true,
          completionType: "Manual",
          allowedDelay: 0,
        });
      }
    }
  }, [visible, editingRule, form]);

  const frequencyOptions = useMemo(
    () => [
      { value: "DAILY", label: "Daily" },
      { value: "WEEKLY", label: "Weekly" },
      { value: "MONTHLY", label: "Monthly" },
      { value: "QUARTERLY", label: "Quarterly" },
    ],
    []
  );

  const groupOptions = useMemo(
    () =>
      allGroups.map((group) => ({
        value: group.id,
        label: group.name,
      })),
    [allGroups]
  );

  const onCancel = useCallback(() => {
    form.resetFields();
    setVisible(false);
    setLoading(false);
  }, [form]);

  const handleSubmit = useCallback(async () => {
    try {
      setLoading(true);

      const values = form.getFieldsValue();
      let ruleData = {
        title: values.title,
        description: values.description,
        frequency: values.frequency,
        allowedDelay: values.allowedDelay || 0,
        completionType: values.completionType || "Manual",
        executionRuleAuthorityGroupId: values.alertGroups,
        executionRuleResponsibleGroupId: values.responsibleGroup,
        status: values.status,
      };
      if (editingRule) {
        ruleData = {
          ...ruleData,
          id: editingRule.id,
        };
        await updateExecutionRuleManagementAction(ruleData);

        const responsibleGroupObj = allGroups.find(
          (g) => g.id === values.responsibleGroup
        );

        setRules((prev) =>
          prev.map((r) =>
            r.id === editingRule.id
              ? {
                  ...r,
                  ...ruleData,
                  responsibleGroup: responsibleGroupObj,
                }
              : r
          )
        );
      } else {
        const createdRule = await createExecutionRuleManagementAction(ruleData);
        const responsibleGroupObj = allGroups.find(
          (g) => g.id === values.responsibleGroup
        );
        setRules((prev) => [
          {
            ...ruleData,
            id: createdRule.id,
            responsibleGroup: responsibleGroupObj,
          },
          ...prev,
        ]);
      }
    } catch (error) {
      console.error("Error creating/updating rule:", error);
      message.error("Failed to create/update rule. Please try again.");
    } finally {
      onCancel();
    }
  }, [form, editingRule, allGroups, setRules, onCancel]);

  return (
    <Modal
      title={editingRule ? "Edit Rule" : "Create New Rule"}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      width={700}
      style={{ top: 20 }}
      okText={editingRule ? "Update" : "Create"}
      confirmLoading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          status: true,
          completionType: "Manual",
          allowedDelay: 0,
        }}
      >
        <Form.Item
          name="title"
          label="Rule Name"
          rules={[{ required: true, message: "Please enter a rule name" }]}
        >
          <Input placeholder="Enter rule name" />
        </Form.Item>

        <Form.Item name="description" label="Description">
          <Input.TextArea rows={2} placeholder="Enter rule description" />
        </Form.Item>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="frequency"
              label="Frequency"
              rules={[{ required: true, message: "Please select frequency" }]}
            >
              <Select
                placeholder="Select frequency"
                options={frequencyOptions}
                className="w-full"
                dropdownMatchSelectWidth={false}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="allowedDelay" label="Allowed Delay (days)">
              <InputNumber min={0} className="w-full" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="completionType"
              label="Completion Type"
              rules={[
                { required: true, message: "Please select completion type" },
              ]}
            >
              <Select
                placeholder="Select completion type"
                options={[
                  { value: "MANUAL", label: "Manual" },
                  { value: "AUTOMATIC", label: "Auto" },
                ]}
                className="w-full"
                dropdownMatchSelectWidth={false}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="responsibleGroup"
              label="Responsible Group"
              rules={[
                { required: true, message: "Please select responsible group" },
              ]}
            >
              <Select
                placeholder="Select responsible group"
                options={groupOptions}
                className="w-full"
                dropdownMatchSelectWidth={false}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="status"
          label="Status"
          valuePropName="checked"
          style={{ marginBottom: 0 }}
        >
          <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
        </Form.Item>

        <Divider orientation="left">Alert Target</Divider>

        <Form.Item name={"alertGroups"} label="Target Groups">
          <Select
            placeholder="Select target groups"
            options={groupOptions}
            className="w-full"
            dropdownMatchSelectWidth={false}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default RuleManagementModal;
