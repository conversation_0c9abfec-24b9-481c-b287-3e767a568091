export const listExecutionRulesManagement = /* GraphQL */ `
  query ListExecutionRules(
    $filter: ModelExecutionRuleFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listExecutionRules(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        frequency
        allowedDelay
        responsibleGroup {
          id
          name
          description
          permissions
          leaderEmail
          createdAt
          updatedAt
          __typename
        }
        authorityGroup {
          id
          name
          description
          permissions
          leaderEmail
          createdAt
          updatedAt
          __typename
        }
        completionType
        status
        ruleDefination
        createdAt
        updatedAt
        executionRuleResponsibleGroupId
        executionRuleAuthorityGroupId
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const listExecutablesRuleManagement = /* GraphQL */ `
  query ListExecutables(
    $filter: ModelExecutableFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listExecutables(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        rule {
          id
          title
          description
          frequency
          allowedDelay
          completionType
          createdAt
          updatedAt
          executionRuleResponsibleGroupId
          executionRuleAuthorityGroupId
          __typename
        }
        expiry
        status
        metaData
        createdAt
        updatedAt
        executableRuleId
        employeeEmail
        __typename
      }
      nextToken
      __typename
    }
  }
`;

export const listEmployeesRuleManagement = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listEmployees(
      email: $email
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        email
        employee_id
        account_status
        first_name
        last_name
        profile_pic
      }
      nextToken
      __typename
    }
  }
`;
export const listGroupPermissionsRuleManagement = /* GraphQL */ `
  query ListGroups(
    $filter: ModelGroupFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listGroups(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        description
        permissions
        assignments {
          nextToken
          __typename
        }
        leaderEmail
        leader {
          email
          employee_id
          account_status
          first_name
          last_name
          profile_pic
          __typename
        }
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const createExecutionRuleManagement = /* GraphQL */ `
  mutation CreateExecutionRule(
    $input: CreateExecutionRuleInput!
    $condition: ModelExecutionRuleConditionInput
  ) {
    createExecutionRule(input: $input, condition: $condition) {
      id
      __typename
    }
  }
`;
export const updateExecutionRuleManagement = /* GraphQL */ `
  mutation UpdateExecutionRule(
    $input: UpdateExecutionRuleInput!
    $condition: ModelExecutionRuleConditionInput
  ) {
    updateExecutionRule(input: $input, condition: $condition) {
      id
      __typename
    }
  }
`;
