import {
  createExecutionRuleManagement,
  listEmployeesRuleManagement,
  listExecutablesRuleManagement,
  listExecutionRulesManagement,
  listGroupPermissionsRuleManagement,
  updateExecutionRuleManagement,
} from "Pages/RuleManagement/Queries/RuleManagementQueries";
import { ExecuteMutationV2, ExecuteQueryCustomV2 } from "utils/Api";

export const listExecutionRulesManagementAction = (limit, nextToken = null) => {
  return ExecuteQueryCustomV2(
    listExecutionRulesManagement,
    { nextToken },
    limit
  );
};
export const listExecutablesRuleManagementAction = () => {
  return ExecuteQueryCustomV2(listExecutablesRuleManagement);
};
export const listEmployeesRuleManagementAction = (filter) => {
  return ExecuteQueryCustomV2(listEmployeesRuleManagement, { filter });
};
export const listGroupPermissionsRuleManagementAction = () => {
  return ExecuteQueryCustomV2(listGroupPermissionsRuleManagement);
};
export const createExecutionRuleManagementAction = (input) => {
  return ExecuteMutationV2(createExecutionRuleManagement, {
    input,
  });
};
export const updateExecutionRuleManagementAction = (input) => {
  return ExecuteMutationV2(updateExecutionRuleManagement, {
    input,
  });
};
