import { Button, message, Switch, Tag, Tooltip, Select, Space } from "antd";
import { EditOutlined, PlusOutlined } from "@ant-design/icons";
import PageHeader from "AtomicComponents/PageHeader";
import CustomTable from "Commons/CustomTable";
import React, { useEffect, useState, useMemo, useCallback } from "react";
import RuleManagementModal from "Pages/RuleManagement/components/RuleManagementModal";
import {
  listExecutablesRuleManagementAction,
  listExecutionRulesManagementAction,
  listGroupPermissionsRuleManagementAction,
  updateExecutionRuleManagementAction,
} from "Pages/RuleManagement/Actions/RuleManagementAction";

/**
 * RuleManagement component for managing business rules.
 * Handles listing, filtering, editing, and creating rules.
 * @component
 */
const RuleManagement = () => {
  // State variables
  const [loading, setLoading] = useState(false);
  const [editingRule, setEditingRule] = useState(null);
  const [allGroups, setAllGroups] = useState([]);
  const [rules, setRules] = useState([]);
  const [modalTriggerKey, setModalTriggerKey] = useState(null);
  const [statusFilter, setStatusFilter] = useState(null);
  const [frequencyFilter, setFrequencyFilter] = useState(null);
  const [executables, setExecutables] = useState([]);
  const [pageIndex, setPageIndex] = useState(1);
  const [pageSize, setPageSize] = useState(11);
  const [nextToken, setNextToken] = useState(null);
  const [totalRules, setTotalRules] = useState(0);

  /**
   * Fetches rule management data, including rules, groups, and executables.
   * @param {boolean} forceRefresh - If true, resets pagination and reloads all data.
   */
  const fetchRuleManagementData = useCallback(
    async (forceRefresh = false) => {
      try {
        setLoading(true);

        // Avoid unnecessary fetch if not forced and no nextToken
        if (!forceRefresh && !nextToken && rules.length > 0) {
          setLoading(false);
          return;
        }

        // Fetch rules
        const ruleData = await listExecutionRulesManagementAction(
          pageSize,
          forceRefresh ? null : nextToken
        );

        const { items = [], nextToken: newNextToken, total } = ruleData;

        if (forceRefresh) {
          setRules(items);
          setTotalRules(total || items.length);
        } else {
          setRules((prev) => [...prev, ...items]);
        }

        setNextToken(newNextToken || null);

        // Fetch groups and executables if needed
        if (forceRefresh || allGroups.length === 0) {
          const groupData = await listGroupPermissionsRuleManagementAction();
          setAllGroups(groupData || []);

          const executableData = await listExecutablesRuleManagementAction();
          setExecutables(executableData || []);
        }
      } catch (error) {
        console.error("Error fetching rule management data:", error);
        message.error(
          "Failed to fetch rule management data. Please try again later."
        );
      } finally {
        setLoading(false);
      }
    },
    [allGroups.length, nextToken, pageSize, rules.length]
  );

  // Initial data fetch on mount
  useEffect(() => {
    fetchRuleManagementData(true);
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**
   * Adds activeExecutablesCount to each rule.
   */
  const rulesWithExecutableCount = useMemo(
    () =>
      rules?.map((rule) => {
        const count = executables?.filter(
          (exe) => exe.executableRuleId === rule.id
        ).length;
        return { ...rule, activeExecutablesCount: count };
      }),
    [rules, executables]
  );

  /**
   * Filters rules based on status and frequency filters.
   */
  const filteredRules = useMemo(() => {
    return rulesWithExecutableCount.filter((rule) => {
      let statusMatch = true;
      let freqMatch = true;

      if (statusFilter !== null && statusFilter !== undefined) {
        const ruleStatus =
          rule.status === true || rule.status === "true" ? true : false;
        statusMatch = ruleStatus === statusFilter;
      }

      if (frequencyFilter) {
        freqMatch = rule.frequency === frequencyFilter;
      }
      return statusMatch && freqMatch;
    });
  }, [rulesWithExecutableCount, statusFilter, frequencyFilter]);

  /**
   * Handles toggling the status of a rule.
   * @param {string|number} ruleKey - Rule identifier.
   * @param {boolean} checked - New status value.
   */
  const handleStatusToggle = useCallback(
    async (ruleKey, checked) => {
      try {
        const ruleToUpdate = rules.find(
          (r) => r.key === ruleKey || r.id === ruleKey
        );
        if (!ruleToUpdate) return;
        setRules((prevRules) =>
          prevRules.map((r) =>
            r.id === ruleKey ? { ...r, status: checked ? "true" : "false" } : r
          )
        );
        const updatedRule = {
          status: checked ? "true" : "false",
          id: ruleToUpdate.id,
        };

        await updateExecutionRuleManagementAction(updatedRule);

        message.success("Status updated successfully");
      } catch (error) {
        message.error("Failed to update status");
        console.error(error);
      }
    },
    [rules]
  );

  /**
   * Opens the edit modal for a rule.
   * @param {object} record - Rule record to edit.
   */
  const handleEditRule = useCallback((record) => {
    setEditingRule(record);
    setModalTriggerKey(Date.now());
  }, []);

  /**
   * Opens the modal for creating a new rule.
   */
  const showAddModal = useCallback(() => {
    setEditingRule(null);
    setModalTriggerKey(Date.now());
  }, []);

  /**
   * Table columns definition for rules table.
   */
  const columns = useMemo(
    () => [
      {
        title: "Rule Title",
        dataIndex: "title",
        key: "title",
        width: 150,
        render: (text, record) => (
          <div className="font-semibold break-words whitespace-normal leading-5">
            {record.title}
          </div>
        ),
      },
      {
        title: "Description",
        dataIndex: "description",
        key: "description",
        width: 200,
        render: (text) => (
          <div className="break-words whitespace-normal leading-5 text-gray-700">
            {text || "No description provided"}
          </div>
        ),
      },
      {
        title: "Frequency",
        dataIndex: "frequency",
        key: "frequency",
        width: 120,
        render: (frequency) => (
          <Tag
            color={
              frequency === "Daily"
                ? "blue"
                : frequency === "Weekly"
                ? "green"
                : frequency === "Monthly"
                ? "orange"
                : "default"
            }
          >
            {frequency}
          </Tag>
        ),
      },
      {
        title: "Responsible Group",
        dataIndex: "responsibleGroup",
        key: "responsibleGroup",
        width: 200,
        render: (group) =>
          group && group.name ? <span>{group.name}</span> : <span>-</span>,
      },
      {
        title: "Active Executables Count",
        dataIndex: "activeExecutablesCount",
        key: "activeExecutablesCount",
        width: 150,
        align: "center",
        render: (count) => <span className="font-semibold">{count || 0}</span>,
      },
      {
        title: "Status",
        dataIndex: "status",
        key: "status",
        width: 100,
        align: "center",
        render: (status, record) => {
          const checked = status === true || status === "true";
          return (
            <Switch
              checked={checked}
              onChange={(checked) => handleStatusToggle(record.id, checked)}
              size="small"
            />
          );
        },
      },
      {
        title: "Actions",
        key: "actions",
        width: 80,
        align: "center",
        render: (text, record) => (
          <Tooltip title="Edit Rule">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditRule(record)}
              size="small"
            />
          </Tooltip>
        ),
      },
    ],
    [handleStatusToggle, handleEditRule]
  );

  return (
    <>
      <PageHeader
        title="Rules Management"
        onlyTitle={true}
        isBeta
        className="mb-2"
      />
      <div className="mt-2">
        <CustomTable
          columns={columns}
          dataSource={filteredRules}
          skeletonLoading={loading}
          scroll={{ x: "max-content" }}
          locale={{ emptyText: "No data available" }}
          title="Rules List"
          Actions={
            <Space>
              {/* Status filter dropdown */}
              <Select
                allowClear
                placeholder="Filter by Status"
                value={statusFilter}
                onChange={setStatusFilter}
                options={[
                  { value: true, label: "Active" },
                  { value: false, label: "Inactive" },
                ]}
              />
              {/* Frequency filter dropdown */}
              <Select
                allowClear
                placeholder="Filter by Frequency"
                value={frequencyFilter}
                onChange={setFrequencyFilter}
                options={[
                  { value: "Daily", label: "Daily" },
                  { value: "Weekly", label: "Weekly" },
                  { value: "Monthly", label: "Monthly" },
                  { value: "Quarterly", label: "Quarterly" },
                ]}
              />
              {/* Button to open create rule modal */}
              <Button
                type="primary"
                onClick={showAddModal}
                icon={<PlusOutlined />}
                disabled={loading}
              >
                Create Rule
              </Button>
            </Space>
          }
          pagination={{
            pageSize,
            current: pageIndex,
            total: nextToken
              ? rules.length + pageSize
              : Math.max(totalRules, rules.length),
            /**
             * Handles pagination changes.
             * @param {number} page - New page number.
             * @param {number} newPageSize - New page size.
             */
            onChange: async (page, newPageSize) => {
              if (loading) return;

              if (newPageSize !== pageSize) {
                setPageSize(newPageSize);
                setPageIndex(1);
                setNextToken(null);
                await fetchRuleManagementData(true);
              } else if (page > pageIndex) {
                if (rules.length >= page * pageSize) {
                  setPageIndex(page);
                } else {
                  setPageIndex(page);
                  await fetchRuleManagementData(false);
                }
              } else {
                setPageIndex(page);
              }
            },
          }}
        />
      </div>

      {/* Modal for creating/editing rules */}
      <RuleManagementModal
        triggerKey={modalTriggerKey}
        editingRule={editingRule}
        setRules={setRules}
        allGroups={allGroups}
      />
    </>
  );
};

export default RuleManagement;
