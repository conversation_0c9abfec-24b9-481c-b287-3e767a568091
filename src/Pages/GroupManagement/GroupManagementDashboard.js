import { EditOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON>over, Tooltip, Typography } from "antd";
import PageHeader from "AtomicComponents/PageHeader";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import CustomTable from "Commons/CustomTable";
import {
  fetchEmployeesForGroupManagement,
  listGroupAssignManagementAction,
  listGroupManagementAction,
} from "Pages/GroupManagement/Actions/GroupManagementActions";
import EmployeePopoverContent from "Pages/GroupManagement/components/EmployeePopoverContent";
import GroupModal from "Pages/GroupManagement/components/GroupModal";
import React, { useEffect, useState, useMemo, useCallback } from "react";

/**
 * GroupManagementDashboard
 * Main dashboard component for managing employee groups.
 * Handles fetching, displaying, and editing group data.
 *
 * @component
 */
const GroupManagementDashboard = () => {
  const { Text } = Typography;

  const [loading, setLoading] = useState(false);
  const [editingGroup, setEditingGroup] = useState(null);
  const [groups, setGroups] = useState([]);
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [allEmployees, setAllEmployees] = useState([]);

  /**
   * Fetches groups, assignments, and employees on mount.
   * Populates state for groups and employees.
   */
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch all groups
        const fetchedGroups = await listGroupManagementAction();
        // Fetch all group assignments
        const groupAssignment = await listGroupAssignManagementAction();
        // Attach assignments to each group
        const groupsWithAssignments = fetchedGroups?.map((group) => {
          const assignments = groupAssignment?.filter(
            (assignment) => assignment.groupId === group.id
          );
          return {
            ...group,
            assignments,
          };
        });

        // Filter for active, non-hidden employees
        const employeesFilter = {
          and: [{ hidden_profile: { ne: true } }, { active: { ne: false } }],
        };
        // Fetch employees
        const fetchedEmployees = await fetchEmployeesForGroupManagement(
          employeesFilter
        );

        setAllEmployees(fetchedEmployees);
        setGroups(groupsWithAssignments);
      } catch (error) {
        // Log error for debugging
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  /**
   * Opens the modal for creating a new group.
   * Resets editing and selected group state.
   */
  const handleModal = useCallback(() => {
    setEditingGroup(null);
    setSelectedGroup(null);
    // Timeout ensures modal opens after state reset
    setTimeout(() => {
      setSelectedGroup({});
    }, 0);
  }, []);

  /**
   * Opens the modal for editing an existing group.
   * Processes group data to include member details.
   * @param {Object} record - The group record to edit
   */
  const handleEditGroup = useCallback(
    (record) => {
      // Map assignments to member details for the modal
      const processedGroup = {
        ...record,
        title: record.name,
        members:
          record.assignments?.map((assignment) => {
            const employee = allEmployees.find(
              (emp) => emp.email === assignment.user?.email
            );
            return {
              email: assignment.user?.email,
              first_name: employee?.first_name || "",
              last_name: employee?.last_name || "",
              profile_pic: employee?.profile_pic,
              employee_id: employee?.employee_id,
            };
          }) || [],
      };

      setEditingGroup(processedGroup);
      setSelectedGroup(processedGroup);
    },
    [allEmployees]
  );

  /**
   * Utility to get member details from assignments.
   * @param {Array} assignments
   * @returns {Array} Array of member objects with details
   */
  const getMembersWithDetails = (assignments) => {
    return assignments?.map((assignment) => ({
      first_name: assignment.user?.first_name || "",
      last_name: assignment.user?.last_name || "",
      email: assignment.user?.email || "",
      profile_pic: assignment.user?.profile_pic || null,
      employee_id: assignment.user?.employee_id || "",
    }));
  };

  /**
   * Table columns definition for groups table.
   * Uses useMemo for performance.
   */
  const columns = useMemo(
    () => [
      {
        title: "Title",
        dataIndex: "name",
        key: "name",
        render: (text) => <Text strong>{text}</Text>,
      },
      {
        title: "Description",
        dataIndex: "description",
        key: "description",
        ellipsis: true,
        render: (text) => (
          <Tooltip title={text || "No description available"}>
            <Text>{text || "No description available"}</Text>
          </Tooltip>
        ),
      },
      {
        title: "Group Leader",
        dataIndex: "leader",
        key: "leader",
        render: (leader) =>
          leader ? (
            <RenderEmployeeFullName
              employee={leader}
              showAvatar={true}
              showPopover={true}
              hoverClassName="hover:text-primary-500"
              className="flex items-center gap-2"
            />
          ) : (
            <Text type="secondary">No leader assigned</Text>
          ),
      },
      {
        title: "Employee Count",
        dataIndex: "assignments",
        key: "memberCount",
        render: (assignments) => {
          // Show member count and popover with member details
          const membersWithDetails = getMembersWithDetails(assignments);
          const memberCount = membersWithDetails?.length;

          return (
            <Popover
              content={<EmployeePopoverContent members={membersWithDetails} />}
              title={
                <div className="px-1 py-1">
                  <Text className="font-semibold">Group Members</Text>
                </div>
              }
              trigger="hover"
              placement="left"
            >
              <Button type="link" size="small">
                {memberCount} member{memberCount !== 1 ? "s" : ""}
              </Button>
            </Popover>
          );
        },
      },
      {
        title: "Actions",
        key: "actions",
        render: (_, record) => (
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditGroup(record)}
          />
        ),
      },
    ],
    // eslint-disable-next-line
    [handleEditGroup, groups, setGroups, allEmployees]
  );

  /**
   * Processes groups data for table rendering.
   * Adds a unique key for each group.
   */
  const processedGroupsData = useMemo(
    () =>
      groups.map((group, index) => ({
        ...group,
        key: group.name || index,
      })),
    [groups]
  );

  /**
   * Table action button for creating a new group.
   */
  const tableActions = useMemo(
    () => (
      <Button type="primary" onClick={handleModal}>
        Create Group
      </Button>
    ),
    [handleModal]
  );

  return (
    <>
      {/* Page header for the group management dashboard */}
      <PageHeader
        title="Group Management Interface "
        onlyTitle={true}
        isBeta
        className="mb-2"
      />

      <div className="mt-2">
        {/* Custom table displaying all groups */}
        <CustomTable
          columns={columns}
          dataSource={processedGroupsData}
          skeletonLoading={loading}
          pagination={false}
          scroll={{ x: "max-content" }}
          locale={{ emptyText: "No data available" }}
          title="Groups List"
          Actions={tableActions}
        />
      </div>

      {/* Modal for creating or editing a group */}
      <GroupModal
        editingGroup={editingGroup}
        selectedGroup={selectedGroup}
        allEmployees={allEmployees}
        setGroups={setGroups}
      />
    </>
  );
};

export default GroupManagementDashboard;
