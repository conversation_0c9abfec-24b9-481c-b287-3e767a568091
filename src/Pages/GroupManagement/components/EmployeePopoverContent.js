import React, { useMemo, useState } from "react";
import { Empty, Input, List } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";

const EmployeePopoverContent = ({ members }) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredMembers = useMemo(() => {
    if (!searchTerm.trim()) return members;

    return members.filter((member) => {
      const fullName = `${member.first_name} ${member.last_name}`.toLowerCase();
      const searchLower = searchTerm.toLowerCase();

      return (
        fullName.includes(searchLower) ||
        member.email.toLowerCase().includes(searchLower) ||
        member.first_name.toLowerCase().includes(searchLower) ||
        member.last_name.toLowerCase().includes(searchLower)
      );
    });
  }, [members, searchTerm]);

  return (
    <div className="w-72">
      {/* Search Input */}
      <div className="mb-3">
        <Input
          prefix={<SearchOutlined className="text-gray-400" />}
          placeholder="Search members..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="rounded-md"
          allowClear
        />
      </div>

      {/* Members List */}
      <div className="max-h-64 overflow-y-auto">
        {filteredMembers.length > 0 ? (
          <List
            dataSource={filteredMembers}
            renderItem={(member) => {
              return (
                <List.Item className="px-0 py-2 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center justify-between w-full">
                    <RenderEmployeeFullName
                      employee={member}
                      showAvatar={true}
                      showPopover={true}
                      avatarSize={32}
                      className="flex items-center gap-2"
                      hoverClassName="hover:text-primary-500"
                    />
                  </div>
                </List.Item>
              );
            }}
          />
        ) : (
          searchTerm && (
            <div className="text-center py-4">
              <Empty
                description={`No members found for "${searchTerm}"`}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </div>
          )
        )}
      </div>

      {/* Footer */}
      <div className="mt-3 pt-2 border-t border-gray-100">
        <span className="text-xs text-gray-500">
          {filteredMembers.length} of {members.length} members
          {searchTerm && ` matching "${searchTerm}"`}
        </span>
      </div>
    </div>
  );
};

export default EmployeePopoverContent;
