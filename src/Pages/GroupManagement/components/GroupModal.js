import React, { useEffect, useState, useMemo, useCallback } from "react";
import { Form, Input, message, Modal, Typography } from "antd";
import { UserOutlined } from "@ant-design/icons";
import RenderEmployeeSelect from "AtomicComponents/RenderEmployeeSelect";
import {
  createGroupManagementAction,
  executeBulkGroupAssignment,
  updateGroupManagementAction,
} from "Pages/GroupManagement/Actions/GroupManagementActions";
import { generateUUID } from "utils/commonMethods";

const { TextArea } = Input;
const { Text } = Typography;

/**
 * GroupModal component for creating and editing group details.
 * Handles group title, description, leader, and member assignments.
 *
 * @param {Object} props
 * @param {Object|null} props.selectedGroup - The currently selected group (if any).
 * @param {Object|null} props.editingGroup - The group being edited (if any).
 * @param {Array} props.allEmployees - List of all employees for selection.
 * @param {Function} props.setGroups - Setter to update the groups list in parent.
 */
const GroupModal = ({
  selectedGroup,
  editingGroup,
  allEmployees,
  setGroups,
}) => {
  const [form] = Form.useForm();
  const [isVisible, setIsVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  // Effect to control modal visibility based on selected group or editing state
  useEffect(() => {
    setIsVisible(Boolean(selectedGroup));
  }, [selectedGroup, editingGroup]);

  // Effect to initialize form fields and original members when modal is opened
  useEffect(() => {
    if (isVisible) {
      if (editingGroup) {
        const originalMemberEmails =
          editingGroup.members?.map((m) => m.email) || [];
        form.setFieldsValue({
          title: editingGroup.title || editingGroup.name,
          description: editingGroup.description || "",
          leader: editingGroup.leader?.email || "",
          members: originalMemberEmails,
        });
      } else {
        form.resetFields();
      }
    }
  }, [editingGroup, isVisible, form]);

  /**
   * Handles form submission for creating or updating a group.
   * Manages assignments for group members and leader.
   */
  const handleSubmit = useCallback(async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      // Arrays to track assignment mutations
      const creates = [];
      const deletes = [];
      const updates = [];
      const createUuidMap = new Map();

      if (editingGroup) {
        // --- Update existing group logic ---

        const currentMembers = values.members || [];
        const originalAssignments = editingGroup.assignments || [];
        const originalAssignmentMap = new Map(
          originalAssignments.map((assignment) => [
            assignment.user?.email,
            assignment,
          ])
        );

        const currentMembersSet = new Set(currentMembers);

        // Identify new assignments to create or update
        currentMembers.forEach((email) => {
          const existingAssignment = originalAssignmentMap.get(email);
          if (!existingAssignment) {
            // New member to be added
            const tempUuid = generateUUID();
            creates.push({ userEmail: email, groupId: editingGroup.id });
            createUuidMap.set(tempUuid, email);
          } else if (
            existingAssignment.user?.email !== email ||
            existingAssignment.groupId !== editingGroup.id
          ) {
            // Existing assignment needs update
            updates.push({
              id: existingAssignment.id,
              userEmail: email,
              groupId: editingGroup.id,
            });
          }
        });

        // Identify assignments to delete
        originalAssignments.forEach((assignment) => {
          const email = assignment.user?.email;
          if (email && !currentMembersSet.has(email)) {
            if (assignment.id) {
              deletes.push(assignment.id);
            }
          }
        });

        // Prepare group update input
        const groupUpdateInput = {
          id: editingGroup.id,
          name: values.title,
          description: values.description,
          leaderEmail: values.leader,
        };
        await updateGroupManagementAction(groupUpdateInput);

        // Execute bulk assignment mutations if needed
        if (creates.length || deletes.length || updates.length) {
          const mutationResults = await executeBulkGroupAssignment({
            creates,
            deletes: deletes.filter(Boolean),
            updates,
          });

          // Build updated assignments list for the group
          const updatedAssignments = [];
          let createResultCounter = 0;

          for (const memberEmail of values.members || []) {
            const createMatch = creates.find(
              (c) => c.userEmail === memberEmail
            );
            if (createMatch) {
              const createKey = `create${createResultCounter}`;
              if (mutationResults[createKey]) {
                updatedAssignments.push({
                  id: mutationResults[createKey].id,
                  user: allEmployees.find((emp) => emp.email === memberEmail),
                  userEmail: memberEmail,
                  groupId: editingGroup.id,
                });
                createResultCounter++;
              }
            } else {
              const existingAssignment = editingGroup.assignments?.find(
                (assignment) =>
                  assignment.user?.email === memberEmail &&
                  !deletes.includes(assignment.id)
              );
              if (existingAssignment) {
                updatedAssignments.push(existingAssignment);
              }
            }
          }

          // Update groups state with new assignments and details
          setGroups((prevGroups) =>
            prevGroups.map((group) =>
              group.id === editingGroup.id
                ? getUpdatedGroup(group, updatedAssignments, values)
                : group
            )
          );
        } else {
          // No assignment changes, only update group details
          setGroups((prevGroups) =>
            prevGroups.map((group) =>
              group.id === editingGroup.id
                ? getUpdatedGroup(group, editingGroup.assignments, values)
                : group
            )
          );
        }
      } else {
        // --- Create new group logic ---

        const newGroup = await createGroupManagementAction({
          name: values.title,
          description: values.description,
          leaderEmail: values.leader,
        });

        const currentMembers = values.members || [];
        currentMembers.forEach((email) => {
          creates.push({ userEmail: email, groupId: newGroup.id });
        });

        let assignments = [];
        if (creates.length) {
          const mutationResults = await executeBulkGroupAssignment({
            creates,
            deletes: [],
          });
          assignments =
            values.members?.map((memberEmail, idx) => {
              const createKey = `create${idx}`;
              return {
                id: mutationResults[createKey]?.id,
                user: allEmployees.find((emp) => emp.email === memberEmail),
                userEmail: memberEmail,
                groupId: newGroup.id,
              };
            }) || [];
        } else {
          assignments = [];
        }

        // Add new group to groups state
        setGroups((prevGroups) => [
          {
            id: newGroup.id,
            name: values.title,
            description: values.description,
            leader: allEmployees.find((emp) => emp.email === values.leader),
            assignments,
          },
          ...prevGroups,
        ]);
      }

      message.success(
        `Group ${editingGroup ? "updated" : "created"} successfully!`
      );

      handleCancel();
    } catch (error) {
      // Handle and display errors gracefully
      console.error("Operation failed:", error);
      message.error(`Failed to ${editingGroup ? "update" : "create"} group`);
    } finally {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editingGroup, form, allEmployees, setGroups]);

  /**
   * Handles modal cancel action.
   * Resets form and closes modal.
   */
  const handleCancel = useCallback(() => {
    form.resetFields();
    setIsVisible(false);
  }, [form]);

  // Memoized modal title for performance
  const modalTitle = useMemo(
    () => (
      <div className="flex items-center space-x-2">
        <UserOutlined />
        <Text strong className="text-lg">
          {editingGroup ? "Edit Group" : "Create New Group"}
        </Text>
      </div>
    ),
    [editingGroup]
  );

  // Helper to update a group object
  const getUpdatedGroup = (group, assignments, values) => ({
    ...group,
    name: values.title,
    description: values.description,
    leader: allEmployees.find((emp) => emp.email === values.leader),
    assignments,
  });

  return (
    <Modal
      title={modalTitle}
      open={isVisible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
      className="group-modal"
      okText={editingGroup ? "Update Group" : "Create Group"}
      cancelText="Cancel"
    >
      <Form form={form} layout="vertical" className="mt-4">
        {/* Group Title Field */}
        <Form.Item
          name="title"
          label={<Text strong>Group Title</Text>}
          rules={[
            { required: true, message: "Please enter group title!" },
            { min: 2, message: "Title must be at least 2 characters!" },
            { max: 50, message: "Title must not exceed 50 characters!" },
          ]}
        >
          <Input placeholder="Enter group title" className="rounded-md" />
        </Form.Item>
        {/* Group Description Field */}
        <Form.Item
          name="description"
          label={<Text strong>Description</Text>}
          rules={[
            {
              max: 500,
              message: "Description must not exceed 500 characters!",
            },
          ]}
        >
          <TextArea
            placeholder="Enter group description (optional)"
            rows={3}
            className="rounded-md"
          />
        </Form.Item>
        {/* Group Leader Field */}
        <Form.Item
          name="leader"
          label={<Text strong>Group Leader</Text>}
          rules={[{ required: true, message: "Please select a group leader!" }]}
        >
          <RenderEmployeeSelect
            employeesList={allEmployees}
            placeholder="Select group leader"
            onChange={(leaderEmail) => {
              // Ensure leader is always included in members list
              const currentMembers = form.getFieldValue("members") || [];
              if (leaderEmail && !currentMembers.includes(leaderEmail)) {
                form.setFieldsValue({
                  members: [...currentMembers, leaderEmail],
                });
              }
            }}
            className="w-full"
          />
        </Form.Item>
        {/* Group Members Field */}
        <Form.Item name="members" label={<Text strong>Group Members</Text>}>
          <RenderEmployeeSelect
            employeesList={allEmployees}
            placeholder="Select group members (optional)"
            mode="multiple"
            onChange={(selectedEmails) => {
              // Ensure leader is always included in members list
              const leaderEmail = form.getFieldValue("leader");
              if (leaderEmail && !selectedEmails.includes(leaderEmail)) {
                form.setFieldsValue({
                  members: [...selectedEmails, leaderEmail],
                });
              } else {
                form.setFieldsValue({
                  members: selectedEmails,
                });
              }
            }}
            className="w-full"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default React.memo(GroupModal);
