export const createGroupsForManagement = /* GraphQL */ `
  mutation CreateGroup(
    $input: CreateGroupInput!
    $condition: ModelGroupConditionInput
  ) {
    createGroup(input: $input, condition: $condition) {
      id
      __typename
    }
  }
`;
export const updateGroupForManagement = /* GraphQL */ `
  mutation UpdateGroup(
    $input: UpdateGroupInput!
    $condition: ModelGroupConditionInput
  ) {
    updateGroup(input: $input, condition: $condition) {
      id
      __typename
    }
  }
`;
export const createGroupAssignmentForManagement = /* GraphQL */ `
  mutation CreateGroupAssignment(
    $input: CreateGroupAssignmentInput!
    $condition: ModelGroupAssignmentConditionInput
  ) {
    createGroupAssignment(input: $input, condition: $condition) {
      userEmail
      groupId
      __typename
    }
  }
`;

export const updateGroupAssignmentForManagement = /* GraphQL */ `
  mutation UpdateGroupAssignment(
    $input: UpdateGroupAssignmentInput!
    $condition: ModelGroupAssignmentConditionInput
  ) {
    updateGroupAssignment(input: $input, condition: $condition) {
      userEmail
      groupId
      __typename
    }
  }
`;

export const deleteGroupAssignmentForManagement = /* GraphQL */ `
  mutation DeleteGroupAssignment(
    $input: DeleteGroupAssignmentInput!
    $condition: ModelGroupAssignmentConditionInput
  ) {
    deleteGroupAssignment(input: $input, condition: $condition) {
      userEmail
      __typename
    }
  }
`;
export const listGroupForManagement = /* GraphQL */ `
  query ListGroups(
    $filter: ModelGroupFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listGroups(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        description
        permissions
        assignments {
          items {
            groupAssignmentsId
            groupId
            id
            updatedAt
            userEmail
          }
        }
        leaderEmail
        leader {
          email
          employee_id
          account_status
          first_name
          last_name
          profile_pic
          __typename
        }
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const listEmployeesForManagement = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listEmployees(
      email: $email
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        employee_id
        account_status
        first_name
        last_name
        email
        profile_pic
      }
      nextToken
      __typename
    }
  }
`;
export const listGroupAssignmentsForManagment = /* GraphQL */ `
  query ListGroupAssignments(
    $filter: ModelGroupAssignmentFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listGroupAssignments(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userEmail
        user {
          email
          employee_id
          account_status
          first_name
          last_name
          profile_pic
          __typename
        }
        groupId
        group {
          id
          name
          description
          permissions
          leaderEmail
          createdAt
          updatedAt
          __typename
        }
        createdAt
        updatedAt
        groupAssignmentsId
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export function buildBulkGroupAssignmentMutation({
  creates = [],
  updates = [],
  deletes = [],
}) {
  let mutationParts = [];
  let variableDefs = [];
  let variables = {};

  // Deletes
  deletes.forEach((id, i) => {
    const alias = `delete${i}`;
    const varName = `deleteId${i}`;
    variableDefs.push(`$${varName}: ID!`);
    mutationParts.push(`
      ${alias}: deleteGroupAssignment(input: { id: $${varName} }) {
        id
      }
    `);
    variables[varName] = id;
  });

  // Creates
  creates.forEach((input, i) => {
    const alias = `create${i}`;
    const varName = `createInput${i}`;
    variableDefs.push(`$${varName}: CreateGroupAssignmentInput!`);
    mutationParts.push(`
      ${alias}: createGroupAssignment(input: $${varName}) {
        id
        userEmail
        groupId
      }
    `);
    variables[varName] = input;
  });

  // Updates
  updates.forEach((input, i) => {
    const alias = `update${i}`;
    const varName = `updateInput${i}`;
    variableDefs.push(`$${varName}: UpdateGroupAssignmentInput!`);
    mutationParts.push(`
      ${alias}: updateGroupAssignment(input: $${varName}) {
        id
        userEmail
        groupId
      }
    `);
    variables[varName] = input;
  });

  const mutation = `
    mutation BulkGroupAssignment(${variableDefs.join(", ")}) {
      ${mutationParts.join("\n")}
    }
  `;
  return { mutation, variables };
}
