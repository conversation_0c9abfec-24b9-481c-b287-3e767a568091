import {
  buildBulkGroupAssignmentMutation,
  createGroupsForManagement,
  deleteGroupAssignmentForManagement,
  listEmployeesForManagement,
  listGroupAssignmentsForManagment,
  listGroupForManagement,
  updateGroupAssignmentForManagement,
  updateGroupForManagement,
} from "Pages/GroupManagement/Queries/GroupManagementQueries";
import {
  ExecuteDynamicMutation,
  ExecuteMutationV2,
  ExecuteQueryCustomV2,
} from "utils/Api";

//create
export const createGroupManagementAction = async (input) => {
  return ExecuteMutationV2(createGroupsForManagement, { input });
};
//update
export const updateGroupManagementAction = async (input) => {
  return ExecuteMutationV2(updateGroupForManagement, { input });
};

//list
export const listGroupManagementAction = async (filter) => {
  return ExecuteQueryCustomV2(listGroupForManagement, { filter });
};
export const fetchEmployeesForGroupManagement = async (filter) => {
  return ExecuteQueryCustomV2(listEmployeesForManagement, { filter });
};

export const listGroupAssignManagementAction = async () => {
  return ExecuteQueryCustomV2(listGroupAssignmentsForManagment);
};

export async function executeBulkGroupAssignment({
  creates,
  updates,
  deletes,
}) {
  const { mutation, variables } = buildBulkGroupAssignmentMutation({
    creates,
    updates,
    deletes,
  });
  return ExecuteDynamicMutation(mutation, variables);
}
