import React, { useMemo, useState } from "react";
import { Calendar, Modal, List, Button, Select, Spin } from "antd";
import moment from "moment";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import { useSelector } from "react-redux";
import {
  getCurrentUserData,
  getEmployeeDetails,
  isExecutive,
  isHr,
  isUnitTeamLeader,
} from "store/slices/loginSlice";
import { ExecuteQueryCustomV2 } from "utils/Api";
import { listEmployeeProjectAllocations } from "graphql/queries";
import { useEffect } from "react";
import { Storage } from "aws-amplify";
import CustomImage from "Commons/CustomImage";
import { getDefaultAvatarImage } from "utils/commonMethods";

export const LeaveCalendar = ({ leaves, leaveLoader }) => {
  const [filterType, setFilterType] = useState(() => {
    // Initialize filter type from localStorage or default to "Team"
    const savedFilter = localStorage.getItem("leaveCalendarFilterType");
    return savedFilter || "Team";
  });
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedDateEmployees, setSelectedDateEmployees] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [userProjectAllocations, setUserProjectAllocations] = useState([]);
  const [userSquad, setUserSquad] = useState(null);

  // Get current user data
  const userData = useSelector(getCurrentUserData);
  const employeeDetails = useSelector(getEmployeeDetails);
  const isExecutiveLogin = useSelector(isExecutive);
  const isHrLogin = useSelector(isHr);
  const isUnitLeader = useSelector(isUnitTeamLeader);

  // Check if user has permission to see all employees filter
  const canSeeAllEmployeesFilter =
    isHrLogin || isExecutiveLogin || isUnitLeader;

  // Save filter type to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem("leaveCalendarFilterType", filterType);
  }, [filterType]);

  // Set appropriate default filter based on user permissions
  useEffect(() => {
    if (userData) {
      const savedFilter = localStorage.getItem("leaveCalendarFilterType");

      if (canSeeAllEmployeesFilter) {
        // HR/Executive/Unit Leaders can use "All" filter
        if (!savedFilter) {
          setFilterType("All");
        } else if (
          savedFilter === "All" ||
          savedFilter === "Team" ||
          savedFilter === "Squad"
        ) {
          setFilterType(savedFilter);
        } else {
          setFilterType("All");
        }
      } else {
        // Regular users can only use "Team" or "Squad"
        if (!savedFilter || savedFilter === "All") {
          setFilterType("Team");
        } else if (savedFilter === "Team" || savedFilter === "Squad") {
          setFilterType(savedFilter);
        } else {
          setFilterType("Team");
        }
      }
    }
  }, [userData, canSeeAllEmployeesFilter]);

  useEffect(() => {
    if (userData?.email) {
      setCurrentUser(userData);
      setUserSquad(employeeDetails?.squad?.name);
      fetchUserProjectAllocations(userData.email);
    }
  }, [userData, employeeDetails]);

  // Fetch user's project allocations
  const fetchUserProjectAllocations = async (email) => {
    try {
      const filter = {
        employeeEmployee_project_allocationId: { eq: email },
      };
      const result = await ExecuteQueryCustomV2(
        listEmployeeProjectAllocations,
        { filter }
      );
      setUserProjectAllocations(result || []);
    } catch (error) {
      console.error("Error fetching project allocations:", error);
    }
  };

  // Filter leaves based on selected filter type
  const filteredLeaves = useMemo(() => {
    let filtered = leaves;

    // Apply team/squad filter
    if (filterType === "Team") {
      const userProjectIds = userProjectAllocations.map(
        (allocation) => allocation.project?.id
      );
      filtered = filtered.filter((leave) => {
        return leave.employee?.employee_project_allocation?.items?.some(
          (allocation) => userProjectIds.includes(allocation.project?.id)
        );
      });
    } else if (filterType === "Squad") {
      filtered = filtered.filter(
        (leave) => leave.employee?.squad?.name === userSquad
      );
    }
    // "All" filter is only available for HR/Executive/Unit Leaders, so no additional filtering needed

    return filtered;
  }, [leaves, filterType, userProjectAllocations, userSquad]);

  // Process leaves for calendar view
  const calendarData = useMemo(() => {
    const dateMap = {};

    filteredLeaves.forEach((leave) => {
      if (
        leave.adjustment_type === "DEBIT" &&
        leave.start_time &&
        leave.end_time
      ) {
        const startDate = moment(leave.start_time);
        const endDate = moment(leave.end_time);

        // Generate all dates in the leave range
        const currentDate = startDate.clone();
        while (currentDate.isSameOrBefore(endDate)) {
          const dateKey = currentDate.format("YYYY-MM-DD");
          if (!dateMap[dateKey]) {
            dateMap[dateKey] = [];
          }
          dateMap[dateKey].push(leave);
          currentDate.add(1, "day");
        }
      }
    });

    return dateMap;
  }, [filteredLeaves]);

  // Get background color based on leave count
  const getBackgroundColor = (count) => {
    if (count === 0) return "transparent";

    if (count <= 2) {
      return "#f6ffed"; // light green
    } else if (count <= 5) {
      return "#fffbe6"; // light yellow
    } else {
      return "#fff2f0"; // light red
    }
  };

  // Calendar date cell renderer
  const dateCellRender = (value) => {
    const dateKey = value.format("YYYY-MM-DD");
    const dayLeaves = calendarData[dateKey] || [];
    const uniqueEmployees = dayLeaves.reduce((acc, leave) => {
      const email = leave.employee?.email;
      if (!acc.find((emp) => emp.email === email)) {
        acc.push(leave.employee);
      }
      return acc;
    }, []);

    return (
      <div
        style={{
          position: "relative",
          height: "100%",
          backgroundColor: getBackgroundColor(uniqueEmployees.length),
          borderRadius: "4px",
          padding: "2px",
        }}
      >
        {uniqueEmployees.length > 0 && (
          <div
            style={{
              position: "absolute",
              top: "2px",
              left: "2px",
              right: "2px",
              display: "flex",
              flexWrap: "wrap",
              gap: "1px",
            }}
          >
            {uniqueEmployees.slice(0, 4).map((employee, index) => (
              <div
                key={employee.email}
                style={{
                  width: "30px",
                  height: "30px",
                  borderRadius: "50%",
                  overflow: "hidden",
                  border: "1px solid #fff",
                }}
              >
                <CustomImage
                  S3Key={employee.profile_pic}
                  src={getDefaultAvatarImage(
                    employee.first_name,
                    employee.last_name
                  )}
                  className="w-full h-full object-cover rounded-full"
                  imgClassName="w-full h-full object-cover rounded-full"
                />
              </div>
            ))}
            {uniqueEmployees.length > 4 && (
              <div
                style={{
                  width: "30px",
                  height: "30px",
                  borderRadius: "50%",
                  backgroundColor: "#1890ff",
                  color: "white",
                  fontSize: "10px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  border: "1px solid #fff",
                  fontWeight: "bold",
                }}
              >
                +{uniqueEmployees.length - 4}
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  // Handle date click
  const onDateSelect = (value) => {
    const dateKey = value.format("YYYY-MM-DD");
    const dayLeaves = calendarData[dateKey] || [];
    const uniqueEmployees = dayLeaves.reduce((acc, leave) => {
      const email = leave.employee?.email;
      if (!acc.find((emp) => emp.email === email)) {
        acc.push(leave.employee);
      }
      return acc;
    }, []);

    setSelectedDate(dateKey);
    setSelectedDateEmployees(uniqueEmployees);
    setIsModalVisible(true);
  };

  // Filter options
  const filterOptions = canSeeAllEmployeesFilter
    ? [
        { value: "All", label: "All" },
        { value: "Team", label: "My Projects" },
        { value: "Squad", label: "My Squad" },
      ]
    : [
        { value: "Team", label: "My Projects" },
        { value: "Squad", label: "My Squad" },
      ];

  return (
    <div>
      {/* Controls */}
      <div style={{ marginBottom: 16 }}>
        <Select
          value={filterType}
          onChange={setFilterType}
          options={filterOptions}
          style={{ width: 120 }}
          placeholder="Filter by"
        />
      </div>

      {/* Calendar View */}
      <div>
        <div style={{ marginBottom: 16 }}>
          <h3>Leave Calendar</h3>
          <div style={{ fontSize: "12px", color: "#666" }}>
            <span style={{ display: "inline-block", marginRight: 16 }}>
              <span
                style={{
                  display: "inline-block",
                  width: "16px",
                  height: "16px",
                  backgroundColor: "#f6ffed",
                  border: "1px solid #d9d9d9",
                  marginRight: 4,
                }}
              ></span>
              ≤2 people (Light Green)
            </span>
            <span style={{ display: "inline-block", marginRight: 16 }}>
              <span
                style={{
                  display: "inline-block",
                  width: "16px",
                  height: "16px",
                  backgroundColor: "#fffbe6",
                  border: "1px solid #d9d9d9",
                  marginRight: 4,
                }}
              ></span>
              3-5 people (Light Yellow)
            </span>
            <span style={{ display: "inline-block", marginRight: 16 }}>
              <span
                style={{
                  display: "inline-block",
                  width: "16px",
                  height: "16px",
                  backgroundColor: "#fff2f0",
                  border: "1px solid #d9d9d9",
                  marginRight: 4,
                }}
              ></span>
              &gt;5 people (Light Red)
            </span>
            <span style={{ display: "inline-block" }}>
              <span
                style={{
                  display: "inline-block",
                  width: "12px",
                  height: "12px",
                  borderRadius: "50%",
                  backgroundColor: "#1890ff",
                  color: "white",
                  fontSize: "8px",
                  textAlign: "center",
                  lineHeight: "12px",
                  marginRight: 4,
                }}
              >
                +
              </span>
              More than 4 people (shows +N)
            </span>
          </div>
        </div>
        <Spin spinning={leaveLoader} tip="Loading leave data...">
          <Calendar
            dateCellRender={dateCellRender}
            onSelect={onDateSelect}
            style={{ backgroundColor: "white", padding: 16, borderRadius: 8 }}
            fullscreen={true}
            headerRender={({ value, type, onChange, onTypeChange }) => {
              const start = 0;
              const current = value.month();
              const months = [
                "Jan",
                "Feb",
                "Mar",
                "Apr",
                "May",
                "Jun",
                "Jul",
                "Aug",
                "Sep",
                "Oct",
                "Nov",
                "Dec",
              ];

              const monthOptions = months.map((month, index) => ({
                label: month,
                value: index,
              }));

              return (
                <div style={{ padding: "8px 0" }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                      }}
                    >
                      <button
                        type="button"
                        onClick={() => {
                          const now = value.clone().month(current - 1);
                          onChange(now);
                        }}
                        style={{
                          border: "none",
                          background: "none",
                          cursor: "pointer",
                          padding: "4px 8px",
                          borderRadius: "4px",
                          fontSize: "14px",
                        }}
                      >
                        ‹
                      </button>
                      <span style={{ fontSize: "16px", fontWeight: "bold" }}>
                        {months[current]} {value.year()}
                      </span>
                      <button
                        type="button"
                        onClick={() => {
                          const now = value.clone().month(current + 1);
                          onChange(now);
                        }}
                        style={{
                          border: "none",
                          background: "none",
                          cursor: "pointer",
                          padding: "4px 8px",
                          borderRadius: "4px",
                          fontSize: "14px",
                        }}
                      >
                        ›
                      </button>
                    </div>
                  </div>
                </div>
              );
            }}
          />
        </Spin>
      </div>

      {/* Employee Details Modal */}
      <Modal
        title={`Employees on Leave - ${
          selectedDate ? moment(selectedDate).format("DD MMM YYYY") : ""
        }`}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsModalVisible(false)}>
            Close
          </Button>,
        ]}
        width={600}
      >
        {selectedDateEmployees.length > 0 ? (
          <List
            dataSource={selectedDateEmployees}
            renderItem={(employee) => (
              <List.Item>
                <RenderEmployeeFullName
                  employee={employee}
                  showAvatar={true}
                  avatarSize={32}
                  showPopover
                />
              </List.Item>
            )}
          />
        ) : (
          <p>No employees on leave for this date.</p>
        )}
      </Modal>
    </div>
  );
};
