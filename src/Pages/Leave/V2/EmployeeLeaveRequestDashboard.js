/* eslint-disable react-hooks/exhaustive-deps */
import {
  CheckOutlined,
  CloseOutlined,
  DragOutlined,
  Loading3QuartersOutlined,
} from "@ant-design/icons";
import { Col, message, Modal, Row, Tag, Input, Card, Typography } from "antd";
import { useSelector } from "react-redux";
import {
  getCurrentUserReportees,
  getEmployeeDetails,
  isExecutive,
  isHr,
  isOnlySL,
  isUnitTeamLeader,
} from "../../../store/slices/loginSlice";
import { useEffect, useRef, useState } from "react";
import { buildReportingEmployeesArray } from "../../MBOV2/Helper/listReportingEmployees";
import {
  createLeaveCustomAction,
  getLeaveRequestAction,
  handleDeleteLeaveRequest,
  listLeaveRequests,
} from "../Actions/LeaveAction";
import {
  capitalizeFirstChar,
  createGlobalNotification,
  getDaysDifference,
  notUndefined,
} from "utils/commonMethods";
import CustomTable from "Commons/CustomTable";
import { formatDatesToStringArray } from "../Helper";
import { ListEmployeesCustomAction } from "utils/Actions";
import { colorMapWithLeaveType } from "../Constants/Constants";
import RenderEmployeeFullName from "AtomicComponents/RenderEmployeeFullName";
import { DateFormat } from "utils/constants";
import moment from "moment";
const { Text } = Typography;

export const EmployeeLeaveRequestDashBoard = ({
  onStatusChange,
  isCustomizing,
}) => {
  const [loading, setLoading] = useState(false);
  const isUserExecutive = useSelector(isExecutive);
  const isUserHR = useSelector(isHr);
  const unitTeamLeader = useSelector(isUnitTeamLeader);
  const squadLeader = useSelector(isOnlySL);

  const [LoaderID, setLoaderID] = useState(null);

  const reportees = useSelector(getCurrentUserReportees);
  const employeeDetails = useSelector(getEmployeeDetails);
  const [LeaveRequests, setLeaveRequests] = useState([]);
  const [isLeaveRejectModalOpen, setIsLeaveRejectModalOpen] = useState(false);
  const [leaveToReject, setLeaveToReject] = useState(null);
  const [rejectionReason, setRejectionReason] = useState("");
  const containerRef = useRef(null);
  const [isCardView, setIsCardView] = useState(false);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const width = entry.contentRect.width;
        setIsCardView(width < 640); // below sm screen
      }
    });

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, []);
  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const checkIncompleteLeaves = (leaveRequests) => {
    const hasIncomplete = leaveRequests?.some(
      (leave) => !leave.completed || leave.status === "pending"
    );
    if (onStatusChange) {
      onStatusChange(hasIncomplete);
    }
  };

  useEffect(() => {
    checkIncompleteLeaves(LeaveRequests);
  }, [LeaveRequests]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const formattedLeaveRequests = await fetchReporteeApiData();
      setLeaveRequests(formattedLeaveRequests);
    } catch (error) {
      console.error("Error fetching leave requests:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchReporteeApiData = async () => {
    let reportingEmps = buildReportingEmployeesArray(reportees);

    if (unitTeamLeader || isUserExecutive || isUserHR) {
      const res = await listLeaveRequests();
      return formatLeaveRequests(res);
    }

    if (squadLeader) {
      const filter = {
        and: [
          { employeeSquadId: { eq: employeeDetails?.employeeSquadId } },
          { hidden_profile: { ne: true } },
          { active: { ne: false } },
        ],
      };
      const res = await ListEmployeesCustomAction(filter);
      let emails = res
        .map((item) => item?.email)
        ?.filter((item) => item !== employeeDetails?.email);

      reportingEmps?.forEach((item) => emails.push(item?.email));
      let leaveRequestFilter = {
        or: emails?.map((email) => ({
          employeeLeaveRequestsId: { eq: email },
        })),
      };
      const res2 = await listLeaveRequests(leaveRequestFilter);
      return formatLeaveRequests(res2);
    }

    return [];
  };

  const formatLeaveRequests = (requests) => {
    return requests.map((request) => ({
      id: request.id,
      date: formatDatesToStringArray([request.start_time, request.end_time]),
      type: request.type,
      leave_length: request.leave_length,
      compoffLeave: request.comp_off
        ? formatDatesToStringArray([
            request?.comp_off?.start_time,
            request?.comp_off?.end_time,
          ])
        : ["-"],
      comment: request.comment,
      status: request.adjustment_type,
      employeeEmail: request.employeeLeaveRequestsId,
      leaveRequestData: request,
      employeeName:
        request.employee?.first_name + " " + request.employee?.last_name,
    }));
  };

  const checkLeaveRequestAlreadyApproved = async (requestId) => {
    try {
      const latestRequests = await getLeaveRequestAction(requestId);
      if (!notUndefined(latestRequests)) {
        message.warning(
          "The Leave Request has already been approved or no longer exists."
        );
        removeFromData(requestId);
        return false;
      }
      return true;
    } catch (error) {
      message.error("Unable to check leave request status");
      return false;
    }
  };

  const deleteRequest = async (row, reason) => {
    setLoaderID(row?.id);
    try {
      const canProcess = await checkLeaveRequestAlreadyApproved(row.id);

      if (!canProcess) return;

      row.employeeLeaveRequestsId = row?.employeeEmail;
      await handleDeleteLeaveRequest(row, {
        deleteCompOff: true,
      });
      let leaveData = {
        type: "Leave Request",
        message: `Your Leave  has been declined. Reason: ${reason}`,
        toAccount: row?.employeeLeaveRequestsId,
      };
      await createGlobalNotification(leaveData);
      removeFromData(row?.id);
    } catch (error) {
      console.error("Error rejecting leave:", error);
      message.error("Unable to delete leave request");
    } finally {
      setLoaderID(null);
      setIsLeaveRejectModalOpen(false);
      setLeaveToReject(null);
      setRejectionReason("");
    }
  };

  const handleApproveLeave = async (data) => {
    setLoaderID(data?.id);
    try {
      const canProcess = await checkLeaveRequestAlreadyApproved(data?.id);
      if (!canProcess) return;

      const { leaveRequestData } = data;
      const diffDays = getDaysDifference(
        leaveRequestData?.start_time,
        leaveRequestData?.end_time
      );

      let compOffDDays = 0;
      if (leaveRequestData?.comp_off?.id) {
        compOffDDays = getDaysDifference(
          leaveRequestData?.comp_off?.start_time,
          leaveRequestData?.comp_off?.end_time
        );
      }
      const currentUser = employeeDetails;
      let leaveInputData = {
        type: leaveRequestData?.type,
        start_time: leaveRequestData?.start_time,
        end_time: leaveRequestData?.end_time,
        adjustment_type: leaveRequestData?.adjustment_type,
        employeeLeavesId: leaveRequestData?.employeeLeaveRequestsId,
        count:
          leaveRequestData?.leave_length !== "FULL_DAY"
            ? 0.5
            : diffDays - compOffDDays,
        description: leaveRequestData?.comment,
        leaveComp_offId: leaveRequestData?.comp_off?.id,
        leave_length: leaveRequestData?.leave_length,
        leaveApproved_byId: employeeDetails?.email,
      };

      await createLeaveCustomAction(leaveInputData);

      let leaveData = {
        type: "Leave Request",
        message: `Your Leave  has been approved by ${capitalizeFirstChar(
          currentUser?.first_name
        )} ${capitalizeFirstChar(currentUser?.last_name)}`,
        toAccount: leaveRequestData?.employeeLeaveRequestsId,
      };
      await createGlobalNotification(leaveData);

      await handleDeleteLeaveRequest(
        { id: leaveRequestData?.id },
        { deleteCompOff: false }
      );

      message.success("Leave approved successfully");
      removeFromData(data?.id);
    } catch (error) {
      console.error("Error approving leave:", error);
      message.error("Unable to approve leave request");
    } finally {
      setLoaderID(null);
    }
  };
  const columnsData = [
    {
      title: "Employee",
      key: "employee",
      render: (record) => {
        return (
          <div>
            <RenderEmployeeFullName
              employee={record?.leaveRequestData?.employee}
              showAvatar={true}
              showPopover
            />
          </div>
        );
      },
    },

    {
      title: "Date",
      dataIndex: "date",
      width: 200,
      render: (record, row) => row.date?.join(" - "),
      defaultSortOrder: "ascend",
      sorter: (a, b) => new Date(a.date[0]) - new Date(b.date[0]),
    },
    {
      title: "Type",
      dataIndex: "type",
      render: (type) => <Tag color={colorMapWithLeaveType[type]}>{type}</Tag>,
    },
    {
      title: "Leave",
      dataIndex: "leave_length",
      render: (text, row) => <Tag>{text && text.replace("_", " ")}</Tag>,
    },
    {
      title: "Comp Off",
      dataIndex: "compoffLeave",
      width: 200,
      render: (record, row) => row.compoffLeave?.join(" - "),
    },
    {
      title: "Status",
      dataIndex: "status",
      width: 100,
      render: (record, row) => (
        <div>
          {LoaderID === row?.id ? (
            <Loading3QuartersOutlined spin />
          ) : (
            <>
              <CheckOutlined
                className="text-primary-500 mr-1 icon-stroke"
                onClick={() => {
                  handleApproveLeave(row);
                }}
              />
              <CloseOutlined
                className="mr-1 text-red-500"
                onClick={() => {
                  setLeaveToReject(row);
                  setIsLeaveRejectModalOpen(true);
                }}
              />
            </>
          )}
        </div>
      ),
    },
  ];

  const removeFromData = (id) => {
    const filteredData = LeaveRequests?.filter((item) => item?.id !== id);
    setLeaveRequests(filteredData);
    checkIncompleteLeaves(filteredData);
  };

  const formattedLeaveDate = leaveToReject?.date?.[0]
    ? leaveToReject.date[0] === leaveToReject.date[1]
      ? moment(leaveToReject.date[0]).format(DateFormat)
      : `${moment(leaveToReject.date[0]).format(DateFormat)} - ${moment(
          leaveToReject.date[1]
        ).format(DateFormat)}`
    : "No date available";

  return (
    <Row className="mb-2 overflow-y-auto" ref={containerRef}>
      <Col span={24}>
        {!isCardView ? (
          <CustomTable
            columns={columnsData}
            dataSource={LeaveRequests}
            bordered
            scroll={{ x: 1000 }}
            tableHeight={500}
            stickyHeader
            skeletonLoading={loading}
            customTitle="Leave Requests To Approve"
            title={
              <div>
                {isCustomizing && (
                  <DragOutlined className="dragHandle cursor-move mr-1" />
                )}
                Leave Requests To Approve
              </div>
            }
          />
        ) : (
          <div className="flex flex-col gap-2 px-1 ">
            {/* Header */}
            <div className="flex items-center font-semibold">
              {isCustomizing && (
                <DragOutlined className="dragHandle cursor-move mr-1" />
              )}
              Leave Requests to Approve
            </div>

            {/* Card Grid */}
            <div className="flex-grow  min-h-0 px-2 ">
              <div className="grid gap-3 p-2 sm:gap-4 sm:grid-cols-[repeat(auto-fit,minmax(220px,1fr))] overflow-y-auto">
                {LeaveRequests.map((leave) => (
                  <Card
                    key={leave.id}
                    className="bg-gray-50 shadow-sm hover:shadow-md transition-shadow relative min-w-0"
                    bordered={false}
                  >
                    <div className="flex flex-col min-w-0">
                      <div className="flex justify-between items-center mb-2 min-w-0">
                        <RenderEmployeeFullName
                          employee={leave.leaveRequestData?.employee}
                          showAvatar
                          avatarSize={20}
                          className="m-0 gap-1 sm:gap-2 text-sm sm:text-base"
                          showPopover
                        />
                        <div className="flex gap-2">
                          <CheckOutlined
                            className="text-green-600 text-base sm:text-lg cursor-pointer"
                            onClick={() => handleApproveLeave(leave)}
                          />
                          <CloseOutlined
                            className="text-red-600 text-base sm:text-lg cursor-pointer"
                            onClick={() => {
                              setLeaveToReject(leave);
                              setIsLeaveRejectModalOpen(true);
                            }}
                          />
                        </div>
                      </div>

                      <div className="text-xs text-gray-400 mb-1 truncate">
                        Requested:{" "}
                        {moment(leave.createdAt).format("DD MMM YYYY, hh:mm A")}
                      </div>

                      <div>
                        <Text className="text-gray-800 text-sm sm:text-base block truncate">
                          {formattedLeaveDate}
                        </Text>
                        <Tag color={colorMapWithLeaveType[leave.type]}>
                          {leave.type}
                        </Tag>
                        <Tag>{leave.leave_length?.replace("_", " ")}</Tag>
                      </div>

                      {Array.isArray(leave.compoffLeave) &&
                        leave.compoffLeave.some((d) => d !== "-") &&
                        leave.type !== "Comp Off" && (
                          <div className="text-sm text-gray-700 mt-1">
                            <Text type="secondary" className="text-xs">
                              Comp Off:
                            </Text>{" "}
                            {leave.compoffLeave.join(" - ")}
                          </div>
                        )}
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )}
        <Modal
          title="Reason to Reject Leave Request"
          visible={isLeaveRejectModalOpen}
          onCancel={() => {
            setIsLeaveRejectModalOpen(false);
            setRejectionReason("");
          }}
          onOk={async () => {
            if (!rejectionReason.trim()) {
              message.error("Please provide a reason for rejection.");
              return;
            }
            await deleteRequest(leaveToReject, rejectionReason);
            setIsLeaveRejectModalOpen(false);
            setRejectionReason("");
          }}
          okText="Reject"
          cancelText="Cancel"
          confirmLoading={LoaderID === leaveToReject?.id}
        >
          <div className="mb-2 text-sm font-medium text-gray-700">
            Please specify reason for rejecting this request:
          </div>
          <Input.TextArea
            rows={4}
            value={rejectionReason}
            onChange={(e) => setRejectionReason(e.target.value)}
            placeholder="Enter rejection reason"
          />
          {leaveToReject && (
            <div className="mt-4 space-y-2 text-sm text-gray-700 ">
              <div>
                <span className="font-medium">Employee:</span>{" "}
                <span className="font-semibold">
                  <RenderEmployeeFullName
                    employee={leaveToReject?.leaveRequestData?.employee}
                    showAvatar={false}
                    showPopover={false}
                  />
                </span>
              </div>
              <div>
                <span className="font-medium">Date:</span>{" "}
                <span className="font-semibold">{formattedLeaveDate}</span>
              </div>
              <div>
                <span className="font-medium">Type:</span>{" "}
                <Tag color={colorMapWithLeaveType[leaveToReject?.type]}>
                  {leaveToReject?.type}
                </Tag>
              </div>
              <div>
                <span className="font-medium">Leave Length:</span>{" "}
                <Tag>{leaveToReject?.leave_length?.replace("_", " ")}</Tag>
              </div>
              {Array.isArray(leaveToReject?.compoffLeave) &&
                leaveToReject.compoffLeave.some((date) => date !== "-") &&
                leaveToReject?.type !== "Comp Off" && (
                  <div>
                    <span className="font-medium">Comp Off:</span>{" "}
                    <span className="font-semibold">
                      {leaveToReject.compoffLeave.join(" - ")}
                    </span>
                  </div>
                )}
            </div>
          )}
        </Modal>
      </Col>
    </Row>
  );
};
