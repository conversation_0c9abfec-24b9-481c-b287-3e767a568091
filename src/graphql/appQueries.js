export const listSkills = /* GraphQL */ `
  query ListSkills(
    $name: String
    $filter: ModelSkillFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listSkills(
      name: $name
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        name
      }
      nextToken
    }
  }
`;

export const listSMES = /* GraphQL */ `
  query ListSMES(
    $name: String
    $filter: ModelSMEFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listSMES(
      name: $name
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        name
      }
      nextToken
    }
  }
`;

export const getEmployee = /* GraphQL */ `
  query GetEmployee($email: String!) {
    getEmployee(email: $email) {
      email
      configurations
      employee_id
      first_name
      last_name
      country
      metadata
      account_status
      hidden_profile
      title {
        id
        name
        level
        overall_level
        createdAt
        updatedAt
      }
      mobile
      blood_group
      emergency_contact_num
      emergency_contact_relation
      married
      spouse_full_name
      reporting_to {
        email
        employee_id
        first_name
        last_name
      }
      skills {
        items {
          id
          employeeID
          skillID
          createdAt
          updatedAt
        }
        nextToken
      }
      SME {
        items {
          id
          employeeID
          sMEID
          createdAt
          updatedAt
        }
        nextToken
      }
      pan_card
      aadhar_card
      form12BB
      experience_letter
      address
      squad {
        name
        budget
        createdAt
        updatedAt
        squadSquad_managerId
      }
      birth_date
      anniversary_date
      employee_project_allocation {
        items {
          project {
            name
            id
          }
          allocation
          createdAt
          employeeEmployee_project_allocationId
          id
          projectEmployee_project_allocationId
          title
          updatedAt
        }
      }
      passport_photo
      address_proof
      resignation_letter
      salary_slip
      salary_slip_1
      salary_slip_2
      personal_email
      york_appointment
      york_agreement
      reffered_by
      career_start_date
      york_start_date
      york_end_date
      profile_pic
      profile_pic_requested
      active
      documents
      gender
      facebook_link
      linkedin_link
      twitter_link
      instagram_link
      usual_starting_time
      sick_leave_balance
      privilege_leave_balance
      epf
      uan
      createdAt
      updatedAt
      squadEmployeeId
      guildEmployeeId
      employeeTitleId
      employeeReporting_toId
      employeeSquadId
      employeeGuildId
      guildEmployeeId
      day_start_time
      day_end_time
      allocatedDesk
      introduction
      wishlist
      isInterviewer
      reward_points
    }
  }
`;

export const listEmployeeSkills = /* GraphQL */ `
  query ListEmployeeSkills(
    $filter: ModelEmployeeSkillFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listEmployeeSkills(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        employeeID
        skillID
      }
      nextToken
    }
  }
`;

export const listEmployeeSMES = /* GraphQL */ `
  query ListEmployeeSMES(
    $filter: ModelEmployeeSMEFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listEmployeeSMES(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        employeeID
        sMEID
      }
      nextToken
    }
  }
`;
