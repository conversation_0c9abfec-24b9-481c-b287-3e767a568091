export const listEmployeesCustom = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listEmployees(
      email: $email
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        email
        employee_id
        first_name
        last_name
        hidden_profile
        country
        title {
          id
          name
          level
        }
        address
        mobile
        blood_group
        emergency_contact_num
        emergency_contact_relation
        married
        spouse_full_name
        reporting_to {
          email
          employee_id
          first_name
          last_name
          squadEmployeeId
          guildEmployeeId
          employeeTitleId
          employeeReporting_toId
          employeeSquadId
        }
        skills {
          items {
            id
            employeeID
            skillID
            createdAt
            updatedAt
          }
          nextToken
        }
        SME {
          items {
            id
            employeeID
            sMEID
            createdAt
            updatedAt
          }
          nextToken
        }
        squad {
          name
          squadSquad_managerId
          productUnitSquadId
        }
        birth_date
        anniversary_date
        passport_photo
        address_proof
        resignation_letter
        salary_slip
        salary_slip_1
        salary_slip_2
        personal_email
        york_appointment
        york_agreement
        reffered_by
        career_start_date
        york_start_date
        york_end_date
        profile_pic
        profile_pic_requested
        active
        documents
        gender
        facebook_link
        linkedin_link
        twitter_link
        instagram_link
        createdAt
        updatedAt
        squadEmployeeId
        guildEmployeeId
        employeeTitleId
        employeeReporting_toId
        employeeSquadId
        introduction
        certification {
          items {
            S3Key
            title
            provider
            url
            provided_date
            id
            type
            createdAt
            updatedAt
            employeeCertificationId
          }
          nextToken
        }
        employee_project_allocation {
          items {
            project {
              id
              name
            }
            allocation
          }
        }
      }
      nextToken
    }
  }
`;

/**
 * Remove from above query
 * checklistItemResponses {
 items {
 id
 response
 createdAt
 updatedAt
 employeeChecklistItemResponsesId
 checklistItemResponseChecklistItemId
 }
 nextToken
 }
 * @ashwin
 */

export const listNoticeBoardsCustom = /* GraphQL */ `
  query ListNoticeBoards(
    $filter: ModelNoticeBoardFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listNoticeBoards(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        expiry
        type
        metaData
        from {
          email
          first_name
          last_name
        }
        createdAt
        updatedAt
        noticeBoardFromId
      }
      nextToken
    }
  }
`;

export const listLeavesCustom = /* GraphQL */ `
  query ListLeaves(
    $filter: ModelLeaveFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listLeaves(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        type
        start_time
        end_time
        adjustment_type
        employee {
          email
          employee_id
          first_name
          last_name
          profile_pic
          active
          employee_project_allocation {
            items {
              project {
                name
                id
              }
              employeeEmployee_project_allocationId
            }
          }
          squad {
            name
          }
        }
        count
        leave_length
        description
        createdAt
        updatedAt
        employeeLeavesId
        leaveComp_offId
      }
      nextToken
    }
  }
`;

export const LeaveByEmployeeCustom = /* GraphQL */ `
  query LeaveByEmployee(
    $employeeLeavesId: String!
    $createdAt: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelLeaveFilterInput
    $limit: Int
    $nextToken: String
  ) {
    LeaveByEmployee(
      employeeLeavesId: $employeeLeavesId
      createdAt: $createdAt
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        type
        start_time
        end_time
        adjustment_type
        comp_off {
          id
          start_time
          end_time
          createdAt
          updatedAt
          compOffLeaveId
          compOffLeave_requestId
          __typename
        }
        employee {
          email
          employee_id
          first_name
          last_name
          profile_pic
          profile_pic_requested
          active
          employee_project_allocation {
            items {
              project {
                name
                id
              }
              employeeEmployee_project_allocationId
            }
          }
          squad {
            name
          }
        }
        approved_by {
          email
          employee_id
          first_name
          last_name
          profile_pic
        }
        employeeLeavesId
        count
        description
        leave_length
        createdAt
        updatedAt
        leaveComp_offId
        __typename
      }
      nextToken
      __typename
    }
  }
`;

export const getEmployeeCustom = /* GraphQL */ `
  query GetEmployee($email: String!) {
    getEmployee(email: $email) {
      email
      configurations
      employee_id
      first_name
      last_name
      account_status
      hidden_profile
      title {
        id
        name
        level
        overall_level
        createdAt
        updatedAt
      }
      mobile
      blood_group
      emergency_contact_num
      emergency_contact_relation
      married
      country
      spouse_full_name
      reporting_to {
        email
        employee_id
        first_name
        last_name
      }
      skills {
        items {
          id
          employeeID
          skillID
          createdAt
          updatedAt
        }
        nextToken
      }
      SME {
        items {
          id
          employeeID
          sMEID
          createdAt
          updatedAt
        }
        nextToken
      }
      pan_card
      aadhar_card
      form12BB
      experience_letter
      address
      squad {
        name
        budget
        createdAt
        updatedAt
        squadSquad_managerId
      }
      birth_date
      anniversary_date
      employee_project_allocation {
        items {
          project {
            name
            id
          }
          allocation
          createdAt
          employeeEmployee_project_allocationId
          id
          projectEmployee_project_allocationId
          title
          updatedAt
        }
      }
      passport_photo
      address_proof
      resignation_letter
      salary_slip
      salary_slip_1
      salary_slip_2
      personal_email
      york_appointment
      york_agreement
      reffered_by
      career_start_date
      york_start_date
      york_end_date
      profile_pic
      profile_pic_requested
      active
      documents
      gender
      facebook_link
      linkedin_link
      twitter_link
      instagram_link
      usual_starting_time
      sick_leave_balance
      wfh_balance
      privilege_leave_balance
      epf
      uan
      createdAt
      updatedAt
      squadEmployeeId
      guildEmployeeId
      employeeTitleId
      employeeReporting_toId
      employeeSquadId
      employeeGuildId
      guildEmployeeId
      day_start_time
      day_end_time
      allocatedDesk
      wishlist
      isInterviewer
      reward_points
      # certification {
      #   items {
      #     S3Key
      #     title
      #     provider
      #     url
      #     provided_date
      #     id
      #     createdAt
      #     updatedAt
      #     employeeCertificationId
      #     email
      #   }
      #   nextToken
      # }
    }
  }
`;

export const listTimeSheetsCustom = /* GraphQL */ `
  query ListTimeSheets(
    $filter: ModelTimeSheetFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listTimeSheets(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        start_time
        approval_status
        end_time
        employeeID
        project {
          id
          name
        }
        employee {
          email
          first_name
          last_name
          profile_pic
          profile_pic_requested
        }
        description
        createdAt
        updatedAt
        employeeTimesheet_entriesId
        timeSheetProjectId
        projectBucket
        related_notifications
      }
      nextToken
    }
  }
`;

export const getTimeSheetCustom = /* GraphQL */ `
  query GetTimeSheet($id: ID!) {
    getTimeSheet(id: $id) {
      id
      start_time
      end_time
      employeeID
      timeSheetProjectId
      projectBucket
      approval_status
    }
  }
`;

export const listLeaveRequestsCustom = /* GraphQL */ `
  query ListLeaveRequests(
    $filter: ModelLeaveRequestFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listLeaveRequests(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        type
        start_time
        end_time
        adjustment_type
        comment
        createdAt
        updatedAt
        employeeLeaveRequestsId
        employee {
          email
          first_name
          last_name
          profile_pic
        }
        leave_length
        comp_off {
          id
          start_time
          end_time
          createdAt
          updatedAt
          compOffLeaveId
          compOffLeave_requestId
        }
      }
    }
  }
`;

export const listWishesCustom = /* GraphQL */ `
  query ListWishes(
    $filter: ModelWishFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listWishes(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        from {
          email
          employee_id
          first_name
          last_name
        }
        comments
        createdAt
        updatedAt
        wishFromId
        wishToId
      }
      nextToken
    }
  }
`;

export const listSquadsCustom = /* GraphQL */ `
  query ListSquads(
    $name: String
    $filter: ModelSquadFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listSquads(
      name: $name
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        name
        budget
        active
        squad_manager {
          email
          employee_id
          first_name
          last_name
          squadEmployeeId
          employeeTitleId
          employeeReporting_toId
          employeeSquadId
          introduction
        }
        employee(filter: { active: { ne: false } }) {
          items {
            email
            active
            employee_id
            first_name
            last_name
            profile_pic
            title {
              id
              name
              level
              overall_level
              createdAt
              updatedAt
            }
            introduction
            employee_project_allocation {
              items {
                allocation
                title
                project {
                  id
                  mrr
                  fixed_cost
                  start_time
                  name
                  end_time
                }
              }
            }
            squadEmployeeId
            employeeTitleId
            employeeReporting_toId
            employeeSquadId
          }
          nextToken
        }
        project {
          items {
            id
            name
            notion_dashboard_link
            notion_backlog_link
            mrr
            fixed_cost
            start_time
            end_time
            status
            time_tracking_required
            notes
            createdAt
            updatedAt
            squadProjectId
            clientProjectsId
            projectProduct_strategistId
            projectProduct_managerId
            projectDev_principalId
            employee_project_allocation {
              items {
                employee {
                  email
                  squadEmployeeId
                }
                allocation
              }
            }
          }
          nextToken
        }
        ProductUnit {
          name
          active
          productUnitUnit_managerId
        }
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const listFestivalLeavesCustom = /* GraphQL */ `
  query ListFestivalLeaves(
    $filter: ModelFestivalLeaveFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listFestivalLeaves(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        year
        name
        date
        day
        id
        createdAt
        updatedAt
        employee {
          email
          employee_id
          first_name
          last_name
          profile_pic
          active
        }
      }
      nextToken
    }
  }
`;

export const listEmployeeSkillsCustom = /* GraphQL */ `
  query ListEmployeeSkills(
    $filter: ModelEmployeeSkillFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listEmployeeSkills(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        employeeID
        skillID
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const listEmployeeSMESCustom = /* GraphQL */ `
  query ListEmployeeSMES(
    $filter: ModelEmployeeSMEFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listEmployeeSMES(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        employeeID
        sMEID
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const listProjectsCustom = /* GraphQL */ `
  query ListProjects(
    $filter: ModelProjectFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listProjects(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        jiraProjectId
        notion_dashboard_link
        notion_backlog_link
        external_slack_channel_id
        internal_slack_channel_id
        project_history {
          items {
            client_POC
            cost
            costType
            createdAt
            employee_project_allocation
            end_time
            id
            ownerID
            projectHistoryDev_principalId
            projectHistoryProduct_managerId
            projectHistoryProduct_strategistId
            projectProject_historyId
            start_time
            dev_principal {
              first_name
              last_name
              email
              profile_pic
            }
            product_manager {
              first_name
              last_name
              email
              profile_pic
            }
            product_strategist {
              first_name
              last_name
              email
              profile_pic
            }
          }
        }
        squad {
          name
          budget
          squadSquad_managerId
          ProductUnit {
            name
            unit_manager {
              first_name
              last_name
            }
          }
        }
        employee_project_allocation {
          items {
            allocation
            title
            employee {
              email
              first_name
              last_name
            }
            id
            employeeEmployee_project_allocationId
            projectEmployee_project_allocationId
          }
          nextToken
        }
        client {
          id
          name
        }
        mrr
        fixed_cost
        budget
        start_time
        end_time
        project_buckets {
          name
          hours
          cost
          isActive
          reminder
        }
        product_strategist {
          email
          employee_id
          first_name
          last_name
          profile_pic
        }
        product_manager {
          email
          employee_id
          first_name
          last_name
          profile_pic
        }
        client_POC
        status
        dev_principal {
          email
          employee_id
          first_name
          last_name
          profile_pic
        }
        ProjectTag {
          items {
            id
            projectTag {
              title
            }
            projectTagID
            updatedAt
            projectID
            createdAt
          }
        }
        time_tracking_required
        notes
        extra_links
        createdAt
        updatedAt
        squadProjectId
        clientProjectsId
        projectProduct_strategistId
        projectProduct_managerId
        project_type
        projectDev_principalId
        meetings {
          items {
            projectMeetingsNotes {
              items {
                notes
                zoomMeeting {
                  start_time
                  topic
                }
              }
            }
          }
        }
      }
      nextToken
    }
  }
`;

export const listProjectsNames = /* GraphQL */ `
  query ListProjects(
    $filter: ModelProjectFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listProjects(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        project_buckets {
          name
          isActive
        }
        status
      }
      nextToken
    }
  }
`;

export const getMBO = /* GraphQL */ `
  query GetMBO($id: ID!) {
    getMBO(id: $id) {
      id
      year
      quarter
      grade
      hard_skill
      hard_skill_grade
      soft_skill
      soft_skill_grade
      trainings
      target
      comments
      status
      is_skill_published
      is_grade_published
      is_comment_published
      createdAt
      updatedAt
      employeeMbosId
      employee {
        career_start_date
        employeeReporting_toId
        first_name
        last_name
      }
    }
  }
`;

export const customListBudget = `query ListBudgets(
  $filter: ModelBudgetFilterInput
  $limit: Int
  $nextToken: String
  ) {
  listBudgets(filter: $filter, limit: $limit, nextToken: $nextToken) {
    items {
      amount
      createdAt
      end_date
      expenses {
        nextToken
        items {
          amount
          createdAt
          date
          description
          id
          invoice
          title
          type
          updatedAt
          employee {
            email
          }
        }
      }
      id
      name
      start_date
      updatedAt
    }
    nextToken
  }
}`;
export const listChecklistItemResponsesCustom = /* GraphQL */ `
  query ListChecklistItemResponses(
    $filter: ModelChecklistItemResponseFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listChecklistItemResponses(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        checklistItem {
          id
          title
          new_hire_only
          type
          createdAt
          updatedAt
        }
        employee {
          email
          employee_id
          first_name
          last_name
          mobile
          blood_group
          emergency_contact_num
          emergency_contact_relation
          married
          spouse_full_name
          pan_card
          aadhar_card
          form12BB
          address
          birth_date
          anniversary_date
          career_start_date
          york_start_date
          york_end_date
          profile_pic
          active
          documents
          gender
          facebook_link
          linkedin_link
          twitter_link
          instagram_link
          epf
          uan
          createdAt
          updatedAt
          squadEmployeeId
          employeeTitleId
          employeeReporting_toId
          employeeSquadId
        }
        response
        createdAt
        updatedAt
        employeeChecklistItemResponsesId
        checklistItemResponseChecklistItemId
      }
      nextToken
    }
  }
`;

export const listBankDetailsCustom = /* GraphQL */ `
  query ListBankDetails(
    $filter: ModelBankDetailsFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listBankDetails(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        bank_name
        account_number
        account_holder_name
        IFSC_code
        branch
        employee {
          email
          employee_id
          first_name
          last_name
        }
        current
        createdAt
        updatedAt
        employeeBank_detailsId
      }
      nextToken
    }
  }
`;

export const listMBOEmployeesCustom = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listEmployees(
      email: $email
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        email
        employee_id
        first_name
        last_name
        createdAt
        updatedAt
        employeeTitleId
        employeeReporting_toId

        employeeSquadId
      }
      nextToken
    }
  }
`;

export const listMeetingSchedulesCustom = /* GraphQL */ `
  query ListMeetingSchedules(
    $filter: ModelMeetingScheduleFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listMeetingSchedules(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        owner
        ownerDetails {
          email
          employee_id
          first_name
          last_name
          mobile
          blood_group
          emergency_contact_num
          emergency_contact_relation
          married
          spouse_full_name
          pan_card
          aadhar_card
          form12BB
          passport_photo
          address_proof
          resignation_letter
          salary_slip
          salary_slip_1
          salary_slip_2
          personal_email
          york_appointment
          york_agreement
          reffered_by
          address
          birth_date
          anniversary_date
          career_start_date
          york_start_date
          york_end_date
          profile_pic
          active
          documents
          gender
          facebook_link
          linkedin_link
          twitter_link
          instagram_link
          usual_starting_time
          epf
          uan
          hidden_profile
          createdAt
          updatedAt
          squadEmployeeId
          employeeTitleId
          employeeReporting_toId

          employeeSquadId
        }
        title
        id
        meetingSchedules_items {
          items {
            id
            with
            withDetails {
              email
              employee_id
              first_name
              last_name
              mobile
              blood_group
              emergency_contact_num
              emergency_contact_relation
              married
              spouse_full_name
              pan_card
              aadhar_card
              form12BB
              passport_photo
              address_proof
              resignation_letter
              salary_slip
              salary_slip_1
              salary_slip_2
              personal_email
              york_appointment
              york_agreement
              reffered_by
              address
              birth_date
              anniversary_date
              career_start_date
              york_start_date
              york_end_date
              profile_pic
              active
              documents
              gender
              facebook_link
              linkedin_link
              twitter_link
              instagram_link
              usual_starting_time
              epf
              uan
              hidden_profile
              createdAt
              updatedAt
              squadEmployeeId
              employeeTitleId
              employeeReporting_toId

              employeeSquadId
            }
            ownerID
            ownerIDDetails {
              email
              employee_id
              first_name
              last_name
              mobile
              blood_group
              emergency_contact_num
              emergency_contact_relation
              married
              spouse_full_name
              pan_card
              aadhar_card
              form12BB
              passport_photo
              address_proof
              resignation_letter
              salary_slip
              salary_slip_1
              salary_slip_2
              personal_email
              york_appointment
              york_agreement
              reffered_by
              address
              birth_date
              anniversary_date
              career_start_date
              york_start_date
              york_end_date
              profile_pic
              active
              documents
              gender
              facebook_link
              linkedin_link
              twitter_link
              instagram_link
              usual_starting_time
              epf
              uan
              hidden_profile
              createdAt
              updatedAt
              squadEmployeeId
              employeeTitleId
              employeeReporting_toId

              employeeSquadId
            }
            scheduled_at
            mom
            meetingScheduleId
            createdAt
            updatedAt
          }
          nextToken
        }
        type
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const listGuildsCustom = /* GraphQL */ `
  query ListGuilds(
    $name: String
    $filter: ModelGuildFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listGuilds(
      name: $name
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        name
        guild_manager {
          email
          first_name
          last_name
          introduction
        }
        employee(filter: { active: { ne: false } }) {
          items {
            first_name
            email
            last_name
            profile_pic
            title {
              name
            }
            introduction
          }
        }
        active
        createdAt
        updatedAt
        guildGuild_managerId
      }
      nextToken
    }
  }
`;

export const listChecklistItemsCustom = /* GraphQL */ `
  query ListChecklistItems(
    $filter: ModelChecklistItemFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listChecklistItems(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        new_hire_only
        type
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const getNotificationsByUserCustom = /* GraphQL */ `
  query GetNotificationByUser(
    $ToAccount: String!
    $limit: Int
    $nextToken: String
  ) {
    GetNotificationByUser(
      ToAccount: $ToAccount
      limit: $limit
      nextToken: $nextToken
      sortDirection: DESC
    ) {
      items {
        id
        ToAccount
        To {
          email
          employee_id
          first_name
          last_name
          profile_pic
          active
          hidden_profile
          createdAt
          updatedAt
          __typename
        }
        AdminNotification
        Type
        Message
        Metadata
        isRead
        timeSensitive
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;

export const getNotificationsByUserTitleCustom = /* GraphQL */ `
  query GetNotificationByUser(
    $ToAccount: String!
    $filter: ModelNotificationFilterInput
  ) {
    GetNotificationByUser(ToAccount: $ToAccount, filter: $filter) {
      items {
        id
        ToAccount
        To {
          email
          employee_id
          first_name
          last_name
          mobile
          blood_group
          emergency_contact_num
          emergency_contact_relation
          married
          spouse_full_name
          pan_card
          aadhar_card
          form12BB
          passport_photo
          address_proof
          resignation_letter
          salary_slip
          salary_slip_1
          salary_slip_2
          personal_email
          york_appointment
          york_agreement
          reffered_by
          address
          birth_date
          anniversary_date
          career_start_date
          york_start_date
          york_end_date
          profile_pic
          active
          documents
          gender
          facebook_link
          linkedin_link
          twitter_link
          instagram_link
          usual_starting_time
          epf
          uan
          hidden_profile
          createdAt
          updatedAt
          squadEmployeeId
          guildEmployeeId
          employeeTitleId
          employeeReporting_toId

          employeeSquadId
          employeeGuildId
          __typename
        }
        AdminNotification
        Type
        Message
        Metadata
        isRead
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;

export const getSpecificInventoryStockByID = /* GraphQL */ `
  query GetInventoryStock($id: ID!) {
    getInventoryStock(id: $id) {
      title
      price_per_item
      stock_count
      configurations
      items {
        asset_tag
        serial_number
        purchased_date
        invoices
        warranty_period
      }
      createdAt
      updatedAt
    }
  }
`;

export const listInventoryStocksCustom = /* GraphQL */ `
  query ListInventoryStocks(
    $filter: ModelInventoryStockFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listInventoryStocks(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        price_per_item
        stock_count
        vendor_name
        configurations
        items {
          asset_tag
          serial_number
          purchased_date
          invoices
          warranty_period
        }
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;
export const getInventoryStockCustom = /* GraphQL */ `
  query GetInventoryStock($id: ID!) {
    getInventoryStock(id: $id) {
      id
      title
      price_per_item
      stock_count
      configurations
      items {
        asset_tag
        serial_number
        purchased_date
        invoices
        warranty_period
        __typename
      }
      createdAt
      updatedAt
      __typename
    }
  }
`;

export const getCandidateByEmail = /* GraphQL */ `
  query GetcandidateByEmail(
    $email: String!
    $sortDirection: ModelSortDirection
    $filter: ModelCandidateFilterInput
    $limit: Int
    $nextToken: String
  ) {
    getcandidateByEmail(
      email: $email
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        first_name
        last_name
        email
        mobile
        employee_relation_with_candidate
        position {
          id
          open
          hired_date
          description
          base_salary
          department
          deadline
          potential_closeDate
          title
          status
          required_experience
          potential_project
          total_position_budget
          key_responsibility
          qualification
          additional_info
          location
          no_of_vacancy
          team
          job_type
          expected_position_budget
          allocated_position_budget
          createdAt
          updatedAt
          hiringPositionPositionId
          hiringPositionHired_candidateId
          hiringPositionGuildId
          hiringPositionPotential_squadId
          __typename
        }
        reffered_by {
          email
          employee_id
          first_name
          last_name
          mobile
          reward_points
          blood_group
          emergency_contact_num
          emergency_contact_relation
          married
          spouse_full_name
          pan_card
          aadhar_card
          form12BB
          experience_letter
          passport_photo
          address_proof
          resignation_letter
          salary_slip
          salary_slip_1
          salary_slip_2
          personal_email
          york_appointment
          york_agreement
          reffered_by
          address
          birth_date
          anniversary_date
          career_start_date
          york_start_date
          york_end_date
          profile_pic
          profile_pic_requested
          active
          documents
          gender
          facebook_link
          linkedin_link
          twitter_link
          instagram_link
          usual_starting_time
          epf
          uan
          hidden_profile
          sick_leave_balance
          privilege_leave_balance
          createdAt
          updatedAt
          squadEmployeeId
          guildEmployeeId
          employeeTitleId
          employeeReporting_toId
          employeeSquadId
          employeeGuildId
          __typename
        }
        location
        linkedin_profile
        interview {
          items {
            id
            title
            interview_date
            interview_recordings
            taken_by {
              email
              first_name
              last_name
            }
            createdAt
            updatedAt
            candidateInterviewId
            interviewTaken_byId
            status
          }
        }
        confidence
        status
        cover_letter
        exeperience
        notice_period
        portfolio_link
        github_link
        other_link
        s3_resume_key
        education
        past_exeperience
        skills {
          nextToken
          __typename
        }
        candidate_source
        current_ctc
        expected_ctc
        createdAt
        updatedAt
        hiringPositionCandidatesId
        candidateReffered_byId
        candidate_source
        __typename
      }
      nextToken
      __typename
    }
  }
`;

export const listNotificationsByUserCustom = /* GraphQL */ `
  query GetNotificationByUser(
    $ToAccount: String!
    $limit: Int
    $nextToken: String
  ) {
    GetNotificationByUser(
      ToAccount: $ToAccount
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        ToAccount
        To {
          email
          employee_id
          first_name
          last_name
          mobile
          blood_group
          emergency_contact_num
          emergency_contact_relation
          married
          spouse_full_name
          pan_card
          aadhar_card
          form12BB
          passport_photo
          address_proof
          resignation_letter
          salary_slip
          salary_slip_1
          salary_slip_2
          personal_email
          york_appointment
          york_agreement
          reffered_by
          address
          birth_date
          anniversary_date
          career_start_date
          york_start_date
          york_end_date
          profile_pic
          active
          documents
          gender
          facebook_link
          linkedin_link
          twitter_link
          instagram_link
          usual_starting_time
          epf
          uan
          hidden_profile
          createdAt
          updatedAt
          squadEmployeeId
          guildEmployeeId
          employeeTitleId
          employeeReporting_toId

          employeeSquadId
          employeeGuildId
          __typename
        }
        AdminNotification
        Type
        Message
        Metadata
        isRead
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const listEmployeesForShopCoin = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listEmployees(
      email: $email
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        email
        employee_id
        first_name
        last_name
        active
        reward_points
      }
      nextToken
    }
  }
`;

export const listCoinTransactionsForTransactionsOverview = /* GraphQL */ `
  query ListCoinTransactions(
    $filter: ModelCoinTransactionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listCoinTransactions(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        coins
        adjustment_type
        employee {
          email
          reward_points
          first_name
          last_name
        }
        approved_by {
          email
          first_name
          last_name
        }
        transaction_date
        createdAt
        updatedAt
        employeeCoins_transactionsId
        coinTransactionApproved_byId
        balance
        comment
      }
      nextToken
      __typename
    }
  }
`;
export const getDocumentByRelationIDForDocumentUpload = /* GraphQL */ `
  query GetDocumentByRelationID(
    $Relation_ID: String!
    $sortDirection: ModelSortDirection
    $filter: ModelDocumentMasterFilterInput
    $limit: Int
    $nextToken: String
  ) {
    getDocumentByRelationID(
      Relation_ID: $Relation_ID
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        Type
        Relation_ID
        S3_Key
        owner
        ownerDetails {
          email
          employee_id
          first_name
          last_name
        }
        metaData
        createdAt
        updatedAt
      }
      nextToken
      __typename
    }
  }
`;
export const getTimeSheetCustomForNotification = /* GraphQL */ `
  query GetTimeSheet($id: ID!) {
    getTimeSheet(id: $id) {
      id
      start_time
      end_time
      employeeID
      timeSheetProjectId
      projectBucket
      approval_status
      related_notifications
    }
  }
`;

export const listZoomMeetingsCustom = `
query ListZoomMeetings {
  listZoomMeetings {
    items {
      employeeZoomMeetingsHostedId
      meetingid
      s3_key
      start_time
      topic
      updatedAt
      createdAt
      hostEmployeeID
      id
      externalParticipants {
        items {
          id
          createdAt
          join_time
          leave_time
          name
          status
          updatedAt
        }
      }
      internalParticipants {
        items {
          createdAt
          employeeID
          employeeZoomMeetingsParticipatedId
          id
          join_time
          leave_time
          status
          updatedAt
          employee {
            email
            first_name
            last_name
          }
        }
      }
    }
  }
}
`;

export const getZoomMeetingCustom = `
query GetZoomMeeting($id: ID!) {
  getZoomMeeting(id: $id) {
    employeeZoomMeetingsHostedId
    meetingid
    s3_key
    start_time
    topic
    updatedAt
    createdAt
    hostEmployeeID
    id
    externalParticipants {
      items {
        id
        createdAt
        join_time
        leave_time
        name
        status
        updatedAt
      }
    }
    internalParticipants {
      items {
        createdAt
        employeeID
        employeeZoomMeetingsParticipatedId
        id
        join_time
        leave_time
        status
        updatedAt
        employee {
          email
          first_name
          last_name
        }
      }
    }
  }
}`;
export const listCurrentNoticesForNoticeBoard = /* GraphQL */ `
  query ListNoticeBoards(
    $filter: ModelNoticeBoardFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listNoticeBoards(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        expiry
        from {
          email
          first_name
          last_name
        }
        createdAt
        updatedAt
        noticeBoardFromId
        type
        metaData
        reactions
      }
      nextToken
    }
  }
`;
export const listArchivedNoticesForNoticeBoard = /* GraphQL */ `
  query ListNoticeBoards(
    $filter: ModelNoticeBoardFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listNoticeBoards(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        expiry
        from {
          email
          first_name
          last_name
        }
        createdAt
        updatedAt
        noticeBoardFromId
      }
      nextToken
    }
  }
`;

export const listEmployeesForSearchBar = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listEmployees(
      email: $email
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        email
        employee_id
        first_name
        last_name
        allocatedDesk
        country
        squadEmployeeId
        guildEmployeeId
        introduction
        skills {
          items {
            id
            employeeID
            skillID
            createdAt
            updatedAt
          }
          nextToken
        }
        career_start_date
      }
      nextToken
    }
  }
`;

export const listEmployeeCertificatesForManageCertifications = /* GraphQL */ `
  query ListEmployeeCertificates(
    $filter: ModelEmployeeCertificatesFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listEmployeeCertificates(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        S3Key
        title
        provider
        url
        employee {
          email
          employee_id
          first_name
          last_name
        }
        provided_date
        id
        createdAt
        updatedAt
        employeeCertificationId
        type
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const listEmployeeForCertificate = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listEmployees(
      email: $email
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        employee_id
        first_name
        last_name
        active
        reward_points
        profile_pic
        email
      }
      nextToken
    }
  }
`;
export const getEmployeeDataForCustomProfile = /* GraphQL */ `
  query GetEmployee($email: String!) {
    getEmployee(email: $email) {
      email
      employee_id
      account_status
      first_name
      last_name
      country
      hidden_profile
      projectPasswords {
        items {
          id
          employeeID
          projectPasswordsID
          createdAt
          updatedAt
          email
          __typename
        }
        nextToken
        __typename
      }
      title {
        id
        name
        level
        overall_level
        createdAt
        updatedAt
      }
      mobile
      blood_group
      emergency_contact_num
      emergency_contact_relation
      married
      spouse_full_name
      reporting_to {
        email
        employee_id
        first_name
        last_name
      }
      skills {
        items {
          id
          employeeID
          skillID
          createdAt
          updatedAt
        }
        nextToken
      }
      SME {
        items {
          id
          employeeID
          sMEID
          createdAt
          updatedAt
        }
        nextToken
      }
      pan_card
      aadhar_card
      form12BB
      experience_letter
      address
      squad {
        name
        budget
        createdAt
        updatedAt
        squadSquad_managerId
      }
      birth_date
      anniversary_date
      employee_project_allocation {
        items {
          project {
            name
            id
          }
          allocation
          createdAt
          employeeEmployee_project_allocationId
          id
          projectEmployee_project_allocationId
          title
          updatedAt
        }
      }
      passport_photo
      address_proof
      resignation_letter
      salary_slip
      salary_slip_1
      salary_slip_2
      personal_email
      york_appointment
      york_agreement
      reffered_by
      career_start_date
      york_start_date
      york_end_date
      profile_pic
      profile_pic_requested
      active
      documents
      gender
      facebook_link
      linkedin_link
      twitter_link
      instagram_link
      usual_starting_time
      sick_leave_balance
      wfh_balance
      privilege_leave_balance
      epf
      uan
      createdAt
      updatedAt
      squadEmployeeId
      guildEmployeeId
      employeeTitleId
      employeeReporting_toId
      employeeSquadId
      employeeGuildId
      guildEmployeeId
      day_start_time
      day_end_time
      allocatedDesk
      reward_points
      introduction
      isInterviewer
      certification {
        items {
          S3Key
          title
          provider
          url
          provided_date
          id
          type
          createdAt
          updatedAt
          employeeCertificationId
        }
        nextToken
      }
    }
  }
`;
export const listEmployeeBadgesForManageBadge = /* GraphQL */ `
  query ListEmployeeCertificates(
    $filter: ModelEmployeeCertificatesFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listEmployeeCertificates(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        S3Key
        title
        provider
        url
        employee {
          email
          employee_id
          first_name
          last_name
        }
        provided_date
        type
        id
        createdAt
        updatedAt
        employeeCertificationId
        __typename
      }
      nextToken
      __typename
    }
  }
`;
export const listProjectsNamesForSearchBar = /* GraphQL */ `
  query ListProjects(
    $filter: ModelProjectFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listProjects(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        project_buckets {
          name
          isActive
        }
        product_strategist {
          email
          employee_id
          first_name
          last_name
        }
        product_manager {
          email
          employee_id
          first_name
          last_name
        }
        status
      }
      nextToken
    }
  }
`;

export const GetMeetingScheduleByOwnerForMeetingTracker = /* GraphQL */ `
  query GetMeetingScheduleByOwner(
    $owner: String!
    $createdAt: ModelStringKeyConditionInput
    $sortDirection: ModelSortDirection
    $filter: ModelMeetingScheduleFilterInput
    $limit: Int
    $nextToken: String
  ) {
    GetMeetingScheduleByOwner(
      owner: $owner
      createdAt: $createdAt
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        owner
        ownerDetails {
          email
          employee_id
          first_name
          last_name
          __typename
        }
        title
        id
        type
        meetingSchedules_items {
          items {
            id
            with
            withDetails {
              email
              employee_id
              first_name
              last_name
            }
            scheduled_at
            mom
            zoomMeetingID
            meetingScheduleId
            createdAt
            updatedAt
          }
          nextToken
        }
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;

export const listEmployeesForOfficeMap = /* GraphQL */ `
  query ListEmployees(
    $email: String
    $filter: ModelEmployeeFilterInput
    $sortDirection: ModelSortDirection
    $nextToken: String
    $limit: Int
  ) {
    listEmployees(
      email: $email
      filter: $filter
      sortDirection: $sortDirection
      nextToken: $nextToken
      limit: $limit
    ) {
      items {
        email
        employee_id
        first_name
        last_name
        active
        mobile
        profile_pic
        allocatedDesk
      }
      nextToken
    }
  }
`;

export const listCelebrations = /* GraphQL */ `
  query ListCelebrations($limit: Int, $nextToken: String) {
    listCelebrations(limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        note
        employeeEmail
        createdAt
        employee {
          email
          first_name
          last_name
          profile_pic
        }
      }
      nextToken
    }
  }
`;
