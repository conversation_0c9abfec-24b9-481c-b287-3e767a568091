export const createSkill = /* GraphQL */ `
  mutation CreateSkill(
    $input: CreateSkillInput!
    $condition: ModelSkillConditionInput
  ) {
    createSkill(input: $input, condition: $condition) {
      name
    }
  }
`;

export const createSME = /* GraphQL */ `
  mutation CreateSME(
    $input: CreateSMEInput!
    $condition: ModelSMEConditionInput
  ) {
    createSME(input: $input, condition: $condition) {
      name
    }
  }
`;
export const updateEmployee = /* GraphQL */ `
  mutation UpdateEmployee(
    $input: UpdateEmployeeInput!
    $condition: ModelEmployeeConditionInput
  ) {
    updateEmployee(input: $input, condition: $condition) {
      email
    }
  }
`;

export const createEmployeeSME = /* GraphQL */ `
  mutation CreateEmployeeSME(
    $input: CreateEmployeeSMEInput!
    $condition: ModelEmployeeSMEConditionInput
  ) {
    createEmployeeSME(input: $input, condition: $condition) {
      id
    }
  }
`;
export const deleteEmployeeSME = /* GraphQL */ `
  mutation DeleteEmployeeSME(
    $input: DeleteEmployeeSMEInput!
    $condition: ModelEmployeeSMEConditionInput
  ) {
    deleteEmployeeSME(input: $input, condition: $condition) {
      id
    }
  }
`;
export const createEmployeeSkill = /* GraphQL */ `
  mutation CreateEmployeeSkill(
    $input: CreateEmployeeSkillInput!
    $condition: ModelEmployeeSkillConditionInput
  ) {
    createEmployeeSkill(input: $input, condition: $condition) {
      id
    }
  }
`;
export const deleteEmployeeSkill = /* GraphQL */ `
  mutation DeleteEmployeeSkill(
    $input: DeleteEmployeeSkillInput!
    $condition: ModelEmployeeSkillConditionInput
  ) {
    deleteEmployeeSkill(input: $input, condition: $condition) {
      id
    }
  }
`;
